{"node": {"40e74f27756bb59ec5044c39bd76f8b541e1652b84": {"workers": {"app/page": {"moduleId": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%2240e74f27756bb59ec5044c39bd76f8b541e1652b84%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}, "app/dashboard/page": {"moduleId": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%2240e74f27756bb59ec5044c39bd76f8b541e1652b84%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}, "app/admin/page": {"moduleId": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%2240e74f27756bb59ec5044c39bd76f8b541e1652b84%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}, "app/_not-found/page": {"moduleId": "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Cai%5C%5Cflows%5C%5Ctrading-mode-suggestion.ts%22%2C%5B%7B%22id%22%3A%2240e74f27756bb59ec5044c39bd76f8b541e1652b84%22%2C%22exportedName%22%3A%22suggestTradingMode%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}}, "layer": {"app/page": "action-browser", "app/dashboard/page": "action-browser", "app/admin/page": "action-browser", "app/_not-found/page": "action-browser"}}}, "edge": {}, "encryptionKey": "gTQX4WA+3hJp4rIMkfnNs79BJXa3d5v30RWQtSwILRY="}