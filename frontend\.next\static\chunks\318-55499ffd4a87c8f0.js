"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[318],{5731:(t,e,o)=>{o.d(e,{ZQ:()=>a,oc:()=>c,sessionApi:()=>i});let n="http://localhost:5000";async function r(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o="".concat(n).concat(t),r=localStorage.getItem("plutoAuthToken"),a=o.includes("/sessions")||o.includes("/trading")||o.includes("/admin");if(a&&!r)throw console.warn("\uD83D\uDD10 Blocked API call to ".concat(o," - no authentication token available")),Error("Authentication required - no token available");console.log("\uD83D\uDD10 Auth token check:",{hasToken:!!r,tokenLength:r?r.length:0,tokenPreview:r?"".concat(r.substring(0,20),"..."):"No token",isAuthenticatedEndpoint:a});let c={"Content-Type":"application/json",...r?{Authorization:"Bearer ".concat(r)}:{},...e.headers};try{let t;let n=new AbortController,r=setTimeout(()=>n.abort(),1e4),a=await fetch(o,{...e,headers:c,signal:n.signal}).finally(()=>clearTimeout(r));if(401===a.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),window.location.href="/login",Error("Authentication expired. Please login again.");let i="",s=a.headers.get("content-type");if(s&&s.includes("application/json")){i=await a.text();try{t=JSON.parse(i)}catch(e){t={message:i}}}else{i=await a.text();try{t=JSON.parse(i)}catch(e){t={message:i}}}if(!a.ok){let o={status:a.status,statusText:a.statusText,url:a.url,method:e.method||"GET",responseData:t,responseText:i.substring(0,500)};throw 401!==a.status&&422!==a.status&&t&&(t.error||t.message||Object.keys(t).length>0)?console.error("\uD83D\uDEA8 API Error Details:",o):console.warn("⚠️ API ".concat(a.status," Error:"),a.url),Error(t.error||t.message||"API error: ".concat(a.status))}return t}catch(t){if(t instanceof TypeError&&t.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",t),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===t.name)throw console.error("Request timeout:",t),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",t),t}}console.log("API Base URL:",n);let a={login:async(t,e)=>{try{let o=await s(async()=>await r("/auth/login",{method:"POST",body:JSON.stringify({username:t,password:e})}));if(o&&o.access_token)return localStorage.setItem("plutoAuthToken",o.access_token),localStorage.setItem("plutoAuth","true"),o.user&&localStorage.setItem("plutoUser",JSON.stringify(o.user)),!0;return!1}catch(t){return console.error("Login API error:",t),!1}},register:async(t,e,o)=>s(async()=>await r("/auth/register",{method:"POST",body:JSON.stringify({username:t,password:e,email:o})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},c={getConfig:async t=>r(t?"/trading/config/".concat(t):"/trading/config"),saveConfig:async t=>r("/trading/config",{method:"POST",body:JSON.stringify(t)}),updateConfig:async(t,e)=>r("/trading/config/".concat(t),{method:"PUT",body:JSON.stringify(e)}),startBot:async t=>r("/trading/bot/start/".concat(t),{method:"POST"}),stopBot:async t=>r("/trading/bot/stop/".concat(t),{method:"POST"}),getBotStatus:async t=>r("/trading/bot/status/".concat(t)),getTradeHistory:async t=>r("/trading/history".concat(t?"?configId=".concat(t):"")),getBalances:async()=>r("/trading/balances"),getMarketPrice:async t=>r("/trading/market-data/".concat(t)),getTradingPairs:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return r("/trading/exchange/trading-pairs?exchange=".concat(t))},getCryptocurrencies:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return r("/trading/exchange/cryptocurrencies?exchange=".concat(t))}},i={getAllSessions:async function(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return r("/sessions/?include_inactive=".concat(t))},createSession:async t=>r("/sessions/",{method:"POST",body:JSON.stringify(t)}),getSession:async t=>r("/sessions/".concat(t)),updateSession:async(t,e)=>r("/sessions/".concat(t),{method:"PUT",body:JSON.stringify(e)}),deleteSession:async t=>r("/sessions/".concat(t),{method:"DELETE"}),activateSession:async t=>r("/sessions/".concat(t,"/activate"),{method:"POST"}),getSessionHistory:async t=>r("/sessions/".concat(t,"/history")),getActiveSession:async()=>r("/sessions/active")},s=async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,o=0,n=async()=>{try{return await t()}catch(t){if((t instanceof TypeError&&t.message.includes("Failed to fetch")||"AbortError"===t.name)&&o<e){let t=500*Math.pow(2,o);return console.log("Retrying after ".concat(t,"ms (attempt ").concat(o+1,"/").concat(e,")...")),o++,await new Promise(e=>setTimeout(e,t)),n()}throw t}};return n()}},7213:(t,e,o)=>{o.d(e,{TradingProvider:()=>I,U:()=>k});var n=o(5155),r=o(2115),a=o(9348),c=o(9737),i=o(7481),s=o(5731),l=o(4553);class u{static getInstance(){return u.instance||(u.instance=new u),u.instance}setupEventListeners(){window.addEventListener("online",this.handleOnline.bind(this)),window.addEventListener("offline",this.handleOffline.bind(this)),document.addEventListener("visibilitychange",()=>{document.hidden||this.checkConnection()})}handleOnline(){console.log("\uD83C\uDF10 Network: Back online"),this.isOnline=!0,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.notifyListeners(!0,!this.hasInitialized)}handleOffline(){console.log("\uD83C\uDF10 Network: Gone offline"),this.isOnline=!1,this.notifyListeners(!1,!this.hasInitialized)}async checkConnection(){let t=navigator.onLine;return t!==this.isOnline&&(this.isOnline=t,this.notifyListeners(t,!this.hasInitialized),t&&(this.lastOnlineTime=Date.now(),this.reconnectAttempts=0)),t}startPeriodicCheck(){let t=setInterval(()=>{this.checkConnection()},6e4);this.periodicInterval=t}cleanup(){this.periodicInterval&&clearInterval(this.periodicInterval),this.listeners.clear()}notifyListeners(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.listeners.forEach(o=>{try{o(t,e)}catch(t){console.error("Error in network status listener:",t)}})}addListener(t){return this.listeners.add(t),()=>{this.listeners.delete(t)}}getStatus(){return{isOnline:this.isOnline,lastOnlineTime:this.lastOnlineTime,reconnectAttempts:this.reconnectAttempts}}async forceCheck(){return await this.checkConnection()}async attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return console.log("\uD83C\uDF10 Network: Max reconnect attempts reached"),!1;this.reconnectAttempts++;let t=Math.min(this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1),3e4);console.log("\uD83C\uDF10 Network: Attempting reconnect ".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts," in ").concat(t,"ms")),await new Promise(e=>setTimeout(e,t));let e=await this.checkConnection();return!e&&this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>this.attemptReconnect(),1e3),e}constructor(){this.isOnline=navigator.onLine,this.listeners=new Set,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=5e3,this.hasInitialized=!1,this.setupEventListeners(),this.startPeriodicCheck(),setTimeout(()=>{this.hasInitialized=!0},1e3)}}class p{static getInstance(){return p.instance||(p.instance=new p),p.instance}setupNetworkListener(){this.networkMonitor.addListener(t=>{t&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on network reconnection"),this.saveFunction(),this.lastSaveTime=Date.now())})}setupBeforeUnloadListener(){window.addEventListener("beforeunload",()=>{this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving before page unload"),this.saveFunction())}),document.addEventListener("visibilitychange",()=>{document.hidden&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on tab switch"),this.saveFunction(),this.lastSaveTime=Date.now())})}enable(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e4;this.saveFunction=t,this.intervalMs=e,this.isEnabled=!0,this.stop(),this.saveInterval=setInterval(()=>{this.isEnabled&&this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Periodic save"),this.saveFunction(),this.lastSaveTime=Date.now())},this.intervalMs),console.log("\uD83D\uDCBE Auto-save: Enabled with ".concat(e,"ms interval"))}disable(){this.isEnabled=!1,this.stop(),console.log("\uD83D\uDCBE Auto-save: Disabled")}stop(){this.saveInterval&&(clearInterval(this.saveInterval),this.saveInterval=null)}saveNow(){this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Manual save triggered"),this.saveFunction(),this.lastSaveTime=Date.now())}getStatus(){return{isEnabled:this.isEnabled,lastSaveTime:this.lastSaveTime,intervalMs:this.intervalMs,isOnline:this.networkMonitor.getStatus().isOnline}}constructor(){this.saveInterval=null,this.saveFunction=null,this.intervalMs=3e4,this.isEnabled=!0,this.lastSaveTime=0,this.networkMonitor=u.getInstance(),this.setupNetworkListener(),this.setupBeforeUnloadListener()}}class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}startMonitoring(){console.log("\uD83D\uDCCA Memory monitoring disabled to prevent frequent notifications")}checkMemoryUsage(){if("memory"in performance){let t=performance.memory,e=t.usedJSHeapSize;this.notifyListeners(t),e>this.criticalThreshold?(console.warn("\uD83E\uDDE0 Memory: Critical memory usage detected:",{used:"".concat((e/1024/1024).toFixed(2),"MB"),total:"".concat((t.totalJSHeapSize/1024/1024).toFixed(2),"MB"),limit:"".concat((t.jsHeapSizeLimit/1024/1024).toFixed(2),"MB")}),"gc"in window&&window.gc()):e>this.warningThreshold&&console.log("\uD83E\uDDE0 Memory: High memory usage:",{used:"".concat((e/1024/1024).toFixed(2),"MB"),total:"".concat((t.totalJSHeapSize/1024/1024).toFixed(2),"MB")})}}notifyListeners(t){this.listeners.forEach(e=>{try{e(t)}catch(t){console.error("Error in memory monitor listener:",t)}})}addListener(t){return this.listeners.add(t),()=>this.listeners.delete(t)}getMemoryUsage(){return"memory"in performance?performance.memory:null}stop(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}constructor(){this.checkInterval=null,this.warningThreshold=262144e3,this.criticalThreshold=0x19000000,this.listeners=new Set,this.startMonitoring()}}let g=t=>t.crypto1&&t.crypto2?D(t):0,y=async t=>{try{if(!t.crypto1||!t.crypto2)return 0;let o="".concat(t.crypto1).concat(t.crypto2).toUpperCase();try{let e=await fetch("https://api.binance.com/api/v3/ticker/price?symbol=".concat(o),{method:"GET",headers:{Accept:"application/json"}});if(e.ok){let o=await e.json(),n=parseFloat(o.price);if(n>0)return console.log("✅ Real-time price from Binance: ".concat(t.crypto1,"/").concat(t.crypto2," = ").concat(n.toLocaleString())),n}else console.warn("Binance API response not OK: ".concat(e.status," ").concat(e.statusText))}catch(t){console.warn("Binance API failed, trying alternative...",t)}try{let e=await fetch("https://api.binance.com/api/v3/ticker/24hr?symbol=".concat(o));if(e.ok){let o=await e.json(),n=parseFloat(o.lastPrice);if(n>0)return console.log("✅ Real-time price from Binance (24hr): ".concat(t.crypto1,"/").concat(t.crypto2," = ").concat(n.toLocaleString())),n}}catch(t){console.warn("Binance 24hr API also failed...",t)}try{let o=S(t.crypto1),n=S(t.crypto2);if(o&&n){let r=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(o,"&vs_currencies=").concat(n));if(r.ok){var e;let a=await r.json(),c=null===(e=a[o])||void 0===e?void 0:e[n];if(c>0)return console.log("✅ Price fetched from CoinGecko: ".concat(t.crypto1,"/").concat(t.crypto2," = ").concat(c)),c}}}catch(t){console.warn("CoinGecko API failed, using mock price...",t)}try{let e=await m(t.crypto1),o=await m(t.crypto2),n=e/o;return console.log("⚠️ Using calculated price from real-time USD values: ".concat(t.crypto1,"/").concat(t.crypto2," = ").concat(n.toLocaleString())),n}catch(t){console.warn("Real-time USD price calculation failed, using static fallback...",t)}let n=D(t);return console.log("⚠️ Using static mock price: ".concat(t.crypto1,"/").concat(t.crypto2," = ").concat(n.toLocaleString())),n}catch(e){return console.error("Error fetching market price:",e),D(t)}},S=t=>({BTC:"bitcoin",ETH:"ethereum",SOL:"solana",ADA:"cardano",DOT:"polkadot",MATIC:"matic-network",AVAX:"avalanche-2",LINK:"chainlink",UNI:"uniswap",USDT:"tether",USDC:"usd-coin",BUSD:"binance-usd",DAI:"dai"})[t.toUpperCase()]||null,f={},h=0,m=async t=>{let e=Date.now(),o=t.toUpperCase();if(f[o]&&e-h<6e4)return f[o];try{let r=S(t);if(r){let a=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(r,"&vs_currencies=usd"));if(a.ok){var n;let c=await a.json(),i=null===(n=c[r])||void 0===n?void 0:n.usd;if(i&&i>0)return f[o]=i,h=e,console.log("\uD83D\uDCCA Real-time price fetched for ".concat(t,": $").concat(i)),i}}}catch(e){console.warn("⚠️ Failed to fetch real-time price for ".concat(t,", using fallback:"),e)}return b(t)},b=t=>{let e=t.toUpperCase();return f[e]?f[e]:C(t)},C=t=>({BTC:106e3,ETH:2500,SOL:180,ADA:.85,DOGE:.32,LINK:22,MATIC:.42,DOT:6.5,AVAX:38,SHIB:22e-6,XRP:2.1,LTC:95,BCH:420,UNI:15,AAVE:180,MKR:1800,SNX:3.5,COMP:85,YFI:8500,SUSHI:2.1,"1INCH":.65,CRV:.85,UMA:3.2,ATOM:12,NEAR:6.5,ALGO:.35,ICP:14,HBAR:.28,APT:12.5,TON:5.8,FTM:.95,ONE:.025,FIL:8.5,TRX:.25,ETC:35,VET:.055,QNT:125,LDO:2.8,CRO:.18,LUNC:15e-5,MANA:.85,SAND:.75,AXS:8.5,ENJ:.45,CHZ:.12,THETA:2.1,FLOW:1.2,XTZ:1.8,EOS:1.1,GRT:.28,BAT:.35,ZEC:45,DASH:35,LRC:.45,ZRX:.65,KNC:.85,REN:.15,BAND:2.5,STORJ:.85,NMR:25,ANT:8.5,BNT:.95,MLN:35,REP:15,IOTX:.065,ZIL:.045,ICX:.35,QTUM:4.5,ONT:.45,WAVES:3.2,LSK:1.8,NANO:1.5,SC:.008,DGB:.025,RVN:.035,BTT:15e-7,WIN:15e-5,HOT:.0035,DENT:.0018,NPXS:85e-5,FUN:.0085,CELR:.025,USDT:1,USDC:1,FDUSD:1,BUSD:1,DAI:1})[t.toUpperCase()]||100,D=t=>{let e=b(t.crypto1),o=b(t.crypto2),n=e/o*(1+(Math.random()-.5)*.02);return console.log("\uD83D\uDCCA Fallback price calculation: ".concat(t.crypto1," ($").concat(e,") / ").concat(t.crypto2," ($").concat(o,") = ").concat(n.toFixed(6))),n},v={tradingMode:"SimpleSpot",crypto1:"",crypto2:"",baseBid:100,multiplier:1.005,numDigits:4,slippagePercent:.2,incomeSplitCrypto1Percent:50,incomeSplitCrypto2Percent:50,preferredStablecoin:a.Ql[0]},w={config:v,targetPriceRows:[],orderHistory:[],appSettings:a.Oh,currentMarketPrice:g(v),botSystemStatus:"Stopped",crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,backendStatus:"unknown"},E=new Map,T="pluto_trading_state",P=t=>{try{{let e={config:t.config,targetPriceRows:t.targetPriceRows,orderHistory:t.orderHistory,appSettings:t.appSettings,currentMarketPrice:t.currentMarketPrice,crypto1Balance:t.crypto1Balance,crypto2Balance:t.crypto2Balance,stablecoinBalance:t.stablecoinBalance,botSystemStatus:t.botSystemStatus,timestamp:Date.now()};localStorage.setItem(T,JSON.stringify(e))}}catch(t){console.error("Failed to save state to localStorage:",t)}},A=()=>{try{{let t=localStorage.getItem(T);if(t){let e=JSON.parse(t);if(e.timestamp&&Date.now()-e.timestamp<864e5)return e}}}catch(t){console.error("Failed to load state from localStorage:",t)}return null},R=(t,e)=>{switch(e.type){case"SET_CONFIG":let o={...t.config,...e.payload};if(e.payload.crypto1||e.payload.crypto2)return{...t,config:o,currentMarketPrice:g(o)};return{...t,config:o};case"SET_TARGET_PRICE_ROWS":return{...t,targetPriceRows:e.payload.sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}))};case"ADD_TARGET_PRICE_ROW":{let o=[...t.targetPriceRows,e.payload].sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:o}}case"UPDATE_TARGET_PRICE_ROW":{let o=t.targetPriceRows.map(t=>t.id===e.payload.id?e.payload:t).sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:o}}case"REMOVE_TARGET_PRICE_ROW":{let o=t.targetPriceRows.filter(t=>t.id!==e.payload).sort((t,e)=>t.targetPrice-e.targetPrice).map((t,e)=>({...t,counter:e+1}));return{...t,targetPriceRows:o}}case"ADD_ORDER_HISTORY_ENTRY":return setTimeout(()=>{let o=l.SessionManager.getInstance(),n=o.getCurrentSessionId();if(n&&o.loadSession(n)){let r=[e.payload,...t.orderHistory];o.saveSession(n,t.config,t.targetPriceRows,r,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus).then(()=>{console.log("\uD83D\uDCBE Immediate auto-save triggered by trade")}).catch(t=>{console.error("❌ Failed immediate auto-save after trade:",t)})}},100),{...t,orderHistory:[e.payload,...t.orderHistory]};case"CLEAR_ORDER_HISTORY":return{...t,orderHistory:[]};case"SET_APP_SETTINGS":return{...t,appSettings:{...t.appSettings,...e.payload}};case"SET_MARKET_PRICE":return{...t,currentMarketPrice:e.payload};case"UPDATE_BALANCES":return{...t,crypto1Balance:void 0!==e.payload.crypto1?e.payload.crypto1:t.crypto1Balance,crypto2Balance:void 0!==e.payload.crypto2?e.payload.crypto2:t.crypto2Balance,stablecoinBalance:void 0!==e.payload.stablecoin?e.payload.stablecoin:t.stablecoinBalance};case"SET_BALANCES":return{...t,crypto1Balance:e.payload.crypto1,crypto2Balance:e.payload.crypto2,stablecoinBalance:void 0!==e.payload.stablecoin?e.payload.stablecoin:t.stablecoinBalance};case"UPDATE_STABLECOIN_BALANCE":return{...t,stablecoinBalance:e.payload};case"RESET_SESSION":let n={...t.config};return{...w,config:n,appSettings:{...t.appSettings},currentMarketPrice:g(n),crypto1Balance:t.crypto1Balance,crypto2Balance:t.crypto2Balance,stablecoinBalance:t.stablecoinBalance};case"SET_BACKEND_STATUS":return{...t,backendStatus:e.payload};case"SYSTEM_START_BOT_INITIATE":if(!t.config.crypto1||!t.config.crypto2)return console.warn("⚠️ Cannot start bot: Both crypto1 and crypto2 must be selected"),t;if(!t.targetPriceRows||0===t.targetPriceRows.length)return console.warn("⚠️ Cannot start bot: Target prices must be set"),t;return{...t,botSystemStatus:"WarmingUp"};case"SYSTEM_COMPLETE_WARMUP":return{...t,botSystemStatus:"Running"};case"SYSTEM_STOP_BOT":return{...t,botSystemStatus:"Stopped"};case"SYSTEM_RESET_BOT":return E.clear(),l.SessionManager.getInstance().clearCurrentSession(),{...t,botSystemStatus:"Stopped",targetPriceRows:[],orderHistory:[]};case"SET_TARGET_PRICE_ROWS":return{...t,targetPriceRows:e.payload};case"RESET_FOR_NEW_CRYPTO":return{...w,config:t.config,backendStatus:t.backendStatus,botSystemStatus:"Stopped",currentMarketPrice:0,crypto1Balance:t.crypto1Balance,crypto2Balance:t.crypto2Balance,stablecoinBalance:t.stablecoinBalance};default:return t}},B=(0,r.createContext)(void 0),I=t=>{let{children:e}=t,[o,a]=(0,r.useReducer)(R,(()=>{if("true"===new URLSearchParams(window.location.search).get("newSession"))return console.log("\uD83C\uDD95 New session requested - starting with completely fresh state"),w;let t=l.SessionManager.getInstance(),e=t.getCurrentSessionId();if(console.log("\uD83D\uDD0D Session restoration check:",{currentSessionId:e,hasSessionId:!!e,allSessions:t.getAllSessions().length}),!e){let e=localStorage.getItem("pluto_last_app_close"),o=Date.now();if(!e||o-parseInt(e)>3e5)return console.log("\uD83C\uDD95 App restart detected - starting with fresh state (running sessions already cleaned up)"),w;{let e=t.getAllSessions().filter(t=>t.isActive).sort((t,e)=>e.lastModified-t.lastModified)[0];if(!e)return console.log("\uD83C\uDD95 No active sessions found - starting with fresh state"),w;console.log("\uD83D\uDD04 Page refresh detected - restoring recent active session:",e.id),t.setCurrentSession(e.id)}}let o=t.getCurrentSessionId();if(!o)return console.log("\uD83C\uDD95 No session ID available after restoration attempt - starting fresh"),w;let n=t.loadSession(o);if(n){console.log("\uD83D\uDD04 Loading session data:",{sessionId:o,isActive:n.isActive,targetPriceRows:n.targetPriceRows.length,orderHistory:n.orderHistory.length,config:n.config,balances:{crypto1:n.crypto1Balance,crypto2:n.crypto2Balance,stablecoin:n.stablecoinBalance}});let t={...v,...n.config,baseBid:n.config.baseBid||v.baseBid,multiplier:n.config.multiplier||v.multiplier,numDigits:n.config.numDigits||v.numDigits,slippagePercent:n.config.slippagePercent||v.slippagePercent,incomeSplitCrypto1Percent:n.config.incomeSplitCrypto1Percent||v.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:n.config.incomeSplitCrypto2Percent||v.incomeSplitCrypto2Percent},e=localStorage.getItem("pluto_last_app_close"),r=Date.now(),a=!e||r-parseInt(e)>3e5,c=n.isActive&&n.targetPriceRows&&n.targetPriceRows.length>0&&!a;return console.log("\uD83D\uDD04 Session restoration details:",{sessionId:o,isActive:n.isActive,hasTargetPrices:n.targetPriceRows.length>0,isAppRestart:a,shouldBotBeRunning:c,targetPriceCount:n.targetPriceRows.length,orderHistoryCount:n.orderHistory.length,restorationReason:a?"App restart - bot will be stopped":"Page refresh - bot state preserved",targetPriceRows:n.targetPriceRows.map(t=>({id:t.id,counter:t.counter,targetPrice:t.targetPrice,status:t.status}))}),{...w,config:t,targetPriceRows:n.targetPriceRows,orderHistory:n.orderHistory,currentMarketPrice:n.currentMarketPrice,crypto1Balance:n.crypto1Balance,crypto2Balance:n.crypto2Balance,stablecoinBalance:n.stablecoinBalance,botSystemStatus:c?"Running":"Stopped"}}console.warn("⚠️ Session ID exists but session data not found, clearing invalid session ID:",o),t.clearCurrentSession();let r=A();return r?{...w,...r}:w})()),{toast:g}=(0,i.dj)();(0,r.useEffect)(()=>{if("true"===new URLSearchParams(window.location.search).get("newSession")){let t=window.location.pathname;window.history.replaceState({},"",t)}},[]);let S=(0,r.useRef)(null),f=(0,r.useCallback)(async()=>{try{let t;if(!o.config.crypto1||!o.config.crypto2){a({type:"SET_MARKET_PRICE",payload:0});return}if("StablecoinSwap"===o.config.tradingMode)try{let e=await m(o.config.crypto1),n=await m(o.config.crypto2);t=e/n,console.log("\uD83D\uDCCA StablecoinSwap Market Price Calculation (Real-time):\n            - ".concat(o.config.crypto1," USD Price: $").concat(e.toLocaleString(),"\n            - ").concat(o.config.crypto2," USD Price: $").concat(n.toLocaleString(),"\n            - Market Price (").concat(o.config.crypto1,"/").concat(o.config.crypto2,"): ").concat(t.toFixed(6),"\n            - Calculation: ").concat(e," \xf7 ").concat(n," = ").concat(t.toFixed(6)))}catch(r){let e=b(o.config.crypto1),n=b(o.config.crypto2);t=e/n,console.log("\uD83D\uDCCA StablecoinSwap Market Price Calculation (Fallback):\n            - ".concat(o.config.crypto1," USD Price: $").concat(e.toLocaleString(),"\n            - ").concat(o.config.crypto2," USD Price: $").concat(n.toLocaleString(),"\n            - Market Price (").concat(o.config.crypto1,"/").concat(o.config.crypto2,"): ").concat(t.toFixed(6)))}else t=await y(o.config);a({type:"SET_MARKET_PRICE",payload:t})}catch(t){console.error("Failed to fetch market price:",t),T("Price API","Failed to fetch market price: ".concat(t)).catch(console.error)}},[o.config,a]);(0,r.useEffect)(()=>{f();let t=setInterval(async()=>{if(u.getInstance().getStatus().isOnline)try{await f()}catch(t){console.warn("Failed to update market price:",t)}},2e3),e=setInterval(async()=>{if(u.getInstance().getStatus().isOnline&&"StablecoinSwap"===o.config.tradingMode)try{await m(o.config.crypto1),await m(o.config.crypto2),console.log("\uD83D\uDCCA Real-time USD price cache updated")}catch(t){console.warn("⚠️ Failed to update USD price cache:",t)}},12e4);return()=>{clearInterval(t),clearInterval(e)}},[f,a,o.config.crypto1,o.config.crypto2,o.config.tradingMode]),(0,r.useEffect)(()=>{{S.current=new Audio;let t=()=>{S.current&&(S.current.volume=0,S.current.play().then(()=>{S.current&&(S.current.volume=1),console.log("\uD83D\uDD0A Audio context enabled after user interaction")}).catch(()=>{})),document.removeEventListener("click",t),document.removeEventListener("keydown",t)};return document.addEventListener("click",t),document.addEventListener("keydown",t),()=>{document.removeEventListener("click",t),document.removeEventListener("keydown",t)}}},[]);let h=(0,r.useCallback)(t=>{let e=l.SessionManager.getInstance(),n=e.getCurrentSessionId(),r=o.appSettings;if(n){let t=e.loadSession(n);t&&t.alarmSettings&&(r=t.alarmSettings)}if(r.soundAlertsEnabled&&S.current){let e;if("soundOrderExecution"===t&&r.alertOnOrderExecution?e=r.soundOrderExecution:"soundError"===t&&r.alertOnError&&(e=r.soundError),e)try{S.current.pause(),S.current.currentTime=0,S.current.src=e,S.current.load(),S.current.onerror=t=>{console.warn("⚠️ Audio loading failed for ".concat(e,":"),t),"/sounds/chime2.wav"!==e&&S.current&&(S.current.src="/sounds/chime2.wav",S.current.load())};let t=S.current.play();void 0!==t&&t.then(()=>{setTimeout(()=>{S.current&&!S.current.paused&&(S.current.pause(),S.current.currentTime=0)},2e3)}).catch(t=>{let e=t.message||String(t);e.includes("user didn't interact")||e.includes("autoplay")?console.log("\uD83D\uDD07 Audio autoplay blocked by browser - user interaction required"):e.includes("interrupted")||e.includes("supported source")||console.warn("⚠️ Audio playback failed:",e)})}catch(t){console.warn("⚠️ Audio setup failed for ".concat(e,":"),t)}}},[o.appSettings]),C=(0,r.useCallback)(async function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let o=localStorage.getItem("telegram_bot_token"),n=localStorage.getItem("telegram_chat_id");if(!o||!n){console.log("Telegram not configured - skipping notification");return}let r=await fetch("https://api.telegram.org/bot".concat(o,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:n,text:t,parse_mode:"HTML"})});r.ok?console.log("✅ Telegram notification sent successfully"):(console.error("Failed to send Telegram notification:",r.statusText),e||await D("Telegram API Error","Failed to send notification: ".concat(r.statusText)))}catch(t){console.error("Error sending Telegram notification:",t),e||await D("Network Disconnection","Failed to connect to Telegram API: ".concat(t))}},[]),D=(0,r.useCallback)(async(t,e)=>{let n=new Date().toLocaleString(),r="⚠️ <b>ERROR ALERT</b>\n\n"+"\uD83D\uDD34 <b>Type:</b> ".concat(t,"\n")+"\uD83D\uDCDD <b>Message:</b> ".concat(e,"\n")+"⏰ <b>Time:</b> ".concat(n,"\n")+"\uD83E\uDD16 <b>Bot:</b> ".concat(o.config.crypto1,"/").concat(o.config.crypto2," ").concat(o.config.tradingMode);await C(r,!0)},[o.config,C]),E=(0,r.useCallback)(async(t,e,n)=>{let r=new Date().toLocaleString(),a="\uD83D\uDCB0 <b>LOW BALANCE ALERT</b>\n\n"+"\uD83E\uDE99 <b>Currency:</b> ".concat(t,"\n")+"\uD83D\uDCCA <b>Current Balance:</b> ".concat(e.toFixed(6)," ").concat(t,"\n")+"⚡ <b>Required Amount:</b> ".concat(n.toFixed(6)," ").concat(t,"\n")+"\uD83D\uDCC9 <b>Shortage:</b> ".concat((n-e).toFixed(6)," ").concat(t,"\n")+"⏰ <b>Time:</b> ".concat(r,"\n")+"\uD83E\uDD16 <b>Bot:</b> ".concat(o.config.crypto1,"/").concat(o.config.crypto2," ").concat(o.config.tradingMode);await C(a)},[o.config,C]),T=(0,r.useCallback)(async(t,e)=>{let n=new Date().toLocaleString(),r="\uD83D\uDD0C <b>API ERROR ALERT</b>\n\n"+"\uD83C\uDF10 <b>API:</b> ".concat(t,"\n")+"❌ <b>Error:</b> ".concat(e,"\n")+"⏰ <b>Time:</b> ".concat(n,"\n")+"\uD83E\uDD16 <b>Bot:</b> ".concat(o.config.crypto1,"/").concat(o.config.crypto2," ").concat(o.config.tradingMode);await C(r)},[o.config,C]);(0,r.useEffect)(()=>{},[o.config.crypto1,o.config.crypto2]);let I=(0,r.useCallback)(async()=>{try{let t=l.SessionManager.getInstance(),e=t.getCurrentSessionId();if(!e){if(o.config.crypto1&&o.config.crypto2&&("Running"===o.botSystemStatus||o.targetPriceRows.length>0||o.orderHistory.length>0)){let e={crypto1:o.crypto1Balance,crypto2:o.crypto2Balance,stablecoin:o.stablecoinBalance},n=await t.createNewSessionWithAutoName(o.config,void 0,e);return t.setCurrentSession(n),await t.saveSession(n,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,"Running"===o.botSystemStatus),!0}return console.log("⚠️ No session to save - bot not running and no meaningful data"),!1}return await t.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,"Running"===o.botSystemStatus)}catch(t){return console.error("Failed to save current session:",t),!1}},[o]),k=(0,r.useCallback)(t=>{if(!t||!Array.isArray(t))return;let e=[...t].filter(t=>!isNaN(t)&&t>0).sort((t,e)=>t-e).map((t,e)=>{let n=o.targetPriceRows.find(e=>e.targetPrice===t);return n?{...n,counter:e+1}:{id:(0,c.A)(),counter:e+1,status:"Free",orderLevel:0,valueLevel:o.config.baseBid,targetPrice:t}});a({type:"SET_TARGET_PRICE_ROWS",payload:e}),setTimeout(async()=>{let t=l.SessionManager.getInstance(),n=t.getCurrentSessionId();n&&(await t.saveSession(n,o.config,e,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,"Running"===o.botSystemStatus),console.log("✅ Session saved immediately after setting target prices"))},100),setTimeout(async()=>{try{await I(),console.log("✅ Session auto-saved after setting target prices")}catch(t){console.error("❌ Failed to auto-save session after setting target prices:",t)}},100)},[o.targetPriceRows,o.config.baseBid,a,I]);(0,r.useEffect)(()=>{let t=u.getInstance().getStatus().isOnline;if("Running"!==o.botSystemStatus||0===o.targetPriceRows.length||o.currentMarketPrice<=0||!t){t||"Running"!==o.botSystemStatus||console.log("\uD83D\uDD34 Trading paused - network offline");return}let{config:e,currentMarketPrice:n,targetPriceRows:r,crypto1Balance:i,crypto2Balance:s}=o,l=[...r].sort((t,e)=>t.targetPrice-e.targetPrice),p=i,d=s,y=0;console.log("\uD83D\uDE80 CONTINUOUS TRADING: Price $".concat(n.toFixed(2)," | Targets: ").concat(l.length," | Balance: $").concat(d," ").concat(e.crypto2));let S=l.filter(t=>Math.abs(n-t.targetPrice)/n*100<=e.slippagePercent);S.length>0&&console.log("\uD83C\uDFAF TARGETS IN RANGE (\xb1".concat(e.slippagePercent,"%):"),S.map(t=>"Counter ".concat(t.counter," (").concat(t.status,")")));for(let t=0;t<l.length;t++){let r=l[t];if(Math.abs(n-r.targetPrice)/n*100<=e.slippagePercent){if("SimpleSpot"===e.tradingMode){if("Free"===r.status){let t=r.valueLevel;if(d>=t){let o=t/n;a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...r,status:"Full",orderLevel:r.orderLevel+1,valueLevel:e.baseBid*Math.pow(e.multiplier,r.orderLevel+1),crypto1AmountHeld:o,originalCostCrypto2:t,crypto1Var:o,crypto2Var:-t}}),a({type:"UPDATE_BALANCES",payload:{crypto1:p+o,crypto2:d-t}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto1,"/").concat(e.crypto2),crypto1:e.crypto1,orderType:"BUY",amountCrypto1:o,avgPrice:n,valueCrypto2:t,price1:n,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.crypto2||""}}),console.log("✅ BUY: Counter ".concat(r.counter," bought ").concat(o.toFixed(6)," ").concat(e.crypto1," at $").concat(n.toFixed(2))),g({title:"BUY Executed",description:"Counter ".concat(r.counter,": ").concat(o.toFixed(6)," ").concat(e.crypto1),duration:2e3}),h("soundOrderExecution"),C("\uD83D\uDFE2 <b>BUY EXECUTED</b>\n"+"\uD83D\uDCCA Counter: ".concat(r.counter,"\n")+"\uD83D\uDCB0 Amount: ".concat(o.toFixed(6)," ").concat(e.crypto1,"\n")+"\uD83D\uDCB5 Price: $".concat(n.toFixed(2),"\n")+"\uD83D\uDCB8 Cost: $".concat(t.toFixed(2)," ").concat(e.crypto2,"\n")+"\uD83D\uDCC8 Mode: Simple Spot"),y++,d-=t,p+=o}else console.log("❌ Insufficient ".concat(e.crypto2," balance: ").concat(d.toFixed(6)," < ").concat(t.toFixed(6))),E(e.crypto2,d,t).catch(console.error)}let t=r.counter,o=l.find(e=>e.counter===t-1);if(o&&"Full"===o.status&&o.crypto1AmountHeld&&o.originalCostCrypto2){let r=o.crypto1AmountHeld,i=r*n,s=i-o.originalCostCrypto2,l=n>0?s*e.incomeSplitCrypto1Percent/100/n:0;a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...o,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:e.baseBid*Math.pow(e.multiplier,o.orderLevel),crypto1Var:-r,crypto2Var:i}}),a({type:"UPDATE_BALANCES",payload:{crypto1:p-r,crypto2:d+i}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto1,"/").concat(e.crypto2),crypto1:e.crypto1,orderType:"SELL",amountCrypto1:r,avgPrice:n,valueCrypto2:i,price1:n,crypto1Symbol:e.crypto1||"",crypto2Symbol:e.crypto2||"",realizedProfitLossCrypto2:s,realizedProfitLossCrypto1:l}}),console.log("✅ SELL: Counter ".concat(t-1," sold ").concat(r.toFixed(6)," ").concat(e.crypto1,". Profit: $").concat(s.toFixed(2))),g({title:"SELL Executed",description:"Counter ".concat(t-1,": Profit $").concat(s.toFixed(2)),duration:2e3}),h("soundOrderExecution");let u=s>0?"\uD83D\uDCC8":s<0?"\uD83D\uDCC9":"➖";C("\uD83D\uDD34 <b>SELL EXECUTED</b>\n"+"\uD83D\uDCCA Counter: ".concat(t-1,"\n")+"\uD83D\uDCB0 Amount: ".concat(r.toFixed(6)," ").concat(e.crypto1,"\n")+"\uD83D\uDCB5 Price: $".concat(n.toFixed(2),"\n")+"\uD83D\uDCB8 Received: $".concat(i.toFixed(2)," ").concat(e.crypto2,"\n")+"".concat(u," Profit: $").concat(s.toFixed(2)," ").concat(e.crypto2,"\n")+"\uD83D\uDCC8 Mode: Simple Spot"),y++,p-=r,d+=i}}else if("StablecoinSwap"===e.tradingMode){if("Free"===r.status){let t=r.valueLevel;if(d>=t){let n=b(e.crypto2||"USDT"),i=e.preferredStablecoin||"USDT",s=b(i),l=n/s,u=t*l,S=b(e.crypto1||"BTC"),f=S*(o.currentMarketPrice/(S/n)),m=f/s,D=u/m,v=r.orderLevel+1,w=e.baseBid*Math.pow(e.multiplier,v),E={...r,status:"Full",orderLevel:v,valueLevel:w,crypto1AmountHeld:D,originalCostCrypto2:t,crypto1Var:D,crypto2Var:-t};console.log("\uD83D\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:",{counter:r.counter,amountCrypto2ToUse:t,crypto1Bought:D,originalCostCrypto2:t,newLevel:v,crypto1StablecoinPrice:m,fluctuatedCrypto1Price:f,currentMarketPrice:o.currentMarketPrice}),a({type:"UPDATE_TARGET_PRICE_ROW",payload:E}),a({type:"UPDATE_BALANCES",payload:{crypto1:p+D,crypto2:d-t}});let T=b(e.crypto2)/b(i),P=t*(l-T),A=P/m;console.log("\uD83D\uDCCA StablecoinSwap Step A SELL P/L:",{crypto2:e.crypto2,amountSold:t,currentPrice:l,basePrice:T,estimatedProfitCrypto2:P,estimatedProfitCrypto1:A}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto2,"/").concat(i),crypto1:e.crypto2,orderType:"SELL",amountCrypto1:t,avgPrice:l,valueCrypto2:u,price1:l,crypto1Symbol:e.crypto2||"",crypto2Symbol:i,realizedProfitLossCrypto2:P,realizedProfitLossCrypto1:A}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto1,"/").concat(i),crypto1:e.crypto1,orderType:"BUY",amountCrypto1:D,avgPrice:m,valueCrypto2:u,price1:m,crypto1Symbol:e.crypto1||"",crypto2Symbol:i}}),console.log("✅ STABLECOIN BUY: Counter ".concat(r.counter," | Step 1: Sold ").concat(t," ").concat(e.crypto2," → ").concat(u.toFixed(2)," ").concat(i," | Step 2: Bought ").concat(D.toFixed(6)," ").concat(e.crypto1," | Level: ").concat(r.orderLevel," → ").concat(v)),g({title:"BUY Executed (Stablecoin)",description:"Counter ".concat(r.counter,": ").concat(D.toFixed(6)," ").concat(e.crypto1," via ").concat(i),duration:2e3}),h("soundOrderExecution"),C("\uD83D\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\n"+"\uD83D\uDCCA Counter: ".concat(r.counter,"\n")+"\uD83D\uDD04 Step 1: Sold ".concat(t.toFixed(2)," ").concat(e.crypto2," → ").concat(u.toFixed(2)," ").concat(i,"\n")+"\uD83D\uDD04 Step 2: Bought ".concat(D.toFixed(6)," ").concat(e.crypto1,"\n")+"\uD83D\uDCCA Level: ".concat(r.orderLevel," → ").concat(v,"\n")+"\uD83D\uDCC8 Mode: Stablecoin Swap"),y++,d-=t,p+=D}else console.log("❌ Insufficient ".concat(e.crypto2," balance for stablecoin swap: ").concat(d.toFixed(6)," < ").concat(t.toFixed(6))),E(e.crypto2,d,t).catch(console.error)}let t=r.counter,n=l.find(e=>e.counter===t-1);if(console.log("\uD83D\uDD0D StablecoinSwap SELL Check:",{currentCounter:t,inferiorRowFound:!!n,inferiorRowStatus:null==n?void 0:n.status,inferiorRowCrypto1Held:null==n?void 0:n.crypto1AmountHeld,inferiorRowOriginalCost:null==n?void 0:n.originalCostCrypto2,canExecuteSell:!!(n&&"Full"===n.status&&n.crypto1AmountHeld&&n.originalCostCrypto2)}),n&&"Full"===n.status&&n.crypto1AmountHeld&&n.originalCostCrypto2){let r=n.crypto1AmountHeld,i=b(e.crypto1||"BTC"),s=b(e.crypto2||"USDT"),l=e.preferredStablecoin||"USDT",u=b(l),S=i*(o.currentMarketPrice/(i/s))/u,f=r*S,m=s/u,D=f/m,v=n.originalCostCrypto2||0,w=D-v,E=S>0?w/S:0;0===w&&console.warn("⚠️ P/L is exactly 0 - this might indicate an issue:\n                - Are prices changing between BUY and SELL?\n                - Is the market price fluctuation working?\n                - Current market price: ".concat(o.currentMarketPrice)),console.log("\uD83D\uDCB0 StablecoinSwap P/L Calculation DETAILED:\n              - Inferior Row Counter: ".concat(n.counter,"\n              - Original Cost (Crypto2): ").concat(v,"\n              - Crypto2 Reacquired: ").concat(D,"\n              - Realized P/L (Crypto2): ").concat(w,"\n              - Crypto1 Stablecoin Price: ").concat(S,"\n              - Realized P/L (Crypto1): ").concat(E,"\n              - Is Profitable: ").concat(w>0?"YES":"NO","\n              - Calculation: ").concat(D," - ").concat(v," = ").concat(w)),a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...n,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:e.baseBid*Math.pow(e.multiplier,n.orderLevel),crypto1Var:0,crypto2Var:0}}),a({type:"UPDATE_BALANCES",payload:{crypto1:p-r,crypto2:d+D}}),console.log("\uD83D\uDCCA Adding StablecoinSwap SELL order to history with P/L:",{realizedProfitLossCrypto2:w,realizedProfitLossCrypto1:E,pair:"".concat(e.crypto1,"/").concat(l),orderType:"SELL",amountCrypto1:r,avgPrice:S,valueCrypto2:f,price1:S,crypto1Symbol:e.crypto1,crypto2Symbol:l}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto1,"/").concat(l),crypto1:e.crypto1,orderType:"SELL",amountCrypto1:r,avgPrice:S,valueCrypto2:f,price1:S,crypto1Symbol:e.crypto1||"",crypto2Symbol:l,realizedProfitLossCrypto2:w,realizedProfitLossCrypto1:E}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,c.A)(),timestamp:Date.now(),pair:"".concat(e.crypto2,"/").concat(l),crypto1:e.crypto2,orderType:"BUY",amountCrypto1:D,avgPrice:m,valueCrypto2:f,price1:m,crypto1Symbol:e.crypto2||"",crypto2Symbol:l}}),console.log("✅ STABLECOIN SELL: Counter ".concat(t-1," | Step A: Sold ").concat(r.toFixed(6)," ").concat(e.crypto1," → ").concat(f.toFixed(2)," ").concat(l," | Step B: Bought ").concat(D.toFixed(2)," ").concat(e.crypto2," | Profit: ").concat(w.toFixed(2)," ").concat(e.crypto2," | Level: ").concat(n.orderLevel," (unchanged)")),g({title:"SELL Executed (Stablecoin)",description:"Counter ".concat(t-1,": Profit ").concat(w.toFixed(2)," ").concat(e.crypto2," via ").concat(l),duration:2e3}),h("soundOrderExecution");let T=w>0?"\uD83D\uDCC8":w<0?"\uD83D\uDCC9":"➖";C("\uD83D\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\n"+"\uD83D\uDCCA Counter: ".concat(t-1,"\n")+"\uD83D\uDD04 Step A: Sold ".concat(r.toFixed(6)," ").concat(e.crypto1," → ").concat(f.toFixed(2)," ").concat(l,"\n")+"\uD83D\uDD04 Step B: Bought ".concat(D.toFixed(2)," ").concat(e.crypto2,"\n")+"".concat(T," Profit: ").concat(w.toFixed(2)," ").concat(e.crypto2,"\n")+"\uD83D\uDCCA Level: ".concat(n.orderLevel," (unchanged)\n")+"\uD83D\uDCC8 Mode: Stablecoin Swap"),y++,p-=r,d+=D}}}}y>0&&console.log("\uD83C\uDFAF CYCLE COMPLETE: ".concat(y," actions taken at price $").concat(n.toFixed(2)))},[o.botSystemStatus,o.currentMarketPrice,o.targetPriceRows,o.config,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,a,g,h,C]);let L=(0,r.useCallback)(()=>o.targetPriceRows&&Array.isArray(o.targetPriceRows)?o.targetPriceRows.map(t=>{let e,n;let r=o.currentMarketPrice||0,a=t.targetPrice||0;if("Full"===t.status&&t.crypto1AmountHeld&&t.originalCostCrypto2){let a=r*t.crypto1AmountHeld-t.originalCostCrypto2;n=a*o.config.incomeSplitCrypto2Percent/100,r>0&&(e=a*o.config.incomeSplitCrypto1Percent/100/r)}return{...t,currentPrice:r,priceDifference:a-r,priceDifferencePercent:r>0?(a-r)/r*100:0,potentialProfitCrypto1:o.config.incomeSplitCrypto1Percent/100*t.valueLevel/(a||1),potentialProfitCrypto2:o.config.incomeSplitCrypto2Percent/100*t.valueLevel,percentFromActualPrice:r&&a?(r/a-1)*100:0,incomeCrypto1:e,incomeCrypto2:n}}).sort((t,e)=>e.targetPrice-t.targetPrice):[],[o.targetPriceRows,o.currentMarketPrice,o.config.incomeSplitCrypto1Percent,o.config.incomeSplitCrypto2Percent,o.config.baseBid,o.config.multiplier]),O=(0,r.useCallback)(async t=>{try{var e;let n={name:"".concat(t.crypto1,"/").concat(t.crypto2," ").concat(t.tradingMode),tradingMode:t.tradingMode,crypto1:t.crypto1,crypto2:t.crypto2,baseBid:t.baseBid,multiplier:t.multiplier,numDigits:t.numDigits,slippagePercent:t.slippagePercent,incomeSplitCrypto1Percent:t.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:t.incomeSplitCrypto2Percent,preferredStablecoin:t.preferredStablecoin,targetPrices:o.targetPriceRows.map(t=>t.targetPrice)},r=await s.oc.saveConfig(n);return console.log("✅ Config saved to backend:",r),(null===(e=r.config)||void 0===e?void 0:e.id)||null}catch(t){return console.error("❌ Failed to save config to backend:",t),g({title:"Backend Error",description:"Failed to save configuration to backend",variant:"destructive",duration:3e3}),null}},[o.targetPriceRows,g]),M=(0,r.useCallback)(async t=>{try{let e=await s.oc.startBot(t);return console.log("✅ Bot started on backend:",e),g({title:"Bot Started",description:"Trading bot started successfully on backend",duration:3e3}),!0}catch(t){return console.error("❌ Failed to start bot on backend:",t),g({title:"Backend Error",description:"Failed to start bot on backend",variant:"destructive",duration:3e3}),!1}},[g]),_=(0,r.useCallback)(async t=>{try{let e=await s.oc.stopBot(t);return console.log("✅ Bot stopped on backend:",e),g({title:"Bot Stopped",description:"Trading bot stopped successfully on backend",duration:3e3}),!0}catch(t){return console.error("❌ Failed to stop bot on backend:",t),g({title:"Backend Error",description:"Failed to stop bot on backend",variant:"destructive",duration:3e3}),!1}},[g]),F=(0,r.useCallback)(async()=>{let t="http://localhost:5000";if(!t){console.error("Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed."),a({type:"SET_BACKEND_STATUS",payload:"offline"});return}try{let e=await fetch("".concat(t,"/health/"));if(!e.ok){console.error("Backend health check failed with status: ".concat(e.status," ").concat(e.statusText));let t=await e.text().catch(()=>"Could not read response text.");console.error("Backend health check response body:",t)}a({type:"SET_BACKEND_STATUS",payload:e.ok?"online":"offline"})}catch(e){a({type:"SET_BACKEND_STATUS",payload:"offline"}),console.error("Backend connectivity check failed. Error details:",e),e.cause&&console.error("Fetch error cause:",e.cause),console.error("Attempted to fetch API URL:","".concat(t,"/health/"))}},[a]);(0,r.useEffect)(()=>{F()},[F]),(0,r.useEffect)(()=>{P(o)},[o]),(0,r.useEffect)(()=>{"Running"===o.botSystemStatus&&0===o.targetPriceRows.length&&(console.log("⚠️ Bot is running but no target prices set - stopping bot"),a({type:"SYSTEM_STOP_BOT"}))},[o.botSystemStatus,o.targetPriceRows.length,a]),(0,r.useEffect)(()=>{"WarmingUp"===o.botSystemStatus&&(console.log("Bot is Warming Up... Immediate execution enabled."),a({type:"SYSTEM_COMPLETE_WARMUP"}),console.log("Bot is now Running immediately."))},[o.botSystemStatus,a]),(0,r.useEffect)(()=>{if("Running"===o.botSystemStatus){let t=l.SessionManager.getInstance(),e=t.getCurrentSessionId();!e&&o.config.crypto1&&o.config.crypto2?(console.log("\uD83D\uDD04 Creating session for running bot..."),t.createNewSessionWithAutoName(o.config).then(async e=>{t.setCurrentSession(e),await t.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!0),console.log("✅ Session created and saved for running bot:",e),window.dispatchEvent(new StorageEvent("storage",{key:"pluto_active_sessions",newValue:Date.now().toString(),storageArea:localStorage}))}).catch(t=>{console.error("❌ Failed to create session for running bot:",t)})):e&&(console.log("\uD83D\uDCBE Marking session as active and saving state for running bot..."),t.setCurrentSession(e),t.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!0).then(()=>{console.log("✅ Session saved and marked as active for running bot:",e),window.dispatchEvent(new StorageEvent("storage",{key:"pluto_active_sessions",newValue:Date.now().toString(),storageArea:localStorage}))}).catch(t=>{console.error("❌ Failed to save session for running bot:",t)}))}},[o.botSystemStatus,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance]),(0,r.useEffect)(()=>{let t=l.SessionManager.getInstance(),e=t.getCurrentSessionId();e&&("Running"===o.botSystemStatus?(t.startSessionRuntime(e),console.log("✅ Started runtime tracking for session:",e)):"Stopped"===o.botSystemStatus&&(t.stopSessionRuntime(e),console.log("⏹️ Stopped runtime tracking for session:",e)))},[o.botSystemStatus]),(0,r.useEffect)(()=>{let t=l.SessionManager.getInstance();"Running"===o.botSystemStatus&&!t.getCurrentSessionId()&&o.config.crypto1&&o.config.crypto2&&o.targetPriceRows.length>0&&t.createNewSessionWithAutoName(o.config).then(e=>{t.setCurrentSession(e),console.log("✅ Auto-created session when bot started:",e)}).catch(t=>{console.error("❌ Failed to auto-create session:",t)});let e=t.getCurrentSessionId();e&&("Running"===o.botSystemStatus?(t.startSessionRuntime(e),console.log("⏱️ Started runtime tracking for session:",e)):"Stopped"===o.botSystemStatus&&(t.stopSessionRuntime(e),console.log("⏹️ Stopped runtime tracking for session:",e)))},[o.botSystemStatus,o.config.crypto1,o.config.crypto2]),(0,r.useEffect)(()=>{let t=l.SessionManager.getInstance(),e=t.getCurrentSessionId();if(e){let n=t.loadSession(e);if(n&&(n.config.crypto1!==o.config.crypto1||n.config.crypto2!==o.config.crypto2)){if(console.log("\uD83D\uDD04 Crypto pair changed during session, auto-saving and resetting..."),"Running"===o.botSystemStatus||o.targetPriceRows.length>0||o.orderHistory.length>0){"Running"===o.botSystemStatus&&_(e);let r=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),a="".concat(n.name," (AutoSaved ").concat(r,")");t.createNewSession(a,n.config).then(async e=>{await t.saveSession(e,n.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!1),console.log("\uD83D\uDCBE AutoSaved session:",a)})}if(a({type:"RESET_FOR_NEW_CRYPTO"}),o.config.crypto1&&o.config.crypto2&&"Running"===o.botSystemStatus){let e={crypto1:o.crypto1Balance,crypto2:o.crypto2Balance,stablecoin:o.stablecoinBalance};t.createNewSessionWithAutoName(o.config,void 0,e).then(n=>{t.setCurrentSession(n),console.log("\uD83C\uDD95 Created new session for crypto pair:",o.config.crypto1,"/",o.config.crypto2,"with balances:",e),g({title:"Crypto Pair Changed",description:"Previous session AutoSaved. New session created for ".concat(o.config.crypto1,"/").concat(o.config.crypto2),duration:5e3})})}else t.clearCurrentSession(),console.log("\uD83D\uDD04 Crypto pair changed but bot wasn't running - cleared current session")}}},[o.config.crypto1,o.config.crypto2]),(0,r.useEffect)(()=>{let t=u.getInstance(),e=p.getInstance(),n=d.getInstance(),r=l.SessionManager.getInstance(),c=t.addListener((t,e)=>{if(console.log("\uD83C\uDF10 Network status changed: ".concat(t?"Online":"Offline")),t||e)t&&!e&&g({title:"Network Reconnected",description:"Connection restored. You can resume trading.",duration:3e3});else{if("Running"===o.botSystemStatus){console.log("\uD83D\uDD34 Internet lost - stopping bot and saving session"),a({type:"SYSTEM_STOP_BOT"}),D("Internet Connection Lost","Trading bot has been automatically stopped due to internet connection loss. Session has been saved and will resume when connection is restored.").catch(console.error);let t=l.SessionManager.getInstance(),e=t.getCurrentSessionId();if(e){let n=t.loadSession(e);if(n){let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),r="".concat(n.name," (Offline Backup ").concat(e,")");t.createNewSession(r,n.config).then(async e=>{await t.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!1),console.log("\uD83D\uDCBE Created offline backup session:",r)})}}}g({title:"Network Disconnected",description:"Bot stopped and session saved. Trading paused until connection restored.",variant:"destructive",duration:8e3})}}),i=n.addListener(t=>{let e=t.usedJSHeapSize/1024/1024;e>150&&(console.warn("\uD83E\uDDE0 High memory usage: ".concat(e.toFixed(2),"MB")),g({title:"High Memory Usage",description:"Memory usage is high (".concat(e.toFixed(0),"MB). Consider refreshing the page."),variant:"destructive",duration:5e3}))}),s=async()=>{try{let t=r.getCurrentSessionId();if(t){if(r.loadSession(t))try{await r.saveSession(t,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,"Running"===o.botSystemStatus)}catch(t){console.warn("⚠️ Session save failed, will retry on next auto-save:",t)}else console.warn("⚠️ Current session ID exists but session not found, clearing session ID"),r.clearCurrentSession()}P(o)}catch(t){console.warn("Auto-save failed, will retry on next cycle:",t)}},y="Running"===o.botSystemStatus?15e3:6e4;e.enable(s,y),console.log("⏰ Auto-save enabled with ".concat(y/1e3,"s interval (bot status: ").concat(o.botSystemStatus,")"));let S=t=>{console.log("\uD83D\uDEAA Browser closing/refreshing - saving session state"),localStorage.setItem("pluto_last_app_close",Date.now().toString());let e=r.getCurrentSessionId(),n=e?r.loadSession(e):null;if(s(),"Running"===o.botSystemStatus&&n){console.log("\uD83D\uDED1 Bot is running - auto-saving to past sessions before close");try{let t=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),a="".concat(n.name," (AutoSaved ").concat(t,")"),c=r.createNewSession(a,n.config);r.saveSession(c,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!1),r.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!1),console.log("✅ Auto-saved running session to past sessions:",a)}catch(t){console.error("❌ Failed to auto-save running session:",t)}let a="Trading bot is currently running and will be stopped. Progress has been auto-saved.";return t.returnValue=a,a}};return window.addEventListener("beforeunload",S),()=>{c(),i(),e.disable(),window.removeEventListener("beforeunload",S)}},[o,g]),(0,r.useEffect)(()=>{p.getInstance().saveNow();let t=l.SessionManager.getInstance(),e=t.getCurrentSessionId();if(e){let n="Running"===o.botSystemStatus;console.log("\uD83D\uDD04 Bot status changed to ".concat(o.botSystemStatus,", updating session active state:"),{sessionId:e,isActive:n}),t.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,n).then(()=>{window.dispatchEvent(new StorageEvent("storage",{key:"pluto_active_sessions",newValue:Date.now().toString(),storageArea:localStorage}))}).catch(t=>{console.error("❌ Failed to update session active state:",t)})}},[o.botSystemStatus,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance]);let N={...o,dispatch:a,setTargetPrices:k,getDisplayOrders:L,checkBackendStatus:F,fetchMarketPrice:f,startBackendBot:M,stopBackendBot:_,saveConfigToBackend:O,saveCurrentSession:I,backendStatus:o.backendStatus,botSystemStatus:o.botSystemStatus,isBotActive:"Running"===o.botSystemStatus};return(0,n.jsx)(B.Provider,{value:N,children:e})},k=()=>{let t=(0,r.useContext)(B);if(void 0===t)throw Error("useTradingContext must be used within a TradingProvider");return t}},7481:(t,e,o)=>{o.d(e,{dj:()=>d});var n=o(2115);let r=0,a=new Map,c=t=>{if(a.has(t))return;let e=setTimeout(()=>{a.delete(t),u({type:"REMOVE_TOAST",toastId:t})},1e6);a.set(t,e)},i=(t,e)=>{switch(e.type){case"ADD_TOAST":return{...t,toasts:[e.toast,...t.toasts].slice(0,1)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(t=>t.id===e.toast.id?{...t,...e.toast}:t)};case"DISMISS_TOAST":{let{toastId:o}=e;return o?c(o):t.toasts.forEach(t=>{c(t.id)}),{...t,toasts:t.toasts.map(t=>t.id===o||void 0===o?{...t,open:!1}:t)}}case"REMOVE_TOAST":if(void 0===e.toastId)return{...t,toasts:[]};return{...t,toasts:t.toasts.filter(t=>t.id!==e.toastId)}}},s=[],l={toasts:[]};function u(t){l=i(l,t),s.forEach(t=>{t(l)})}function p(t){let{...e}=t,o=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>u({type:"DISMISS_TOAST",toastId:o});return u({type:"ADD_TOAST",toast:{...e,id:o,open:!0,onOpenChange:t=>{t||n()}}}),{id:o,dismiss:n,update:t=>u({type:"UPDATE_TOAST",toast:{...t,id:o}})}}function d(){let[t,e]=n.useState(l);return n.useEffect(()=>(s.push(e),()=>{let t=s.indexOf(e);t>-1&&s.splice(t,1)}),[t]),{...t,toast:p,dismiss:t=>u({type:"DISMISS_TOAST",toastId:t})}}},9348:(t,e,o)=>{o.d(e,{Oh:()=>n,Ql:()=>c,hg:()=>r,vA:()=>a});let n={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/order-executed.mp3",soundError:"/sounds/error.mp3",clearOrderHistoryOnStart:!1},r=["BTC","ETH","ADA","SOL","DOGE","LINK","MATIC","DOT","AVAX","XRP","LTC","BCH","BNB","SHIB"],a={BTC:["USDT","USDC","FDUSD","EUR"],ETH:["USDT","USDC","FDUSD","BTC","EUR"],ADA:["USDT","USDC","BTC","ETH"],SOL:["USDT","USDC","BTC","ETH"]},c=["USDT","USDC","FDUSD","DAI"]},9434:(t,e,o)=>{o.d(e,{cn:()=>a});var n=o(2596),r=o(9688);function a(){for(var t=arguments.length,e=Array(t),o=0;o<t;o++)e[o]=arguments[o];return(0,r.QP)((0,n.$)(e))}}}]);