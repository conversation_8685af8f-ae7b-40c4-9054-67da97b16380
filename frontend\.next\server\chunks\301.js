"use strict";exports.id=301,exports.ids=[301],exports.modules={43:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(43210);r(60687);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},1359:(e,t,r)=>{r.d(t,{Oh:()=>i});var n=r(43210),o=0;function i(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},15036:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},18853:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(43210),o=r(66156);function i(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},24026:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},26134:(e,t,r)=>{r.d(t,{UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>Z,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>J});var n=r(43210),o=r(70569),i=r(98599),l=r(11273),a=r(96963),s=r(65551),c=r(31355),u=r(32547),d=r(25028),f=r(46059),p=r(14163),h=r(1359),m=r(42247),v=r(63376),g=r(8730),w=r(60687),y="Dialog",[b,x]=(0,l.A)(y),[S,E]=b(y),C=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:i,onOpenChange:l,modal:c=!0}=e,u=n.useRef(null),d=n.useRef(null),[f=!1,p]=(0,s.i)({prop:o,defaultProp:i,onChange:l});return(0,w.jsx)(S,{scope:t,triggerRef:u,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};C.displayName=y;var R="DialogTrigger",T=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=E(R,r),a=(0,i.s)(t,l.triggerRef);return(0,w.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":K(l.open),...n,ref:a,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});T.displayName=R;var A="DialogPortal",[j,L]=b(A,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:i}=e,l=E(A,t);return(0,w.jsx)(j,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,w.jsx)(f.C,{present:r||l.open,children:(0,w.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};P.displayName=A;var D="DialogOverlay",N=n.forwardRef((e,t)=>{let r=L(D,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=E(D,e.__scopeDialog);return i.modal?(0,w.jsx)(f.C,{present:n||i.open,children:(0,w.jsx)(k,{...o,ref:t})}):null});N.displayName=D;var k=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(D,r);return(0,w.jsx)(m.A,{as:g.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,w.jsx)(p.sG.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),I="DialogContent",M=n.forwardRef((e,t)=>{let r=L(I,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=E(I,e.__scopeDialog);return(0,w.jsx)(f.C,{present:n||i.open,children:i.modal?(0,w.jsx)(O,{...o,ref:t}):(0,w.jsx)(F,{...o,ref:t})})});M.displayName=I;var O=n.forwardRef((e,t)=>{let r=E(I,e.__scopeDialog),l=n.useRef(null),a=(0,i.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,w.jsx)(H,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=n.forwardRef((e,t)=>{let r=E(I,e.__scopeDialog),o=n.useRef(!1),i=n.useRef(!1);return(0,w.jsx)(H,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),H=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...s}=e,d=E(I,r),f=n.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,w.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)($,{titleId:d.titleId}),(0,w.jsx)(q,{contentRef:f,descriptionId:d.descriptionId})]})]})}),_="DialogTitle",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(_,r);return(0,w.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});W.displayName=_;var B="DialogDescription",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(B,r);return(0,w.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});z.displayName=B;var G="DialogClose",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=E(G,r);return(0,w.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function K(e){return e?"open":"closed"}V.displayName=G;var U="DialogTitleWarning",[X,Y]=(0,l.q)(U,{contentName:I,titleName:_,docsSlug:"dialog"}),$=({titleId:e})=>{let t=Y(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},q=({contentRef:e,descriptionId:t})=>{let r=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},Z=C,J=T,Q=P,ee=N,et=M,er=W,en=z,eo=V},28850:(e,t,r)=>{r.d(t,{UC:()=>t4,YJ:()=>t7,In:()=>t3,q7:()=>rt,VF:()=>rn,p4:()=>rr,JU:()=>re,ZL:()=>t9,bL:()=>t2,wn:()=>ri,PP:()=>ro,wv:()=>rl,l9:()=>t6,WT:()=>t5,LM:()=>t8});var n=r(43210),o=r(51215),i=r(67969),l=r(70569),a=r(9510),s=r(98599),c=r(11273),u=r(43),d=r(31355),f=r(1359),p=r(32547),h=r(96963);let m=["top","right","bottom","left"],v=Math.min,g=Math.max,w=Math.round,y=Math.floor,b=e=>({x:e,y:e}),x={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function E(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function A(e){return"y"===e?"height":"width"}function j(e){return["top","bottom"].includes(C(e))?"y":"x"}function L(e){return e.replace(/start|end/g,e=>S[e])}function P(e){return e.replace(/left|right|bottom|top/g,e=>x[e])}function D(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function N(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function k(e,t,r){let n,{reference:o,floating:i}=e,l=j(t),a=T(j(t)),s=A(a),c=C(t),u="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(c){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(R(t)){case"start":n[a]-=p*(r&&u?-1:1);break;case"end":n[a]+=p*(r&&u?-1:1)}return n}let I=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=k(c,n,s),f=n,p={},h=0;for(let r=0;r<a.length;r++){let{name:i,fn:m}=a[r],{x:v,y:g,data:w,reset:y}=await m({x:u,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[i]:{...p[i],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(c=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:u,y:d}=k(c,f,s)),r=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function M(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=E(t,e),h=D(p),m=a[f?"floating"===d?"reference":"floating":d],v=N(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(m)))||r?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:s})),g="floating"===d?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),y=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},b=N(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:w,strategy:s}):g);return{top:(v.top-b.top+h.top)/y.y,bottom:(b.bottom-v.bottom+h.bottom)/y.y,left:(v.left-b.left+h.left)/y.x,right:(b.right-v.right+h.right)/y.x}}function O(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function F(e){return m.some(t=>e[t]>=0)}async function H(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=C(r),a=R(r),s="y"===j(r),c=["left","top"].includes(l)?-1:1,u=i&&s?-1:1,d=E(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*u,y:f*c}:{x:f*c,y:p*u}}function _(){return"undefined"!=typeof window}function W(e){return G(e)?(e.nodeName||"").toLowerCase():"#document"}function B(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function z(e){var t;return null==(t=(G(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function G(e){return!!_()&&(e instanceof Node||e instanceof B(e).Node)}function V(e){return!!_()&&(e instanceof Element||e instanceof B(e).Element)}function K(e){return!!_()&&(e instanceof HTMLElement||e instanceof B(e).HTMLElement)}function U(e){return!!_()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof B(e).ShadowRoot)}function X(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=J(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function Y(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function $(e){let t=q(),r=V(e)?J(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function Z(e){return["html","body","#document"].includes(W(e))}function J(e){return B(e).getComputedStyle(e)}function Q(e){return V(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if("html"===W(e))return e;let t=e.assignedSlot||e.parentNode||U(e)&&e.host||z(e);return U(t)?t.host:t}function et(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=ee(t);return Z(r)?t.ownerDocument?t.ownerDocument.body:t.body:K(r)&&X(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=B(o);if(i){let e=er(l);return t.concat(l,l.visualViewport||[],X(o)?o:[],e&&r?et(e):[])}return t.concat(o,et(o,[],r))}function er(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function en(e){let t=J(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=K(e),i=o?e.offsetWidth:r,l=o?e.offsetHeight:n,a=w(r)!==i||w(n)!==l;return a&&(r=i,n=l),{width:r,height:n,$:a}}function eo(e){return V(e)?e:e.contextElement}function ei(e){let t=eo(e);if(!K(t))return b(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=en(t),l=(i?w(r.width):r.width)/n,a=(i?w(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let el=b(0);function ea(e){let t=B(e);return q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function es(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=eo(e),a=b(1);t&&(n?V(n)&&(a=ei(n)):a=ei(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===B(l))&&o)?ea(l):b(0),c=(i.left+s.x)/a.x,u=(i.top+s.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=B(l),t=n&&V(n)?B(n):n,r=e,o=er(r);for(;o&&n&&t!==r;){let e=ei(o),t=o.getBoundingClientRect(),n=J(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,f*=e.y,c+=i,u+=l,o=er(r=B(o))}}return N({width:d,height:f,x:c,y:u})}function ec(e,t){let r=Q(e).scrollLeft;return t?t.left+r:es(z(e)).left+r}function eu(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:ec(e,n)),y:n.top+t.scrollTop}}function ed(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=B(e),n=z(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=q();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,r);else if("document"===t)n=function(e){let t=z(e),r=Q(e),n=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=g(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),l=-r.scrollLeft+ec(e),a=-r.scrollTop;return"rtl"===J(n).direction&&(l+=g(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:l,y:a}}(z(e));else if(V(t))n=function(e,t){let r=es(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=K(e)?ei(e):b(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:n*i.y}}(t,r);else{let r=ea(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return N(n)}function ef(e){return"static"===J(e).position}function ep(e,t){if(!K(e)||"fixed"===J(e).position)return null;if(t)return t(e);let r=e.offsetParent;return z(e)===r&&(r=r.ownerDocument.body),r}function eh(e,t){let r=B(e);if(Y(e))return r;if(!K(e)){let t=ee(e);for(;t&&!Z(t);){if(V(t)&&!ef(t))return t;t=ee(t)}return r}let n=ep(e,t);for(;n&&["table","td","th"].includes(W(n))&&ef(n);)n=ep(n,t);return n&&Z(n)&&ef(n)&&!$(n)?r:n||function(e){let t=ee(e);for(;K(t)&&!Z(t);){if($(t))return t;if(Y(t))break;t=ee(t)}return null}(e)||r}let em=async function(e){let t=this.getOffsetParent||eh,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=K(t),o=z(t),i="fixed"===r,l=es(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=b(0);if(n||!n&&!i){if(("body"!==W(t)||X(o))&&(a=Q(t)),n){let e=es(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=ec(o))}let c=!o||n||i?b(0):eu(o,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ev={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=z(n),a=!!t&&Y(t.floating);if(n===l||a&&i)return r;let s={scrollLeft:0,scrollTop:0},c=b(1),u=b(0),d=K(n);if((d||!d&&!i)&&(("body"!==W(n)||X(l))&&(s=Q(n)),K(n))){let e=es(n);c=ei(n),u.x=e.x+n.clientLeft,u.y=e.y+n.clientTop}let f=!l||d||i?b(0):eu(l,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+u.x+f.x,y:r.y*c.y-s.scrollTop*c.y+u.y+f.y}},getDocumentElement:z,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?Y(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=et(e,[],!1).filter(e=>V(e)&&"body"!==W(e)),o=null,i="fixed"===J(e).position,l=i?ee(e):e;for(;V(l)&&!Z(l);){let t=J(l),r=$(l);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||X(l)&&!r&&function e(t,r){let n=ee(t);return!(n===r||!V(n)||Z(n))&&("fixed"===J(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=ee(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=i[0],a=i.reduce((e,r)=>{let n=ed(t,r,o);return e.top=g(n.top,e.top),e.right=v(n.right,e.right),e.bottom=v(n.bottom,e.bottom),e.left=g(n.left,e.left),e},ed(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eh,getElementRects:em,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=en(e);return{width:t,height:r}},getScale:ei,isElement:V,isRTL:function(e){return"rtl"===J(e).direction}};function eg(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ew=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:c,padding:u=0}=E(e,t)||{};if(null==c)return{};let d=D(u),f={x:r,y:n},p=T(j(o)),h=A(p),m=await l.getDimensions(c),w="y"===p,y=w?"clientHeight":"clientWidth",b=i.reference[h]+i.reference[p]-f[p]-i.floating[h],x=f[p]-i.reference[p],S=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),C=S?S[y]:0;C&&await (null==l.isElement?void 0:l.isElement(S))||(C=a.floating[y]||i.floating[h]);let L=C/2-m[h]/2-1,P=v(d[w?"top":"left"],L),N=v(d[w?"bottom":"right"],L),k=C-m[h]-N,I=C/2-m[h]/2+(b/2-x/2),M=g(P,v(I,k)),O=!s.arrow&&null!=R(o)&&I!==M&&i.reference[h]/2-(I<P?P:N)-m[h]/2<0,F=O?I<P?I-P:I-k:0;return{[p]:f[p]+F,data:{[p]:M,centerOffset:I-M-F,...O&&{alignmentOffset:F}},reset:O}}}),ey=(e,t,r)=>{let n=new Map,o={platform:ev,...r},i={...o.platform,_c:n};return I(e,t,{...o,platform:i})};var eb="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;function ex(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ex(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ex(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eE(e,t){let r=eS(e);return Math.round(t*r)/r}function eC(e){let t=n.useRef(e);return eb(()=>{t.current=e}),t}let eR=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ew({element:r.current,padding:n}).fn(t):{}:r?ew({element:r,padding:n}).fn(t):{}}}),eT=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await H(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eA=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=E(e,t),c={x:r,y:n},u=await M(t,s),d=j(C(o)),f=T(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+u[e],n=p-u[t];p=g(r,v(p,n))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=h+u[e],n=h-u[t];h=g(r,v(h,n))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-r,y:m.y-n,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:c=!0}=E(e,t),u={x:r,y:n},d=j(o),f=T(d),p=u[f],h=u[d],m=E(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,r=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var g,w;let e="y"===f?"width":"height",t=["top","left"].includes(C(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(w=l.offset)?void 0:w[d])||0)-(t?v.crossAxis:0);h<r?h=r:h>n&&(h=n)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:s,rects:c,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:w=!0,...y}=E(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let b=C(a),x=j(u),S=C(u)===u,D=await (null==d.isRTL?void 0:d.isRTL(f.floating)),N=m||(S||!w?[P(u)]:function(e){let t=P(e);return[L(e),t,L(t)]}(u)),k="none"!==g;!m&&k&&N.push(...function(e,t,r,n){let o=R(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(C(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(L)))),i}(u,w,g,D));let I=[u,...N],O=await M(t,y),F=[],H=(null==(n=s.flip)?void 0:n.overflows)||[];if(p&&F.push(O[b]),h){let e=function(e,t,r){void 0===r&&(r=!1);let n=R(e),o=T(j(e)),i=A(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=P(l)),[l,P(l)]}(a,c,D);F.push(O[e[0]],O[e[1]])}if(H=[...H,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=I[e];if(t)return{data:{index:e,overflows:H},reset:{placement:t}};let r=null==(i=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(v){case"bestFit":{let e=null==(l=H.filter(e=>{if(k){let t=j(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=u}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i;let{placement:l,rects:a,platform:s,elements:c}=t,{apply:u=()=>{},...d}=E(e,t),f=await M(t,d),p=C(l),h=R(l),m="y"===j(l),{width:w,height:y}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let b=y-f.top-f.bottom,x=w-f.left-f.right,S=v(y-f[o],b),T=v(w-f[i],x),A=!t.middlewareData.shift,L=S,P=T;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(P=x),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(L=b),A&&!h){let e=g(f.left,0),t=g(f.right,0),r=g(f.top,0),n=g(f.bottom,0);m?P=w-2*(0!==e||0!==t?e+t:g(f.left,f.right)):L=y-2*(0!==r||0!==n?r+n:g(f.top,f.bottom))}await u({...t,availableWidth:P,availableHeight:L});let D=await s.getDimensions(c.floating);return w!==D.width||y!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=E(e,t);switch(n){case"referenceHidden":{let e=O(await M(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:F(e)}}}case"escaped":{let e=O(await M(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:F(e)}}}default:return{}}}}}(e),options:[e,t]}),eN=(e,t)=>({...eR(e),options:[e,t]});var ek=r(14163),eI=r(60687),eM=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,eI.jsx)(ek.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eI.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eM.displayName="Arrow";var eO=r(13495),eF=r(66156),eH=r(18853),e_="Popper",[eW,eB]=(0,c.A)(e_),[ez,eG]=eW(e_),eV=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,eI.jsx)(ez,{scope:t,anchor:o,onAnchorChange:i,children:r})};eV.displayName=e_;var eK="PopperAnchor",eU=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=eG(eK,r),a=n.useRef(null),c=(0,s.s)(t,a);return n.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eI.jsx)(ek.sG.div,{...i,ref:c})});eU.displayName=eK;var eX="PopperContent",[eY,e$]=eW(eX),eq=n.forwardRef((e,t)=>{let{__scopePopper:r,side:i="bottom",sideOffset:l=0,align:a="center",alignOffset:c=0,arrowPadding:u=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:m=!1,updatePositionStrategy:w="optimized",onPlaced:b,...x}=e,S=eG(eX,r),[E,C]=n.useState(null),R=(0,s.s)(t,e=>C(e)),[T,A]=n.useState(null),j=(0,eH.X)(T),L=j?.width??0,P=j?.height??0,D="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},N=Array.isArray(f)?f:[f],k=N.length>0,I={padding:D,boundary:N.filter(e0),altBoundary:k},{refs:M,floatingStyles:O,placement:F,isPositioned:H,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:s}={},transform:c=!0,whileElementsMounted:u,open:d}=e,[f,p]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=n.useState(i);ex(h,i)||m(i);let[v,g]=n.useState(null),[w,y]=n.useState(null),b=n.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=n.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),S=a||v,E=s||w,C=n.useRef(null),R=n.useRef(null),T=n.useRef(f),A=null!=u,j=eC(u),L=eC(l),P=eC(d),D=n.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:r,middleware:h};L.current&&(e.platform=L.current),ey(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};N.current&&!ex(T.current,t)&&(T.current=t,o.flushSync(()=>{p(t)}))})},[h,t,r,L,P]);eb(()=>{!1===d&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let N=n.useRef(!1);eb(()=>(N.current=!0,()=>{N.current=!1}),[]),eb(()=>{if(S&&(C.current=S),E&&(R.current=E),S&&E){if(j.current)return j.current(S,E,D);D()}},[S,E,D,j,A]);let k=n.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),I=n.useMemo(()=>({reference:S,floating:E}),[S,E]),M=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!I.floating)return e;let t=eE(I.floating,f.x),n=eE(I.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+n+"px)",...eS(I.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,c,I.floating,f.x,f.y]);return n.useMemo(()=>({...f,update:D,refs:k,elements:I,floatingStyles:M}),[f,D,k,I,M])}({strategy:"fixed",placement:i+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,u=eo(e),d=i||l?[...u?et(u):[],...et(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),l&&e.addEventListener("resize",r)});let f=u&&s?function(e,t){let r,n=null,o=z(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=y(d),m=y(o.clientWidth-(u+f)),w={rootMargin:-h+"px "+-m+"px "+-y(o.clientHeight-(d+p))+"px "+-y(u)+"px",threshold:g(0,v(1,s))||1},b=!0;function x(t){let n=t[0].intersectionRatio;if(n!==s){if(!b)return l();n?l(!1,n):r=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==n||eg(c,e.getBoundingClientRect())||l(),b=!1}try{n=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,w)}n.observe(e)}(!0),i}(u,r):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[n]=e;n&&n.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),r()}),u&&!c&&h.observe(u),h.observe(t));let m=c?es(e):null;return c&&function t(){let n=es(e);m&&!eg(m,n)&&r(),m=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",r),l&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===w}),elements:{reference:S.anchor},middleware:[eT({mainAxis:l+P,alignmentAxis:c}),d&&eA({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ej():void 0,...I}),d&&eL({...I}),eP({...I,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),T&&eN({element:T,padding:u}),e1({arrowWidth:L,arrowHeight:P}),m&&eD({strategy:"referenceHidden",...I})]}),[W,B]=e2(F),G=(0,eO.c)(b);(0,eF.N)(()=>{H&&G?.()},[H,G]);let V=_.arrow?.x,K=_.arrow?.y,U=_.arrow?.centerOffset!==0,[X,Y]=n.useState();return(0,eF.N)(()=>{E&&Y(window.getComputedStyle(E).zIndex)},[E]),(0,eI.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:H?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eI.jsx)(eY,{scope:r,placedSide:W,onArrowChange:A,arrowX:V,arrowY:K,shouldHideArrow:U,children:(0,eI.jsx)(ek.sG.div,{"data-side":W,"data-align":B,...x,ref:R,style:{...x.style,animation:H?void 0:"none"}})})})});eq.displayName=eX;var eZ="PopperArrow",eJ={top:"bottom",right:"left",bottom:"top",left:"right"},eQ=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=e$(eZ,r),i=eJ[o.placedSide];return(0,eI.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eI.jsx)(eM,{...n,ref:t,style:{...n.style,display:"block"}})})});function e0(e){return null!==e}eQ.displayName=eZ;var e1=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,c]=e2(r),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=i?u:`${d}px`,h=`${-a}px`):"top"===s?(p=i?u:`${d}px`,h=`${n.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=i?u:`${f}px`):"left"===s&&(p=`${n.floating.width+a}px`,h=i?u:`${f}px`),{data:{x:p,y:h}}}});function e2(e){let[t,r="center"]=e.split("-");return[t,r]}var e6=r(25028),e5=r(8730),e3=r(65551),e9=r(83721),e4=r(69024),e8=r(63376),e7=r(42247),te=[" ","Enter","ArrowUp","ArrowDown"],tt=[" ","Enter"],tr="Select",[tn,to,ti]=(0,a.N)(tr),[tl,ta]=(0,c.A)(tr,[ti,eB]),ts=eB(),[tc,tu]=tl(tr),[td,tf]=tl(tr),tp=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:g}=e,w=ts(t),[y,b]=n.useState(null),[x,S]=n.useState(null),[E,C]=n.useState(!1),R=(0,u.jH)(d),[T=!1,A]=(0,e3.i)({prop:o,defaultProp:i,onChange:l}),[j,L]=(0,e3.i)({prop:a,defaultProp:s,onChange:c}),P=n.useRef(null),D=!y||g||!!y.closest("form"),[N,k]=n.useState(new Set),I=Array.from(N).map(e=>e.props.value).join(";");return(0,eI.jsx)(eV,{...w,children:(0,eI.jsxs)(tc,{required:v,scope:t,trigger:y,onTriggerChange:b,valueNode:x,onValueNodeChange:S,valueNodeHasChildren:E,onValueNodeHasChildrenChange:C,contentId:(0,h.B)(),value:j,onValueChange:L,open:T,onOpenChange:A,dir:R,triggerPointerDownPosRef:P,disabled:m,children:[(0,eI.jsx)(tn.Provider,{scope:t,children:(0,eI.jsx)(td,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{k(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{k(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),D?(0,eI.jsxs)(tQ,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:j,onChange:e=>L(e.target.value),disabled:m,form:g,children:[void 0===j?(0,eI.jsx)("option",{value:""}):null,Array.from(N)]},I):null]})})};tp.displayName=tr;var th="SelectTrigger",tm=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...i}=e,a=ts(r),c=tu(th,r),u=c.disabled||o,d=(0,s.s)(t,c.onTriggerChange),f=to(r),p=n.useRef("touch"),[h,m,v]=t0(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===c.value),n=t1(t,e,r);void 0!==n&&c.onValueChange(n.value)}),g=e=>{u||(c.onOpenChange(!0),v()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eI.jsx)(eU,{asChild:!0,...a,children:(0,eI.jsx)(ek.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":tJ(c.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&te.includes(e.key)&&(g(),e.preventDefault())})})})});tm.displayName=th;var tv="SelectValue",tg=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:i,placeholder:l="",...a}=e,c=tu(tv,r),{onValueNodeHasChildrenChange:u}=c,d=void 0!==i,f=(0,s.s)(t,c.onValueNodeChange);return(0,eF.N)(()=>{u(d)},[u,d]),(0,eI.jsx)(ek.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:tJ(c.value)?(0,eI.jsx)(eI.Fragment,{children:l}):i})});tg.displayName=tv;var tw=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,eI.jsx)(ek.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});tw.displayName="SelectIcon";var ty=e=>(0,eI.jsx)(e6.Z,{asChild:!0,...e});ty.displayName="SelectPortal";var tb="SelectContent",tx=n.forwardRef((e,t)=>{let r=tu(tb,e.__scopeSelect),[i,l]=n.useState();return((0,eF.N)(()=>{l(new DocumentFragment)},[]),r.open)?(0,eI.jsx)(tC,{...e,ref:t}):i?o.createPortal((0,eI.jsx)(tS,{scope:e.__scopeSelect,children:(0,eI.jsx)(tn.Slot,{scope:e.__scopeSelect,children:(0,eI.jsx)("div",{children:e.children})})}),i):null});tx.displayName=tb;var[tS,tE]=tl(tb),tC=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:u,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:w,collisionPadding:y,sticky:b,hideWhenDetached:x,avoidCollisions:S,...E}=e,C=tu(tb,r),[R,T]=n.useState(null),[A,j]=n.useState(null),L=(0,s.s)(t,e=>T(e)),[P,D]=n.useState(null),[N,k]=n.useState(null),I=to(r),[M,O]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(R)return(0,e8.Eq)(R)},[R]),(0,f.Oh)();let H=n.useCallback(e=>{let[t,...r]=I().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&A&&(A.scrollTop=0),r===n&&A&&(A.scrollTop=A.scrollHeight),r?.focus(),document.activeElement!==o))return},[I,A]),_=n.useCallback(()=>H([P,R]),[H,P,R]);n.useEffect(()=>{M&&_()},[M,_]);let{onOpenChange:W,triggerPointerDownPosRef:B}=C;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||W(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,W,B]),n.useEffect(()=>{let e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[W]);let[z,G]=t0(e=>{let t=I().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=t1(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),V=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==C.value&&C.value===t||n)&&(D(e),n&&(F.current=!0))},[C.value]),K=n.useCallback(()=>R?.focus(),[R]),U=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==C.value&&C.value===t||n)&&k(e)},[C.value]),X="popper"===o?tT:tR,Y=X===tT?{side:u,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:w,collisionPadding:y,sticky:b,hideWhenDetached:x,avoidCollisions:S}:{};return(0,eI.jsx)(tS,{scope:r,content:R,viewport:A,onViewportChange:j,itemRefCallback:V,selectedItem:P,onItemLeave:K,itemTextRefCallback:U,focusSelectedItem:_,selectedItemText:N,position:o,isPositioned:M,searchRef:z,children:(0,eI.jsx)(e7.A,{as:e5.DX,allowPinchZoom:!0,children:(0,eI.jsx)(p.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eI.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,eI.jsx)(X,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...E,...Y,onPlaced:()=>O(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,l.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});tC.displayName="SelectContentImpl";var tR=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...l}=e,a=tu(tb,r),c=tE(tb,r),[u,d]=n.useState(null),[f,p]=n.useState(null),h=(0,s.s)(t,e=>p(e)),m=to(r),v=n.useRef(!1),g=n.useRef(!0),{viewport:w,selectedItem:y,selectedItemText:b,focusSelectedItem:x}=c,S=n.useCallback(()=>{if(a.trigger&&a.valueNode&&u&&f&&w&&y&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==a.dir){let o=n.left-t.left,l=r.left-o,a=e.left-l,s=e.width+a,c=Math.max(s,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);u.style.minWidth=s+"px",u.style.left=f+"px"}else{let o=t.right-n.right,l=window.innerWidth-r.right-o,a=window.innerWidth-e.right-l,s=e.width+a,c=Math.max(s,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);u.style.minWidth=s+"px",u.style.right=f+"px"}let l=m(),s=window.innerHeight-20,c=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),x=p+h+c+parseInt(d.paddingBottom,10)+g,S=Math.min(5*y.offsetHeight,x),E=window.getComputedStyle(w),C=parseInt(E.paddingTop,10),R=parseInt(E.paddingBottom,10),T=e.top+e.height/2-10,A=y.offsetHeight/2,j=p+h+(y.offsetTop+A);if(j<=T){let e=l.length>0&&y===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-T,A+(e?R:0)+(f.clientHeight-w.offsetTop-w.offsetHeight)+g);u.style.height=j+t+"px"}else{let e=l.length>0&&y===l[0].ref.current;u.style.top="0px";let t=Math.max(T,p+w.offsetTop+(e?C:0)+A);u.style.height=t+(x-j)+"px",w.scrollTop=j-T+w.offsetTop}u.style.margin="10px 0",u.style.minHeight=S+"px",u.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,u,f,w,y,b,a.dir,o]);(0,eF.N)(()=>S(),[S]);let[E,C]=n.useState();(0,eF.N)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let R=n.useCallback(e=>{e&&!0===g.current&&(S(),x?.(),g.current=!1)},[S,x]);return(0,eI.jsx)(tA,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,eI.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,eI.jsx)(ek.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tR.displayName="SelectItemAlignedPosition";var tT=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...i}=e,l=ts(r);return(0,eI.jsx)(eq,{...l,...i,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tT.displayName="SelectPopperPosition";var[tA,tj]=tl(tb,{}),tL="SelectViewport",tP=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...i}=e,a=tE(tL,r),c=tj(tL,r),u=(0,s.s)(t,a.onViewportChange),d=n.useRef(0);return(0,eI.jsxs)(eI.Fragment,{children:[(0,eI.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eI.jsx)(tn.Slot,{scope:r,children:(0,eI.jsx)(ek.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=c;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+e,l=Math.min(n,i),a=i-l;r.style.height=l+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tP.displayName=tL;var tD="SelectGroup",[tN,tk]=tl(tD),tI=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,h.B)();return(0,eI.jsx)(tN,{scope:r,id:o,children:(0,eI.jsx)(ek.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})});tI.displayName=tD;var tM="SelectLabel",tO=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=tk(tM,r);return(0,eI.jsx)(ek.sG.div,{id:o.id,...n,ref:t})});tO.displayName=tM;var tF="SelectItem",[tH,t_]=tl(tF),tW=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:i=!1,textValue:a,...c}=e,u=tu(tF,r),d=tE(tF,r),f=u.value===o,[p,m]=n.useState(a??""),[v,g]=n.useState(!1),w=(0,s.s)(t,e=>d.itemRefCallback?.(e,o,i)),y=(0,h.B)(),b=n.useRef("touch"),x=()=>{i||(u.onValueChange(o),u.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eI.jsx)(tH,{scope:r,value:o,disabled:i,textId:y,isSelected:f,onItemTextChange:n.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,eI.jsx)(tn.ItemSlot,{scope:r,value:o,disabled:i,textValue:p,children:(0,eI.jsx)(ek.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:w,onFocus:(0,l.m)(c.onFocus,()=>g(!0)),onBlur:(0,l.m)(c.onBlur,()=>g(!1)),onClick:(0,l.m)(c.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:(0,l.m)(c.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:(0,l.m)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(c.onPointerMove,e=>{b.current=e.pointerType,i?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(c.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,l.m)(c.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(tt.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});tW.displayName=tF;var tB="SelectItemText",tz=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:i,style:l,...a}=e,c=tu(tB,r),u=tE(tB,r),d=t_(tB,r),f=tf(tB,r),[p,h]=n.useState(null),m=(0,s.s)(t,e=>h(e),d.onItemTextChange,e=>u.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,g=n.useMemo(()=>(0,eI.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:w,onNativeOptionRemove:y}=f;return(0,eF.N)(()=>(w(g),()=>y(g)),[w,y,g]),(0,eI.jsxs)(eI.Fragment,{children:[(0,eI.jsx)(ek.sG.span,{id:d.textId,...a,ref:m}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});tz.displayName=tB;var tG="SelectItemIndicator",tV=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return t_(tG,r).isSelected?(0,eI.jsx)(ek.sG.span,{"aria-hidden":!0,...n,ref:t}):null});tV.displayName=tG;var tK="SelectScrollUpButton",tU=n.forwardRef((e,t)=>{let r=tE(tK,e.__scopeSelect),o=tj(tK,e.__scopeSelect),[i,l]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eF.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){l(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,eI.jsx)(t$,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tU.displayName=tK;var tX="SelectScrollDownButton",tY=n.forwardRef((e,t)=>{let r=tE(tX,e.__scopeSelect),o=tj(tX,e.__scopeSelect),[i,l]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eF.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,eI.jsx)(t$,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tY.displayName=tX;var t$=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...i}=e,a=tE("SelectScrollButton",r),s=n.useRef(null),c=to(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,eF.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,eI.jsx)(ek.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{u()})})}),tq=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,eI.jsx)(ek.sG.div,{"aria-hidden":!0,...n,ref:t})});tq.displayName="SelectSeparator";var tZ="SelectArrow";function tJ(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ts(r),i=tu(tZ,r),l=tE(tZ,r);return i.open&&"popper"===l.position?(0,eI.jsx)(eQ,{...o,...n,ref:t}):null}).displayName=tZ;var tQ=n.forwardRef((e,t)=>{let{value:r,...o}=e,i=n.useRef(null),l=(0,s.s)(t,i),a=(0,e9.Z)(r);return n.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,eI.jsx)(e4.s,{asChild:!0,children:(0,eI.jsx)("select",{...o,ref:l,defaultValue:r})})});function t0(e){let t=(0,eO.c)(e),r=n.useRef(""),o=n.useRef(0),i=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),l=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,i,l]}function t1(e,t,r){var n,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=r?e.indexOf(r):-1,a=(n=e,o=Math.max(l,0),n.map((e,t)=>n[(o+t)%n.length]));1===i.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}tQ.displayName="BubbleSelect";var t2=tp,t6=tm,t5=tg,t3=tw,t9=ty,t4=tx,t8=tP,t7=tI,re=tO,rt=tW,rr=tz,rn=tV,ro=tU,ri=tY,rl=tq},32547:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(43210),o=r(98599),i=r(14163),l=r(13495),a=r(60687),s="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",u={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...w}=e,[y,b]=n.useState(null),x=(0,l.c)(v),S=(0,l.c)(g),E=n.useRef(null),C=(0,o.s)(t,e=>b(e)),R=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(R.paused||!y)return;let t=e.target;y.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(R.paused||!y)return;let t=e.relatedTarget;null===t||y.contains(t)||h(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(y)});return y&&r.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,y,R.paused]),n.useEffect(()=>{if(y){m.add(R);let e=document.activeElement;if(!y.contains(e)){let t=new CustomEvent(s,u);y.addEventListener(s,x),y.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(h(n,{select:t}),document.activeElement!==r)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(y))}return()=>{y.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(c,u);y.addEventListener(c,S),y.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),y.removeEventListener(c,S),m.remove(R)},0)}}},[y,x,S,R]);let T=n.useCallback(e=>{if(!r&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&h(i,{select:!0})):(e.preventDefault(),r&&h(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...w,ref:C,onKeyDown:T})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function h(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},41360:(e,t,r)=>{r.d(t,{UC:()=>$,B8:()=>X,bL:()=>U,l9:()=>Y});var n=r(43210),o=r(70569),i=r(11273),l=r(9510),a=r(98599),s=r(96963),c=r(14163),u=r(13495),d=r(65551),f=r(43),p=r(60687),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,w,y]=(0,l.N)(v),[b,x]=(0,i.A)(v,[y]),[S,E]=b(v),C=n.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));C.displayName=v;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:l=!1,dir:s,currentTabStopId:v,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:b,preventScrollOnEntryFocus:x=!1,...E}=e,C=n.useRef(null),R=(0,a.s)(t,C),T=(0,f.jH)(s),[A=null,j]=(0,d.i)({prop:v,defaultProp:g,onChange:y}),[P,D]=n.useState(!1),N=(0,u.c)(b),k=w(r),I=n.useRef(!1),[M,O]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(h,N),()=>e.removeEventListener(h,N)},[N]),(0,p.jsx)(S,{scope:r,orientation:i,dir:T,loop:l,currentTabStopId:A,onItemFocus:n.useCallback(e=>j(e),[j]),onItemShiftTab:n.useCallback(()=>D(!0),[]),onFocusableItemAdd:n.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>O(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:P||0===M?-1:0,"data-orientation":i,...E,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);L([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),x)}}I.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>D(!1))})})}),T="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:l=!1,tabStopId:a,...u}=e,d=(0,s.B)(),f=a||d,h=E(T,r),m=h.currentTabStopId===f,v=w(r),{onFocusableItemAdd:y,onFocusableItemRemove:b}=h;return n.useEffect(()=>{if(i)return y(),()=>b()},[i,y,b]),(0,p.jsx)(g.ItemSlot,{scope:r,id:f,focusable:i,active:l,children:(0,p.jsx)(c.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...u,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return j[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>L(r))}})})})});A.displayName=T;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function L(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var P=r(46059),D="Tabs",[N,k]=(0,i.A)(D,[x]),I=x(),[M,O]=N(D),F=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:i,orientation:l="horizontal",dir:a,activationMode:u="automatic",...h}=e,m=(0,f.jH)(a),[v,g]=(0,d.i)({prop:n,onChange:o,defaultProp:i});return(0,p.jsx)(M,{scope:r,baseId:(0,s.B)(),value:v,onValueChange:g,orientation:l,dir:m,activationMode:u,children:(0,p.jsx)(c.sG.div,{dir:m,"data-orientation":l,...h,ref:t})})});F.displayName=D;var H="TabsList",_=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,i=O(H,r),l=I(r);return(0,p.jsx)(C,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:n,children:(0,p.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});_.displayName=H;var W="TabsTrigger",B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...l}=e,a=O(W,r),s=I(r),u=V(a.baseId,n),d=K(a.baseId,n),f=n===a.value;return(0,p.jsx)(A,{asChild:!0,...s,focusable:!i,active:f,children:(0,p.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==a.activationMode;f||i||!e||a.onValueChange(n)})})})});B.displayName=W;var z="TabsContent",G=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:l,...a}=e,s=O(z,r),u=V(s.baseId,o),d=K(s.baseId,o),f=o===s.value,h=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(P.C,{present:i||f,children:({present:r})=>(0,p.jsx)(c.sG.div,{"data-state":f?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:d,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&l})})});function V(e,t){return`${e}-trigger-${t}`}function K(e,t){return`${e}-content-${t}`}G.displayName=z;var U=F,X=_,Y=B,$=G},42247:(e,t,r)=>{r.d(t,{A:()=>K});var n,o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,r(43210)),a="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var u="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,i,l=(t=null,void 0===r&&(r=f),n=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,i);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){i=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var o=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(o)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return l.options=o({async:!0,ssr:!1},e),l}(),h=function(){},m=l.forwardRef(function(e,t){var r,n,a,s,f=l.useRef(null),m=l.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=m[0],g=m[1],w=e.forwardProps,y=e.children,b=e.className,x=e.removeScrollBar,S=e.enabled,E=e.shards,C=e.sideCar,R=e.noIsolation,T=e.inert,A=e.allowPinchZoom,j=e.as,L=e.gapMode,P=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(r=[f,t],n=function(e){return r.forEach(function(t){return c(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=n,s=a.facade,u(function(){var e=d.get(s);if(e){var t=new Set(e),n=new Set(r),o=s.current;t.forEach(function(e){n.has(e)||c(e,null)}),n.forEach(function(e){t.has(e)||c(e,o)})}d.set(s,r)},[r]),s),N=o(o({},P),v);return l.createElement(l.Fragment,null,S&&l.createElement(C,{sideCar:p,removeScrollBar:x,shards:E,noIsolation:R,inert:T,setCallbacks:g,allowPinchZoom:!!A,lockRef:f,gapMode:L}),w?l.cloneElement(l.Children.only(y),o(o({},N),{ref:D})):l.createElement(void 0===j?"div":j,o({},N,{className:b,ref:D}),y))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:s,zeroRight:a};var v=function(e){var t=e.sideCar,r=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,o({},r))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||r.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,r){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},y=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(r),x(n),x(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=S(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},C=y(),R="data-scroll-locked",T=function(e,t,r,n){var o=e.left,i=e.top,l=e.right,c=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(c,"px ").concat(n,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(c,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(n,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(c,"px ").concat(n,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},j=function(){l.useEffect(function(){return document.body.setAttribute(R,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},L=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;j();var i=l.useMemo(function(){return E(o)},[o]);return l.createElement(C,{styles:T(i,!t,o,r?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){P=!1}var N=!!P&&{passive:!1},k=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},I=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),M(e,n)){var o=O(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},M=function(e,t){return"v"===e?k(t,"overflowY"):k(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,r,n,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*n,s=r.target,c=t.contains(s),u=!1,d=a>0,f=0,p=0;do{var h=O(e,s),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&M(e,s)&&(f+=v,p+=m),s=s instanceof ShadowRoot?s.host:s.parentNode}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&a>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(u=!0),u},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},B=0,z=[];let G=(p.useMedium(function(e){var t=l.useRef([]),r=l.useRef([0,0]),n=l.useRef(),o=l.useState(B++)[0],i=l.useState(y)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=H(e),l=r.current,s="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=I(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=I(d,u)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||c)&&(n.current=o),!o)return!0;var p=n.current||o;return F(p,t,e,"h"===p?s:c,!0)},[]),c=l.useCallback(function(e){if(z.length&&z[z.length-1]===i){var r="deltaY"in e?_(e):H(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(a.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=l.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){r.current=H(e),n.current=void 0},[]),f=l.useCallback(function(t){u(t.type,_(t),t.target,s(t,e.lockRef.current))},[]),p=l.useCallback(function(t){u(t.type,H(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,N),document.addEventListener("touchmove",c,N),document.addEventListener("touchstart",d,N),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,N),document.removeEventListener("touchmove",c,N),document.removeEventListener("touchstart",d,N)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(L,{gapMode:e.gapMode}):null)}),v);var V=l.forwardRef(function(e,t){return l.createElement(m,o({},e,{ref:t,sideCar:G}))});V.classNames=m.classNames;let K=V},58450:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},61662:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},63376:(e,t,r)=>{r.d(t,{Eq:()=>u});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,s=function(e){return e&&(e.host||s(e.parentNode))},c=function(e,t,r,n){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=s(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[r]||(l[r]=new WeakMap);var u=l[r],d=[],f=new Set,p=new Set(c),h=function(e){!(!e||f.has(e))&&(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(n),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,s=(u.get(e)||0)+1;o.set(e,a),u.set(e,s),d.push(e),1===a&&l&&i.set(e,!0),1===s&&e.setAttribute(r,"true"),l||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=u.get(e)-1;o.set(e,t),u.set(e,l),t||(i.has(e)||e.removeAttribute(n),i.delete(e)),l||e.removeAttribute(r)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},u=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||n(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,r,"aria-hidden")):function(){return null}}},67969:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},68123:(e,t,r)=>{r.d(t,{LM:()=>Y,OK:()=>$,VM:()=>E,bL:()=>X,lr:()=>I});var n=r(43210),o=r(14163),i=r(46059),l=r(11273),a=r(98599),s=r(13495),c=r(43),u=r(66156),d=r(67969),f=r(70569),p=r(60687),h="ScrollArea",[m,v]=(0,l.A)(h),[g,w]=m(h),y=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:l,scrollHideDelay:s=600,...u}=e,[d,f]=n.useState(null),[h,m]=n.useState(null),[v,w]=n.useState(null),[y,b]=n.useState(null),[x,S]=n.useState(null),[E,C]=n.useState(0),[R,T]=n.useState(0),[A,j]=n.useState(!1),[L,P]=n.useState(!1),D=(0,a.s)(t,e=>f(e)),N=(0,c.jH)(l);return(0,p.jsx)(g,{scope:r,type:i,dir:N,scrollHideDelay:s,scrollArea:d,viewport:h,onViewportChange:m,content:v,onContentChange:w,scrollbarX:y,onScrollbarXChange:b,scrollbarXEnabled:A,onScrollbarXEnabledChange:j,scrollbarY:x,onScrollbarYChange:S,scrollbarYEnabled:L,onScrollbarYEnabledChange:P,onCornerWidthChange:C,onCornerHeightChange:T,children:(0,p.jsx)(o.sG.div,{dir:N,...u,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});y.displayName=h;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,nonce:l,...s}=e,c=w(b,r),u=n.useRef(null),d=(0,a.s)(t,u,c.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,p.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var S="ScrollAreaScrollbar",E=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=w(S,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:a}=i,s="horizontal"===e.orientation;return n.useEffect(()=>(s?l(!0):a(!0),()=>{s?l(!1):a(!1)}),[s,l,a]),"hover"===i.type?(0,p.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===i.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"auto"===i.type?(0,p.jsx)(T,{...o,ref:t,forceMount:r}):"always"===i.type?(0,p.jsx)(A,{...o,ref:t}):null});E.displayName=S;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=w(S,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,p.jsx)(i.C,{present:r||a,children:(0,p.jsx)(T,{"data-state":a?"visible":"hidden",...o,ref:t})})}),R=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,a=w(S,e.__scopeScrollArea),s="horizontal"===e.orientation,c=K(()=>d("SCROLL_END"),100),[u,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>r[e][t]??e,"hidden"));return n.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>d("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,a.scrollHideDelay,d]),n.useEffect(()=>{let e=a.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(d("SCROLL"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,s,d,c]),(0,p.jsx)(i.C,{present:o||"hidden"!==u,children:(0,p.jsx)(A,{"data-state":"hidden"===u?"hidden":"visible",...l,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),T=n.forwardRef((e,t)=>{let r=w(S,e.__scopeScrollArea),{forceMount:o,...l}=e,[a,s]=n.useState(!1),c="horizontal"===e.orientation,u=K(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(c?e:t)}},10);return U(r.viewport,u),U(r.content,u),(0,p.jsx)(i.C,{present:o||a,children:(0,p.jsx)(A,{"data-state":a?"visible":"hidden",...l,ref:t})})}),A=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,i=w(S,e.__scopeScrollArea),l=n.useRef(null),a=n.useRef(0),[s,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=W(s.viewport,s.content),d={...o,sizes:s,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r,n="ltr"){let o=B(r),i=t||o/2,l=r.scrollbar.paddingStart+i,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-i),s=r.content-r.viewport;return G([l,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,p.jsx)(j,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=z(i.viewport.scrollLeft,s,i.dir);l.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=f(e,i.dir))}}):"vertical"===r?(0,p.jsx)(L,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=z(i.viewport.scrollTop,s);l.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=f(e))}}):null}),j=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,l=w(S,e.__scopeScrollArea),[s,c]=n.useState(),u=n.useRef(null),d=(0,a.s)(t,u,l.onScrollbarXChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(N,{"data-orientation":"horizontal",...i,ref:d,sizes:r,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":B(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{u.current&&l.viewport&&s&&o({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:_(s.paddingLeft),paddingEnd:_(s.paddingRight)}})}})}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,l=w(S,e.__scopeScrollArea),[s,c]=n.useState(),u=n.useRef(null),d=(0,a.s)(t,u,l.onScrollbarYChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(N,{"data-orientation":"vertical",...i,ref:d,sizes:r,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":B(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{u.current&&l.viewport&&s&&o({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:_(s.paddingTop),paddingEnd:_(s.paddingBottom)}})}})}),[P,D]=m(S),N=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:l,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:h,onDragScroll:m,onWheelScroll:v,onResize:g,...y}=e,b=w(S,r),[x,E]=n.useState(null),C=(0,a.s)(t,e=>E(e)),R=n.useRef(null),T=n.useRef(""),A=b.viewport,j=i.content-i.viewport,L=(0,s.c)(v),D=(0,s.c)(h),N=K(g,10);function k(e){R.current&&m({x:e.clientX-R.current.left,y:e.clientY-R.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&L(e,j)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[A,x,j,L]),n.useEffect(D,[i,D]),U(x,N),U(b.content,N),(0,p.jsx)(P,{scope:r,scrollbar:x,hasThumb:l,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(u),onThumbPositionChange:D,onThumbPointerDown:(0,s.c)(d),children:(0,p.jsx)(o.sG.div,{...y,ref:C,style:{position:"absolute",...y.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),R.current=x.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),k(e))}),onPointerMove:(0,f.m)(e.onPointerMove,k),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,b.viewport&&(b.viewport.style.scrollBehavior=""),R.current=null})})})}),k="ScrollAreaThumb",I=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=D(k,e.__scopeScrollArea);return(0,p.jsx)(i.C,{present:r||o.hasThumb,children:(0,p.jsx)(M,{ref:t,...n})})}),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...l}=e,s=w(k,r),c=D(k,r),{onThumbPositionChange:u}=c,d=(0,a.s)(t,e=>c.onThumbChange(e)),h=n.useRef(void 0),m=K(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{m(),h.current||(h.current=V(e,u),u())};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,m,u]),(0,p.jsx)(o.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...l,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.m)(e.onPointerUp,c.onThumbPointerUp)})});I.displayName=k;var O="ScrollAreaCorner",F=n.forwardRef((e,t)=>{let r=w(O,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(H,{...e,ref:t}):null});F.displayName=O;var H=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...i}=e,l=w(O,r),[a,s]=n.useState(0),[c,u]=n.useState(0),d=!!(a&&c);return U(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),u(e)}),U(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),s(e)}),d?(0,p.jsx)(o.sG.div,{...i,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function _(e){return e?parseInt(e,10):0}function W(e,t){let r=e/t;return isNaN(r)?0:r}function B(e){let t=W(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function z(e,t,r="ltr"){let n=B(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,l=t.content-t.viewport,a=(0,d.q)(e,"ltr"===r?[0,l]:[-1*l,0]);return G([0,l],[0,i-n])(a)}function G(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var V=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let i={left:e.scrollLeft,top:e.scrollTop},l=r.left!==i.left,a=r.top!==i.top;(l||a)&&t(),r=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function K(e,t){let r=(0,s.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function U(e,t){let r=(0,s.c)(t);(0,u.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var X=y,Y=x,$=F},78148:(e,t,r)=>{r.d(t,{b:()=>a});var n=r(43210),o=r(14163),i=r(60687),l=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},83721:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(43210);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},89743:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},96963:(e,t,r)=>{r.d(t,{B:()=>s});var n,o=r(43210),i=r(66156),l=(n||(n=r.t(o,2)))["useId".toString()]||(()=>void 0),a=0;function s(e){let[t,r]=o.useState(l());return(0,i.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}}};