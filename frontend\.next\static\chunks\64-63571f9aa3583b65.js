"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[64],{64:(e,t,n)=>{n.d(t,{UC:()=>X,B8:()=>q,bL:()=>W,l9:()=>z});var r=n(2115),o=n(5185),a=n(6081),i=n(2284),u=n(6101),l=n(1285),s=n(3655),c=n(9033),d=n(5845),f=n(4315),m=n(5155),v="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[w,N,y]=(0,i.N)(b),[g,h]=(0,a.A)(b,[y]),[R,x]=g(b),C=r.forwardRef((e,t)=>(0,m.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(M,{...e,ref:t})})}));C.displayName=b;var M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:l,currentTabStopId:b,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:y,onEntryFocus:g,preventScrollOnEntryFocus:h=!1,...x}=e,C=r.useRef(null),M=(0,u.s)(t,C),A=(0,f.jH)(l),[I=null,E]=(0,d.i)({prop:b,defaultProp:w,onChange:y}),[j,D]=r.useState(!1),F=(0,c.c)(g),S=N(n),O=r.useRef(!1),[P,_]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(v,F),()=>e.removeEventListener(v,F)},[F]),(0,m.jsx)(R,{scope:n,orientation:a,dir:A,loop:i,currentTabStopId:I,onItemFocus:r.useCallback(e=>E(e),[E]),onItemShiftTab:r.useCallback(()=>D(!0),[]),onFocusableItemAdd:r.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>_(e=>e-1),[]),children:(0,m.jsx)(s.sG.div,{tabIndex:j||0===P?-1:0,"data-orientation":a,...x,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(v,p);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),h)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>D(!1))})})}),A="RovingFocusGroupItem",I=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:u,...c}=e,d=(0,l.B)(),f=u||d,v=x(A,n),p=v.currentTabStopId===f,b=N(n),{onFocusableItemAdd:y,onFocusableItemRemove:g}=v;return r.useEffect(()=>{if(a)return y(),()=>g()},[a,y,g]),(0,m.jsx)(w.ItemSlot,{scope:n,id:f,focusable:a,active:i,children:(0,m.jsx)(s.sG.span,{tabIndex:p?0:-1,"data-orientation":v.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>T(n))}})})})});I.displayName=A;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var j=n(8905),D="Tabs",[F,S]=(0,a.A)(D,[h]),O=h(),[P,_]=F(D),L=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:u,activationMode:c="automatic",...v}=e,p=(0,f.jH)(u),[b,w]=(0,d.i)({prop:r,onChange:o,defaultProp:a});return(0,m.jsx)(P,{scope:n,baseId:(0,l.B)(),value:b,onValueChange:w,orientation:i,dir:p,activationMode:c,children:(0,m.jsx)(s.sG.div,{dir:p,"data-orientation":i,...v,ref:t})})});L.displayName=D;var U="TabsList",k=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=_(U,n),i=O(n);return(0,m.jsx)(C,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:(0,m.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});k.displayName=U;var G="TabsTrigger",K=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...i}=e,u=_(G,n),l=O(n),c=V(u.baseId,r),d=H(u.baseId,r),f=r===u.value;return(0,m.jsx)(I,{asChild:!0,...l,focusable:!a,active:f,children:(0,m.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...i,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;f||a||!e||u.onValueChange(r)})})})});K.displayName=G;var $="TabsContent",B=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...u}=e,l=_($,n),c=V(l.baseId,o),d=H(l.baseId,o),f=o===l.value,v=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(j.C,{present:a||f,children:n=>{let{present:r}=n;return(0,m.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:d,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&i})}})});function V(e,t){return"".concat(e,"-trigger-").concat(t)}function H(e,t){return"".concat(e,"-content-").concat(t)}B.displayName=$;var W=L,q=k,z=K,X=B},1285:(e,t,n)=>{n.d(t,{B:()=>l});var r,o=n(2115),a=n(2712),i=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),u=0;function l(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},2284:(e,t,n)=>{n.d(t,{N:()=>l});var r=n(2115),o=n(6081),a=n(6101),i=n(9708),u=n(5155);function l(e){let t=e+"CollectionProvider",[n,l]=(0,o.A)(t),[s,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),a=r.useRef(new Map).current;return(0,u.jsx)(s,{scope:t,itemMap:a,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=c(f,n),l=(0,a.s)(t,o.collectionRef);return(0,u.jsx)(i.DX,{ref:l,children:r})});m.displayName=f;let v=e+"CollectionItemSlot",p="data-radix-collection-item",b=r.forwardRef((e,t)=>{let{scope:n,children:o,...l}=e,s=r.useRef(null),d=(0,a.s)(t,s),f=c(v,n);return r.useEffect(()=>(f.itemMap.set(s,{ref:s,...l}),()=>void f.itemMap.delete(s))),(0,u.jsx)(i.DX,{[p]:"",ref:d,children:o})});return b.displayName=v,[{Provider:d,Slot:m,ItemSlot:b},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(p,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},l]}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},4315:(e,t,n)=>{n.d(t,{jH:()=>a});var r=n(2115);n(5155);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r=n(2115),o=n(9033);function a({prop:e,defaultProp:t,onChange:n=()=>{}}){let[a,i]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[a]=n,i=r.useRef(a),u=(0,o.c)(t);return r.useEffect(()=>{i.current!==a&&(u(a),i.current=a)},[a,i,u]),n}({defaultProp:t,onChange:n}),u=void 0!==e,l=u?e:a,s=(0,o.c)(n);return[l,r.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else i(t)},[u,e,i,s])]}},6081:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>a});var r=n(2115),o=n(5155);function a(e,t){let n=r.createContext(t),a=e=>{let{children:t,...a}=e,i=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(n.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){let i=r.createContext(a),u=n.length;n=[...n,a];let l=t=>{let{scope:n,children:a,...l}=t,s=n?.[e]?.[u]||i,c=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(s.Provider,{value:c,children:a})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[u]||i,s=r.useContext(l);if(s)return s;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},8905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(2115),o=n(6101),a=n(2712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(l.current);c.current="mounted"===d?e:"none"},[d]),(0,a.N)(()=>{let t=l.current,n=s.current;if(n!==e){let r=c.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=u(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=u(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),l="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,o.s)(i.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||i.isPresent?r.cloneElement(l,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}}}]);