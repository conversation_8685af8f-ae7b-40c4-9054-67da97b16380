exports.id=991,exports.ids=[991],exports.modules={8417:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(60687);a(43210);var s=a(60240);let n=({className:e,useFullName:t=!0})=>(0,r.jsxs)("div",{className:`flex items-center text-2xl font-bold text-primary ${e}`,children:[(0,r.jsx)(s.A,{className:"mr-2 h-7 w-7"}),(0,r.jsxs)("span",{children:["Pluto",t?" Trading Bot":""]})]})},15079:(e,t,a)=>{"use strict";a.d(t,{bq:()=>m,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>p});var r=a(60687),s=a(43210),n=a(28850),l=a(61662),i=a(89743),o=a(58450),d=a(4780);let c=n.bL;n.YJ;let p=n.WT,m=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=n.l9.displayName;let u=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=n.PP.displayName;let f=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));f.displayName=n.wn.displayName;let x=s.forwardRef(({className:e,children:t,position:a="popper",...s},l)=>(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{ref:l,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:[(0,r.jsx)(u,{}),(0,r.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(f,{})]})}));x.displayName=n.UC.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.JU.displayName;let h=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(n.p4,{children:t})]}));h.displayName=n.q7.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.wv.displayName},20674:(e,t,a)=>{Promise.resolve().then(a.bind(a,63144))},22649:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(60687);a(43210);var s=a(85763),n=a(16189),l=a(82614);let i=(0,l.A)("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]]),o=(0,l.A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),d=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,r.jsx)(i,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,r.jsx)(o,{})}];function c(){let e=(0,n.useRouter)(),t=(0,n.usePathname)(),a="orders";return"/dashboard/history"===t&&(a="history"),(0,r.jsx)(s.tU,{value:a,onValueChange:t=>{let a=d.find(e=>e.value===t);a&&e.push(a.href)},className:"w-full mb-6",children:(0,r.jsx)(s.j7,{className:"grid w-full grid-cols-2 bg-card border-2 border-border",children:d.map(e=>(0,r.jsx)(s.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var r=a(60687),s=a(43210),n=a(8730),l=a(24224),i=a(4780);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef(({className:e,variant:t,size:a,asChild:s=!1,...l},d)=>{let c=s?n.DX:"button";return(0,r.jsx)(c,{className:(0,i.cn)(o({variant:t,size:a,className:e})),ref:d,...l})});d.displayName="Button"},38826:(e,t,a)=>{Promise.resolve().then(a.bind(a,78368))},39907:(e,t,a)=>{"use strict";a.d(t,{w:()=>c});var r=a(60687),s=a(43210),n=a(14163),l="horizontal",i=["horizontal","vertical"],o=s.forwardRef((e,t)=>{var a;let{decorative:s,orientation:o=l,...d}=e,c=(a=o,i.includes(a))?o:l;return(0,r.jsx)(n.sG.div,{"data-orientation":c,...s?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:t})});o.displayName="Separator";var d=a(4780);let c=s.forwardRef(({className:e,orientation:t="horizontal",decorative:a=!0,...s},n)=>(0,r.jsx)(o,{ref:n,decorative:a,orientation:t,className:(0,d.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...s}));c.displayName=o.displayName},42692:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,F:()=>i});var r=a(60687),s=a(43210),n=a(68123),l=a(4780);let i=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.bL,{ref:s,className:(0,l.cn)("relative overflow-hidden",e),...a,children:[(0,r.jsx)(n.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,r.jsx)(o,{}),(0,r.jsx)(n.OK,{})]}));i.displayName=n.bL.displayName;let o=s.forwardRef(({className:e,orientation:t="vertical",...a},s)=>(0,r.jsx)(n.VM,{ref:s,orientation:t,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...a,children:(0,r.jsx)(n.lr,{className:"relative flex-1 rounded-full bg-border"})}));o.displayName=n.VM.displayName},44493:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>i});var r=a(60687),s=a(43210),n=a(4780);let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...t}));l.displayName="Card";let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...t}));i.displayName="CardHeader";let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("h3",{ref:a,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("p",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("p-4 md:p-6 pt-0",e),...t}));c.displayName="CardContent",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",e),...t})).displayName="CardFooter"},63144:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx","default")},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>m,Es:()=>f,HM:()=>c,L3:()=>x,c7:()=>u,lG:()=>o,rr:()=>h});var r=a(60687),s=a(43210),n=a(26134),l=a(78726),i=a(4780);let o=n.bL;n.l9;let d=n.ZL,c=n.bm,p=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));p.displayName=n.hJ.displayName;let m=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(d,{children:[(0,r.jsx)(p,{}),(0,r.jsxs)(n.UC,{ref:s,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,r.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=n.UC.displayName;let u=({className:e,...t})=>(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});u.displayName="DialogHeader";let f=({className:e,...t})=>(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});f.displayName="DialogFooter";let x=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=n.hE.displayName;let h=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));h.displayName=n.VY.displayName},78368:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ex});var r=a(60687),s=a(43210),n=a(85814),l=a.n(n),i=a(16189),o=a(8417),d=a(29523),c=a(63213),p=a(24026),m=a(82614);let u=(0,m.A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),f=(0,m.A)("SquarePlus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]),x=(0,m.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var h=a(96834),b=a(44493),g=a(67857),y=a(8158),v=a(15036);function j({className:e=""}){let[t,a]=(0,s.useState)(navigator.onLine),[n,l]=(0,s.useState)(new Date);return(0,r.jsxs)("div",{className:`flex items-center gap-2 ${e}`,children:[(0,r.jsxs)(h.E,{variant:t?"default":"destructive",className:`flex items-center gap-1 ${t?"bg-green-600 hover:bg-green-600/90 text-white":""}`,children:[t?(0,r.jsx)(g.A,{className:"h-3 w-3"}):(0,r.jsx)(y.A,{className:"h-3 w-3"}),t?"Online":"Offline"]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,r.jsx)(v.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:n.toLocaleTimeString()})]})]})}function N(){let{logout:e}=(0,c.A)();(0,i.useRouter)();let t=(0,i.usePathname)(),a=[{href:"/dashboard",label:"Home",icon:(0,r.jsx)(p.A,{})},{href:"/admin",label:"Admin Panel",icon:(0,r.jsx)(u,{})}];return(0,r.jsxs)("header",{className:"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(o.A,{useFullName:!1}),(0,r.jsx)(j,{})]}),(0,r.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[a.map(e=>(0,r.jsx)(d.$,{variant:t===e.href?"default":"ghost",size:"sm",asChild:!0,className:`${t===e.href?"btn-neo":"hover:bg-accent/50"}`,children:(0,r.jsxs)(l(),{href:e.href,className:"flex items-center gap-2",children:[e.icon,(0,r.jsx)("span",{className:"hidden sm:inline",children:e.label})]})},e.label)),(0,r.jsxs)(d.$,{variant:"ghost",size:"sm",onClick:()=>{window.open("/dashboard?newSession=true","_blank")},className:"hover:bg-accent/50 flex items-center gap-2",title:"Open a new independent trading session in a new tab",children:[(0,r.jsx)(f,{}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"New Session"})]}),(0,r.jsxs)(d.$,{variant:"ghost",size:"sm",onClick:()=>{e()},className:"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2",children:[(0,r.jsx)(x,{}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})}var w=a(78895),S=a(89667),C=a(80013),k=a(98599),A=a(11273),E=a(70569),T=a(65551),P=a(83721),R=a(18853),M=a(46059),O=a(14163),D="Checkbox",[F,L]=(0,A.A)(D),[U,B]=F(D),I=s.forwardRef((e,t)=>{let{__scopeCheckbox:a,name:n,checked:l,defaultChecked:i,required:o,disabled:d,value:c="on",onCheckedChange:p,form:m,...u}=e,[f,x]=s.useState(null),h=(0,k.s)(t,e=>x(e)),b=s.useRef(!1),g=!f||m||!!f.closest("form"),[y=!1,v]=(0,T.i)({prop:l,defaultProp:i,onChange:p}),j=s.useRef(y);return s.useEffect(()=>{let e=f?.form;if(e){let t=()=>v(j.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[f,v]),(0,r.jsxs)(U,{scope:a,state:y,disabled:d,children:[(0,r.jsx)(O.sG.button,{type:"button",role:"checkbox","aria-checked":V(y)?"mixed":y,"aria-required":o,"data-state":z(y),"data-disabled":d?"":void 0,disabled:d,value:c,...u,ref:h,onKeyDown:(0,E.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,E.m)(e.onClick,e=>{v(e=>!!V(e)||!e),g&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),g&&(0,r.jsx)(G,{control:f,bubbles:!b.current,name:n,value:c,checked:y,required:o,disabled:d,form:m,style:{transform:"translateX(-100%)"},defaultChecked:!V(i)&&i})]})});I.displayName=D;var $="CheckboxIndicator",_=s.forwardRef((e,t)=>{let{__scopeCheckbox:a,forceMount:s,...n}=e,l=B($,a);return(0,r.jsx)(M.C,{present:s||V(l.state)||!0===l.state,children:(0,r.jsx)(O.sG.span,{"data-state":z(l.state),"data-disabled":l.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});_.displayName=$;var G=e=>{let{control:t,checked:a,bubbles:n=!0,defaultChecked:l,...i}=e,o=s.useRef(null),d=(0,P.Z)(a),c=(0,R.X)(t);s.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==a&&t){let r=new Event("click",{bubbles:n});e.indeterminate=V(a),t.call(e,!V(a)&&a),e.dispatchEvent(r)}},[d,a,n]);let p=s.useRef(!V(a)&&a);return(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l??p.current,...i,tabIndex:-1,ref:o,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function V(e){return"indeterminate"===e}function z(e){return V(e)?"indeterminate":e?"checked":"unchecked"}var J=a(58450),q=a(4780);let H=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(I,{ref:a,className:(0,q.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:(0,r.jsx)(_,{className:(0,q.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(J.A,{className:"h-4 w-4"})})}));H.displayName=I.displayName;var Z=a(15079),W=a(42692),X=a(39907),Y=a(63503);let K=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("textarea",{className:(0,q.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));K.displayName="Textarea";var Q=a(85763),ee=a(29867);function et({isOpen:e,onClose:t,onSetTargetPrices:a}){let n;let[l,i]=(0,s.useState)("manual"),[o,c]=(0,s.useState)(""),{toast:p}=(0,ee.dj)();try{n=(0,w.U)()}catch(e){console.warn("Trading context not available:",e),n=null}let[m,u]=(0,s.useState)("8"),[f,x]=(0,s.useState)("5"),[h,b]=(0,s.useState)("even"),g=n?.currentMarketPrice||1e5,y=n?.config?.slippagePercent||.2,v=()=>{let e=parseInt(m),t=parseFloat(f);if(!e||e<2||e>20||!t||t<=0)return[];let a=[],r=g*(1-t/100),s=g*(1+t/100);if("even"===h)for(let t=0;t<e;t++){let n=r+t/(e-1)*(s-r);a.push(Math.round(n))}else if("fibonacci"===h){let t=[0,.236,.382,.5,.618,.764,.854,.927,1];for(let n=0;n<e;n++){let l=r+(s-r)*(t[Math.min(n,t.length-1)]||n/(e-1));a.push(Math.round(l))}}else if("exponential"===h)for(let t=0;t<e;t++){let n=r+(s-r)*Math.pow(t/(e-1),1.5);a.push(Math.round(n))}let n=3*y/100*g,l=a.sort((e,t)=>e-t),i=[];for(let e=0;e<l.length;e++){let t=l[e];if(i.length>0){let e=i[i.length-1];t-e<n&&(t=e+n)}i.push(Math.round(t))}return i},j=()=>{let e=o.split("\n").filter(e=>""!==e.trim()).map(e=>parseFloat(e.trim())).filter(e=>!isNaN(e)&&e>0).sort((e,t)=>e-t);if(e.length<2)return{hasOverlap:!1,message:""};let t=y/100*g;for(let a=0;a<e.length-1;a++)if(e[a]+t>=e[a+1]-t){let r=2*t,s=e[a+1]-e[a];return{hasOverlap:!0,message:`Overlap detected between ${e[a]} and ${e[a+1]}. Minimum gap needed: ${r.toFixed(0)}, actual gap: ${s.toFixed(0)}`}}return{hasOverlap:!1,message:"No slippage zone overlaps detected ✓"}},N=j();return(0,r.jsx)(Y.lG,{open:e,onOpenChange:t,children:(0,r.jsxs)(Y.Cf,{className:"sm:max-w-2xl bg-card border-2 border-border",children:[(0,r.jsxs)(Y.c7,{children:[(0,r.jsx)(Y.L3,{className:"text-primary",children:"Set Target Prices"}),(0,r.jsx)(Y.rr,{children:"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."})]}),(0,r.jsxs)(Q.tU,{value:l,onValueChange:i,className:"w-full",children:[(0,r.jsxs)(Q.j7,{className:"grid w-full grid-cols-2",children:[(0,r.jsx)(Q.Xi,{value:"manual",children:"Manual Entry"}),(0,r.jsx)(Q.Xi,{value:"automatic",children:"Automatic Generation"})]}),(0,r.jsx)(Q.av,{value:"manual",className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(C.J,{htmlFor:"target-prices-input",className:"text-left",children:"Target Prices"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored."}),(0,r.jsx)(K,{id:"target-prices-input",value:o,onChange:e=>c(e.target.value),placeholder:"50000 50500 49800",className:"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"}),N.message&&(0,r.jsx)("p",{className:`text-sm ${N.hasOverlap?"text-red-500":"text-green-500"}`,children:N.message})]})}),(0,r.jsxs)(Q.av,{value:"automatic",className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(C.J,{htmlFor:"targetCount",children:"Number of Targets"}),(0,r.jsxs)(Z.l6,{value:m,onValueChange:u,children:[(0,r.jsx)(Z.bq,{children:(0,r.jsx)(Z.yv,{})}),(0,r.jsx)(Z.gC,{children:[4,6,8,10,12,15,20].map(e=>(0,r.jsxs)(Z.eb,{value:e.toString(),children:[e," targets"]},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(C.J,{htmlFor:"priceRange",children:"Price Range (%)"}),(0,r.jsxs)(Z.l6,{value:f,onValueChange:x,children:[(0,r.jsx)(Z.bq,{children:(0,r.jsx)(Z.yv,{})}),(0,r.jsx)(Z.gC,{children:[2,2.5,3,3.5,4,4.5,5,6,7,8,10].map(e=>(0,r.jsxs)(Z.eb,{value:e.toString(),children:["\xb1",e,"%"]},e))})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(C.J,{htmlFor:"distribution",children:"Distribution Pattern"}),(0,r.jsxs)(Z.l6,{value:h,onValueChange:b,children:[(0,r.jsx)(Z.bq,{children:(0,r.jsx)(Z.yv,{})}),(0,r.jsxs)(Z.gC,{children:[(0,r.jsx)(Z.eb,{value:"even",children:"Even Distribution"}),(0,r.jsx)(Z.eb,{value:"fibonacci",children:"Fibonacci (More near current price)"}),(0,r.jsx)(Z.eb,{value:"exponential",children:"Exponential (Wider spread)"})]})]})]}),(0,r.jsx)("div",{className:"bg-muted p-3 rounded-md",children:(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Current Market Price:"})," $",g.toLocaleString(),(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Slippage:"})," \xb1",y,"% ($",(g*y/100).toFixed(0),")",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Range:"})," $",(g*(1-parseFloat(f)/100)).toLocaleString()," - $",(g*(1+parseFloat(f)/100)).toLocaleString()]})}),(0,r.jsxs)(d.$,{onClick:()=>{c(v().join("\n"))},className:"w-full btn-neo",children:["Generate ",m," Target Prices"]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(C.J,{children:"Generated Prices (Preview)"}),(0,r.jsx)(K,{value:o,onChange:e=>c(e.target.value),className:"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono",placeholder:"Click 'Generate' to create automatic target prices..."}),N.message&&(0,r.jsx)("p",{className:`text-sm ${N.hasOverlap?"text-red-500":"text-green-500"}`,children:N.message})]})]})]}),(0,r.jsxs)(Y.Es,{children:[(0,r.jsx)(Y.HM,{asChild:!0,children:(0,r.jsx)(d.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,r.jsx)(d.$,{type:"button",onClick:()=>{let e=o.split("\n").map(e=>e.trim()).filter(e=>""!==e),r=e.map(e=>parseFloat(e)).filter(e=>!isNaN(e)&&e>0);if(0===r.length&&e.length>0){p({title:"Invalid Input",description:"No valid prices found. Please enter numbers, one per line.",variant:"destructive"});return}let s=j();if(s.hasOverlap){p({title:"Slippage Zone Overlap",description:s.message,variant:"destructive"});return}a(r),p({title:"Target Prices Updated",description:`${r.length} target prices have been set.`}),c(""),t()},disabled:N.hasOverlap,className:"btn-neo",children:"Save Prices"})]})]})})}var ea=a(55280),er=a(78726);let es=(0,m.A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);function en({label:e,value:t,allowedCryptos:a,onValidCrypto:n,placeholder:l="Enter crypto symbol",description:i,className:o}){let[c,p]=(0,s.useState)(""),[m,u]=(0,s.useState)("idle"),[f,x]=(0,s.useState)(""),[h,b]=(0,s.useState)(!1),g=()=>{let e=c.toUpperCase().trim();if(!e){u("invalid"),x("Please enter a crypto symbol");return}if(!a||!Array.isArray(a)){u("invalid"),x("No allowed cryptocurrencies configured");return}a.includes(e)?(u("valid"),x(""),b(!0),n(e)):(u("invalid"),x(`${e} is not available. Allowed: ${a.join(", ")}`))};return(0,r.jsxs)("div",{className:(0,q.cn)("space-y-2",o),children:[(0,r.jsx)(C.J,{htmlFor:`crypto-input-${e}`,children:e}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)(S.p,{id:`crypto-input-${e}`,value:c||t||"",onChange:e=>{p(e.target.value),u("idle"),x("")},onKeyPress:e=>{"Enter"===e.key&&g()},placeholder:l,className:(0,q.cn)("pr-8",(()=>{switch(m){case"valid":return"border-green-500";case"invalid":return"border-red-500";default:return""}})())}),"idle"!==m&&(0,r.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(()=>{switch(m){case"valid":return(0,r.jsx)(J.A,{className:"h-4 w-4 text-green-500"});case"invalid":return(0,r.jsx)(er.A,{className:"h-4 w-4 text-red-500"});default:return null}})()})]}),(0,r.jsx)(d.$,{onClick:g,variant:"outline",className:"btn-neo",disabled:!c.trim(),children:"Check"})]}),t&&h&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(J.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("span",{className:"text-green-600 font-medium",children:["Selected: ",t]})]}),"invalid"===m&&f&&(0,r.jsxs)("div",{className:"flex items-start space-x-2 text-sm text-red-600",children:[(0,r.jsx)(es,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{children:f})]}),i&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:i}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[a&&Array.isArray(a)?a.length:0," cryptocurrencies available"]})]})}let el=(0,m.A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]]);var ei=a(11516);let eo=(0,m.A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),ed=(0,m.A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),ec=["USDT","USDC","BTC"],ep=["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],em=["USDC","DAI","TUSD","FDUSD","USDT","EUR"],eu=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];function ef(){let e;try{e=(0,w.U)()}catch(e){return console.error("Trading context not available:",e),(0,r.jsx)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("p",{className:"text-red-500",children:"Trading context not available. Please refresh the page."})})})}let{config:t,dispatch:a,botSystemStatus:n,appSettings:l,targetPriceRows:i,setTargetPrices:o}=e,c="Running"===n,p="WarmingUp"===n,{toast:m}=(0,ee.dj)(),[u,f]=(0,s.useState)(!1),x=e=>{let t;let{name:r,value:s,type:n,checked:l}=e.target;if("checkbox"===n)t=l;else if("number"===n){if(""===s||null==s)t=0;else{let e=parseFloat(s);t=isNaN(e)?0:e}}else t=s;a({type:"SET_CONFIG",payload:{[r]:t}})},h=(e,r)=>{if(a({type:"SET_CONFIG",payload:{[e]:r}}),"crypto1"===e){let e=ea.vA[r]||ec||["USDT","USDC","BTC"];t.crypto2&&Array.isArray(e)&&e.includes(t.crypto2)||a({type:"SET_CONFIG",payload:{crypto2:e[0]||"USDT"}})}},g=(e,t)=>{let r=parseFloat(t);isNaN(r)&&(r=0),r<0&&(r=0),r>100&&(r=100),"incomeSplitCrypto1Percent"===e?a({type:"SET_CONFIG",payload:{incomeSplitCrypto1Percent:r,incomeSplitCrypto2Percent:100-r}}):a({type:"SET_CONFIG",payload:{incomeSplitCrypto2Percent:r,incomeSplitCrypto1Percent:100-r}})},y=ea.hg||[];return"SimpleSpot"===t.tradingMode?ea.vA[t.crypto1]:(ea.hg||[]).filter(e=>e!==t.crypto1),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_CRYPTOS length:",ea.hg?.length),console.log("\uD83D\uDD0D DEBUG: crypto1Options length:",y.length),console.log("\uD83D\uDD0D DEBUG: First 20 cryptos:",ea.hg?.slice(0,20)),console.log("\uD83D\uDD0D DEBUG: LOCAL_ALLOWED_CRYPTO1:",ep),console.log("\uD83D\uDD0D DEBUG: LOCAL_ALLOWED_CRYPTO2:",em),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_STABLECOINS:",ea.Ql),(0,r.jsxs)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,r.jsx)("h2",{className:"text-2xl font-bold text-sidebar-primary",children:"Trading Configuration"})}),(0,r.jsx)(W.F,{className:"flex-1 pr-2",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(b.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(b.aR,{children:(0,r.jsx)(b.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Mode"})}),(0,r.jsxs)(b.Wu,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(H,{id:"enableStablecoinSwap",checked:"StablecoinSwap"===t.tradingMode,onCheckedChange:e=>{let r;let s=e?"StablecoinSwap":"SimpleSpot";r="StablecoinSwap"===s?ep.filter(e=>e!==t.crypto1)[0]:em[0],a({type:"SET_CONFIG",payload:{tradingMode:s,crypto2:r}})}}),(0,r.jsx)(C.J,{htmlFor:"enableStablecoinSwap",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Enable Stablecoin Swap Mode"})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Current mode: ","SimpleSpot"===t.tradingMode?"Simple Spot":"Stablecoin Swap"]}),"StablecoinSwap"===t.tradingMode&&(0,r.jsxs)("div",{children:[(0,r.jsx)(C.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin"}),(0,r.jsxs)(Z.l6,{name:"preferredStablecoin",value:t.preferredStablecoin,onValueChange:e=>h("preferredStablecoin",e),children:[(0,r.jsx)(Z.bq,{id:"preferredStablecoin",children:(0,r.jsx)(Z.yv,{placeholder:"Select stablecoin"})}),(0,r.jsx)(Z.gC,{className:"max-h-[300px] overflow-y-auto",children:eu.map(e=>(0,r.jsx)(Z.eb,{value:e,children:e},e))})]})]})]})]}),(0,r.jsxs)(b.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(b.aR,{children:(0,r.jsx)(b.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Pair"})}),(0,r.jsxs)(b.Wu,{className:"space-y-4",children:[(0,r.jsx)(en,{label:"Crypto 1 (Base)",value:t.crypto1,allowedCryptos:ep,onValidCrypto:e=>{if(a({type:"SET_CONFIG",payload:{crypto1:e}}),"StablecoinSwap"===t.tradingMode){let r=ep.filter(t=>t!==e);r.includes(t.crypto2)&&t.crypto2!==e||a({type:"SET_CONFIG",payload:{crypto2:r[0]}})}else em.includes(t.crypto2)||a({type:"SET_CONFIG",payload:{crypto2:em[0]}})},placeholder:"e.g., BTC, ETH, SOL",description:"Enter the base cryptocurrency symbol"}),(0,r.jsx)(en,{label:"StablecoinSwap"===t.tradingMode?"Crypto 2":"Crypto 2 (Stablecoin)",value:t.crypto2,allowedCryptos:"StablecoinSwap"===t.tradingMode?ep.filter(e=>e!==t.crypto1):em,onValidCrypto:e=>{("StablecoinSwap"!==t.tradingMode||e!==t.crypto1)&&a({type:"SET_CONFIG",payload:{crypto2:e}})},placeholder:"StablecoinSwap"===t.tradingMode?"e.g., BTC, ETH, SOL":"e.g., USDT, USDC, DAI",description:"StablecoinSwap"===t.tradingMode?"Enter the second cryptocurrency symbol":"Enter the quote/stablecoin symbol"})]})]}),(0,r.jsxs)(b.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(b.aR,{children:(0,r.jsx)(b.ZB,{className:"text-sidebar-accent-foreground",children:"Parameters"})}),(0,r.jsxs)(b.Wu,{className:"space-y-3",children:[[{name:"baseBid",label:"Base Bid (Crypto 2)",type:"number",step:"0.01"},{name:"multiplier",label:"Multiplier",type:"number",step:"0.001"},{name:"numDigits",label:"Display Digits",type:"number",step:"1"},{name:"slippagePercent",label:"Slippage %",type:"number",step:"0.01"}].map(e=>(0,r.jsxs)("div",{children:[(0,r.jsx)(C.J,{htmlFor:e.name,children:e.label}),(0,r.jsx)(S.p,{id:e.name,name:e.name,type:e.type,value:t[e.name]||"",onChange:x,step:e.step,min:"0"})]},e.name)),(0,r.jsxs)("div",{children:[(0,r.jsx)(C.J,{children:"Couple Income % Split (must sum to 100)"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(C.J,{htmlFor:"incomeSplitCrypto1Percent",className:"text-xs",children:[t.crypto1||"Crypto 1","%"]}),(0,r.jsx)(S.p,{id:"incomeSplitCrypto1Percent",type:"number",value:t.incomeSplitCrypto1Percent||"",onChange:e=>g("incomeSplitCrypto1Percent",e.target.value),min:"0",max:"100"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(C.J,{htmlFor:"incomeSplitCrypto2Percent",className:"text-xs",children:[t.crypto2||"Crypto 2","%"]}),(0,r.jsx)(S.p,{id:"incomeSplitCrypto2Percent",type:"number",value:t.incomeSplitCrypto2Percent||"",onChange:e=>g("incomeSplitCrypto2Percent",e.target.value),min:"0",max:"100"})]})]})]})]})]})]})}),(0,r.jsxs)("div",{className:"flex-shrink-0 mt-4",children:[(0,r.jsx)(X.w,{className:"mb-4 bg-sidebar-border"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:(0,q.cn)("text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",c?"bg-green-600 text-primary-foreground":"bg-muted text-muted-foreground"),children:[c?(0,r.jsx)(el,{className:"h-4 w-4"}):p?(0,r.jsx)(ei.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(eo,{className:"h-4 w-4"}),"Bot Status: ",c?"Running":p?"Warming Up":"Stopped"]}),(0,r.jsx)(d.$,{onClick:()=>f(!0),className:"w-full btn-outline-neo",children:"Set Target Prices"}),(0,r.jsxs)(d.$,{onClick:()=>{if(c)a({type:"SYSTEM_STOP_BOT"});else{if(!t.crypto1||!t.crypto2){m({title:"Cannot Start Bot",description:"Please select both Crypto 1 and Crypto 2 before starting the bot.",variant:"destructive",duration:3e3});return}if(0===i.length){m({title:"Cannot Start Bot",description:"Please set target prices before starting the bot.",variant:"destructive",duration:3e3});return}a({type:"SYSTEM_START_BOT_INITIATE"})}},className:(0,q.cn)("w-full btn-neo",c||p?"bg-destructive hover:bg-destructive/90":"bg-green-600 hover:bg-green-600/90"),disabled:p,children:[c?(0,r.jsx)(el,{className:"h-4 w-4"}):p?(0,r.jsx)(ei.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(eo,{className:"h-4 w-4"}),c?"Stop Bot":p?"Warming Up...":"Start Bot"]}),(0,r.jsxs)(d.$,{onClick:()=>{a({type:"SYSTEM_RESET_BOT"}),m({title:"Bot Reset",description:"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.",duration:4e3})},variant:"outline",className:"w-full btn-outline-neo",disabled:p,children:[(0,r.jsx)(ed,{className:"h-4 w-4 mr-2"}),"Reset Bot"]})]})]}),(0,r.jsx)(et,{isOpen:u,onClose:()=>f(!1),onSetTargetPrices:o})]})}function ex({children:e}){let{isAuthenticated:t,isLoading:a}=(0,c.A)(),s=(0,i.useRouter)();return a?(0,r.jsx)("div",{className:"flex items-center justify-center h-screen bg-background",children:(0,r.jsx)(ei.A,{className:"h-12 w-12 animate-spin text-primary"})}):t?(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-background text-foreground",children:[(0,r.jsx)(N,{}),(0,r.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,r.jsx)(ef,{}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8",children:e})]})]}):(s.push("/login"),null)}},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>d});var r=a(60687),s=a(43210),n=a(78148),l=a(24224),i=a(4780);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.b,{ref:a,className:(0,i.cn)(o(),e),...t}));d.displayName=n.b.displayName},85763:(e,t,a)=>{"use strict";a.d(t,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>i});var r=a(60687),s=a(43210),n=a(41360),l=a(4780);let i=n.bL,o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));o.displayName=n.B8.displayName;let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));d.displayName=n.l9.displayName;let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=n.UC.displayName},89667:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var r=a(60687),s=a(43210),n=a(4780);let l=s.forwardRef(({className:e,type:t,...a},s)=>(0,r.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...a}));l.displayName="Input"},96834:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var r=a(60687);a(43210);var s=a(24224),n=a(4780);let l=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...a}){return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:t}),e),...a})}}};