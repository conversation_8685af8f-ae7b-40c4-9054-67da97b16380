"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_lib_session-manager_ts",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nconst ACTIVE_SESSIONS_KEY = 'pluto_active_sessions'; // Track active sessions across all windows\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    loadActiveSessionsFromStorage() {\n        try {\n            if (false) {}\n            const activeSessionsData = localStorage.getItem(ACTIVE_SESSIONS_KEY);\n            if (activeSessionsData) {\n                const parsedActiveSessions = JSON.parse(activeSessionsData);\n                this.activeSessionsAcrossWindows = new Map(Object.entries(parsedActiveSessions));\n                // Clean up stale entries (older than 5 minutes)\n                const now = Date.now();\n                const staleThreshold = 5 * 60 * 1000; // 5 minutes\n                for (const [key, entry] of this.activeSessionsAcrossWindows.entries()){\n                    if (now - entry.timestamp > staleThreshold) {\n                        this.activeSessionsAcrossWindows.delete(key);\n                    }\n                }\n                this.saveActiveSessionsToStorage();\n            }\n        } catch (error) {\n            console.error('Failed to load active sessions from storage:', error);\n        }\n    }\n    saveActiveSessionsToStorage() {\n        try {\n            if (false) {}\n            const activeSessionsObject = Object.fromEntries(this.activeSessionsAcrossWindows);\n            localStorage.setItem(ACTIVE_SESSIONS_KEY, JSON.stringify(activeSessionsObject));\n            // Trigger storage event to notify other windows/tabs (including admin panel)\n            window.dispatchEvent(new StorageEvent('storage', {\n                key: ACTIVE_SESSIONS_KEY,\n                newValue: JSON.stringify(activeSessionsObject),\n                storageArea: localStorage\n            }));\n        } catch (error) {\n            console.error('Failed to save active sessions to storage:', error);\n        }\n    }\n    addActiveSession(sessionId) {\n        // Validate session ID before adding\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.warn('⚠️ Attempted to add invalid session ID to active sessions:', sessionId);\n            return;\n        }\n        const key = \"\".concat(this.windowId, \"_\").concat(sessionId);\n        this.activeSessionsAcrossWindows.set(key, {\n            sessionId,\n            windowId: this.windowId,\n            timestamp: Date.now()\n        });\n        this.saveActiveSessionsToStorage();\n    }\n    removeActiveSession(sessionId) {\n        const key = \"\".concat(this.windowId, \"_\").concat(sessionId);\n        this.activeSessionsAcrossWindows.delete(key);\n        this.saveActiveSessionsToStorage();\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    handleAppRestart() {\n        try {\n            console.log('🔄 Checking for app restart and cleaning up running sessions...');\n            // Check if this is a fresh app start by looking for a restart marker\n            const lastAppClose = localStorage.getItem('pluto_last_app_close');\n            const appStartTime = Date.now();\n            // If no close marker or it's been more than 5 minutes, consider it an app restart\n            const isAppRestart = !lastAppClose || appStartTime - parseInt(lastAppClose) > 5 * 60 * 1000;\n            if (isAppRestart) {\n                console.log('🔄 App restart detected - cleaning up running sessions');\n                // Find all sessions marked as active/running\n                const runningSessions = Array.from(this.sessions.values()).filter((session)=>session.isActive);\n                if (runningSessions.length > 0) {\n                    console.log(\"\\uD83D\\uDED1 Found \".concat(runningSessions.length, \" running sessions to clean up:\"), runningSessions.map((s)=>({\n                            id: s.id,\n                            name: s.name\n                        })));\n                    runningSessions.forEach((session)=>{\n                        try {\n                            // Create auto-saved version in past sessions\n                            const timestamp = new Date().toLocaleString('en-US', {\n                                month: 'short',\n                                day: 'numeric',\n                                hour: '2-digit',\n                                minute: '2-digit',\n                                hour12: false\n                            });\n                            const autoSavedName = \"\".concat(session.name, \" (AutoSaved \").concat(timestamp, \")\");\n                            // Create new session for the auto-saved version\n                            const autoSavedId = this.createNewSession(autoSavedName, session.config);\n                            // Save the auto-saved session with all progress as inactive\n                            if (typeof autoSavedId === 'string') {\n                                this.saveSession(autoSavedId, session.config, session.targetPriceRows, session.orderHistory, session.currentMarketPrice, session.crypto1Balance, session.crypto2Balance, session.stablecoinBalance, false // Mark as inactive (past session)\n                                );\n                            }\n                            // Mark original session as inactive\n                            session.isActive = false;\n                            session.lastModified = Date.now();\n                            this.sessions.set(session.id, session);\n                            console.log('✅ Auto-saved running session \"'.concat(session.name, '\" to past sessions as \"').concat(autoSavedName, '\"'));\n                        } catch (error) {\n                            console.error('❌ Failed to auto-save running session \"'.concat(session.name, '\":'), error);\n                            // At minimum, mark the session as inactive\n                            session.isActive = false;\n                            session.lastModified = Date.now();\n                            this.sessions.set(session.id, session);\n                        }\n                    });\n                    // Clear current session since all running sessions have been stopped\n                    this.currentSessionId = null;\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.removeItem(currentSessionKey);\n                    sessionStorage.removeItem(CURRENT_SESSION_KEY);\n                    // Save updated sessions\n                    this.saveSessionsToStorage();\n                    console.log('✅ App restart cleanup completed - all running sessions moved to past sessions');\n                } else {\n                    console.log('✅ No running sessions found - no cleanup needed');\n                }\n            } else {\n                console.log('✅ Normal app continuation - no cleanup needed');\n            }\n            // Set marker for next app close detection\n            localStorage.setItem('pluto_app_start', appStartTime.toString());\n        } catch (error) {\n            console.error('❌ Error during app restart cleanup:', error);\n        }\n    }\n    cleanupStalePersistenceInfo() {\n        try {\n            console.log('🧹 Cleaning up stale persistence info...');\n            const now = Date.now();\n            const staleThreshold = 24 * 60 * 60 * 1000; // 24 hours\n            // Get all localStorage keys that are persistence info\n            const keysToRemove = [];\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key && key.startsWith('pluto_session_persistence_')) {\n                    try {\n                        const persistenceInfo = JSON.parse(localStorage.getItem(key) || '{}');\n                        if (persistenceInfo.lastSaved && now - persistenceInfo.lastSaved > staleThreshold) {\n                            keysToRemove.push(key);\n                        }\n                    } catch (error) {\n                        // Invalid JSON, remove it\n                        keysToRemove.push(key);\n                    }\n                }\n            }\n            // Remove stale persistence info\n            keysToRemove.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed stale persistence info: \".concat(key));\n            });\n            if (keysToRemove.length > 0) {\n                console.log(\"✅ Cleaned up \".concat(keysToRemove.length, \" stale persistence entries\"));\n            } else {\n                console.log('✅ No stale persistence info found');\n            }\n        } catch (error) {\n            console.error('❌ Error cleaning up persistence info:', error);\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    return session.isActive && session.lastModified && Date.now() - session.lastModified < 30 * 60 * 1000 // Active within last 30 minutes\n                    ;\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>current[1].lastModified > latest[1].lastModified ? current : latest);\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n                // Ensure the current session is marked as active if it exists\n                const currentSession = this.sessions.get(currentSessionId);\n                if (currentSession && currentSession.isActive) {\n                    this.addActiveSession(currentSessionId);\n                    console.log(\"✅ Restored active session tracking for: \".concat(currentSessionId));\n                }\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session and restore it properly\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                    // Store in both localStorage and sessionStorage for persistence\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.setItem(currentSessionKey, activeSession.session_uuid);\n                    sessionStorage.setItem(CURRENT_SESSION_KEY, activeSession.session_uuid);\n                    console.log(\"✅ Restored active session from backend: \".concat(activeSession.session_uuid));\n                }\n                // Also save to localStorage as backup\n                this.saveSessionsToStorage();\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n            // Trigger storage event to notify other windows/tabs (including admin panel)\n            window.dispatchEvent(new StorageEvent('storage', {\n                key: SESSIONS_STORAGE_KEY,\n                newValue: JSON.stringify(sessionsObject),\n                storageArea: localStorage\n            }));\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Add persistence timestamp for recovery\n            const persistenceInfo = {\n                lastSaved: Date.now(),\n                windowId: this.windowId,\n                isActive,\n                sessionId\n            };\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            // Store persistence info for recovery\n            localStorage.setItem(\"pluto_session_persistence_\".concat(sessionId), JSON.stringify(persistenceInfo));\n            console.log(\"\\uD83D\\uDCBE Saving session with complete data:\", {\n                sessionId,\n                targetPriceRows: targetPriceRows.length,\n                orderHistory: orderHistory.length,\n                balances: {\n                    crypto1Balance,\n                    crypto2Balance,\n                    stablecoinBalance\n                },\n                isActive,\n                runtime: currentRuntime\n            });\n            this.sessions.set(sessionId, updatedSession);\n            // If session is active, ensure it's tracked in active sessions\n            if (isActive) {\n                this.addActiveSession(sessionId);\n                console.log(\"✅ Session marked as active and tracked: \".concat(sessionId));\n            } else {\n                this.removeActiveSession(sessionId);\n                console.log(\"⏹️ Session marked as inactive and removed from tracking: \".concat(sessionId));\n            }\n            // Save to backend only if explicitly authenticated\n            if (this.useBackend && \"object\" !== 'undefined') {\n                const token = localStorage.getItem('plutoAuthToken');\n                if (token && token.length > 10) {\n                    try {\n                        // Double-check authentication before making API call\n                        const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n                        const sessionData = {\n                            name: updatedSession.name,\n                            config: config,\n                            targetPriceRows: targetPriceRows,\n                            currentMarketPrice: currentMarketPrice,\n                            crypto1Balance: crypto1Balance,\n                            crypto2Balance: crypto2Balance,\n                            stablecoinBalance: stablecoinBalance,\n                            isActive: isActive,\n                            additionalRuntime: currentRuntime\n                        };\n                        await sessionApi.updateSession(sessionId, sessionData);\n                        console.log('✅ Session saved to backend:', sessionId);\n                    } catch (error) {\n                        const errorMessage = error instanceof Error ? error.message : String(error);\n                        console.warn('❌ Backend session save failed:', errorMessage);\n                        // Handle \"Session not found\" error by creating a new session\n                        if (errorMessage.includes('Session not found')) {\n                            console.log('🔄 Session not found in backend, creating new session...');\n                            try {\n                                // Create a new session in the backend with the same data\n                                const newSessionId = await this.createNewSession(updatedSession.name, config, {\n                                    crypto1: crypto1Balance,\n                                    crypto2: crypto2Balance,\n                                    stablecoin: stablecoinBalance\n                                });\n                                // Update the local session ID and session data\n                                this.sessions.set(newSessionId, {\n                                    ...updatedSession,\n                                    id: newSessionId\n                                });\n                                this.sessions.delete(sessionId); // Remove old session\n                                this.setCurrentSession(newSessionId); // Update current session ID\n                                console.log('✅ Created new session to replace missing one:', newSessionId);\n                                return true;\n                            } catch (createError) {\n                                console.error('❌ Failed to create replacement session:', createError);\n                            // Fall through to disable backend mode\n                            }\n                        }\n                        // Disable backend mode on any authentication-related error\n                        if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication') || errorMessage.includes('required')) {\n                            console.log('🔐 Disabling backend mode due to authentication issue');\n                            this.useBackend = false;\n                        }\n                    // Continue with localStorage save as fallback\n                    }\n                } else {\n                    console.log('⚠️ Invalid or missing auth token, skipping backend session save');\n                    this.useBackend = false; // Disable backend mode if no valid token\n                }\n            }\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    getCurrentActiveSessions() {\n        // Get all sessions that are currently active across all windows\n        const activeSessionIds = new Set(Array.from(this.activeSessionsAcrossWindows.values()).map((entry)=>entry.sessionId).filter((sessionId)=>sessionId && sessionId !== 'undefined') // Filter out invalid session IDs\n        );\n        // Also include current session if it exists (might not be in activeSessionsAcrossWindows yet)\n        if (this.currentSessionId && this.sessions.has(this.currentSessionId)) {\n            activeSessionIds.add(this.currentSessionId);\n        }\n        console.log('🔍 getCurrentActiveSessions debug:', {\n            activeSessionsAcrossWindows: Array.from(this.activeSessionsAcrossWindows.entries()),\n            activeSessionIds: Array.from(activeSessionIds),\n            allSessions: Array.from(this.sessions.keys()),\n            currentSessionId: this.currentSessionId,\n            windowId: this.windowId\n        });\n        // Get sessions that are either in active tracking OR marked as active in their data\n        const allActiveSessions = Array.from(this.sessions.values()).filter((session)=>{\n            const isInActiveTracking = session.id && activeSessionIds.has(session.id);\n            const isMarkedActive = session.isActive;\n            const isCurrentSession = session.id === this.currentSessionId;\n            console.log(\"\\uD83D\\uDD0D Session \".concat(session.id, \" (\").concat(session.name, \"):\"), {\n                isInActiveTracking,\n                isMarkedActive,\n                isCurrentSession,\n                shouldInclude: isInActiveTracking || isMarkedActive || isCurrentSession\n            });\n            return isInActiveTracking || isMarkedActive || isCurrentSession;\n        }).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: true,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            })).filter((session)=>session.id); // Ensure all returned sessions have valid IDs\n        console.log('🔍 Returning active sessions:', allActiveSessions.length, allActiveSessions.map((s)=>({\n                id: s.id,\n                name: s.name\n            })));\n        return allActiveSessions;\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            // Allow multiple sessions to be active simultaneously (for different tabs)\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                // Track this session as active across windows\n                this.addActiveSession(sessionId);\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n                // Trigger storage event to notify admin panel about current session change\n                window.dispatchEvent(new StorageEvent('storage', {\n                    key: 'pluto_current_session',\n                    newValue: sessionId,\n                    storageArea: localStorage\n                }));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Remove session from active tracking and mark as inactive only if no other windows are using it\n        if (this.currentSessionId) {\n            this.removeActiveSession(this.currentSessionId);\n            // Check if any other windows are still using this session\n            const isStillActiveInOtherWindows = Array.from(this.activeSessionsAcrossWindows.values()).some((entry)=>entry.sessionId === this.currentSessionId);\n            if (!isStillActiveInOtherWindows) {\n                const session = this.sessions.get(this.currentSessionId);\n                if (session && session.isActive) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive (no other windows using it)\"));\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD04 Session \".concat(this.currentSessionId, \" still active in other windows\"));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.activeSessionsAcrossWindows = new Map();\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.loadActiveSessionsFromStorage();\n        this.setupStorageListener();\n        // Handle app restart - stop any running sessions and move to past sessions\n        this.handleAppRestart();\n        // Clean up stale persistence info\n        this.cleanupStalePersistenceInfo();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});