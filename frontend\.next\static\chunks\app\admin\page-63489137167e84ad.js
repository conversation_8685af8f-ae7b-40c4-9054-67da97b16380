(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{2136:(e,s,t)=>{Promise.resolve().then(t.bind(t,6360))},6360:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>K});var r=t(5155),a=t(2115),n=t(5695),i=t(7313),o=t(285),l=t(2523),d=t(5057),c=t(6695),u=t(4884),m=t(9434);let h=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(u.bL,{className:(0,m.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...a,ref:s,children:(0,r.jsx)(u.zi,{className:(0,m.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});h.displayName=u.bL.displayName;var x=t(7213),g=t(7481),v=t(6424),p=t(8271),j=t(3576),b=t(3503),f=t(5300),y=t(8186),S=t(4607),N=t(7607),E=t(6126),C=t(207),w=t(7554),A=t(9119),k=t(3129),T=t(620),O=t(2773),D=t(8718),_=t(7648),I=t(7223),B=t(4553),R=t(4165),P=t(9409),F=t(9348);let U=[{value:"/sounds/trade_success.mp3",label:"Default Success",category:"default"},{value:"/sounds/trade_error.mp3",label:"Default Error",category:"default"},{value:"/sounds/bell.mp3",label:"Bell",category:"default"},{value:"/sounds/chime.mp3",label:"Chime",category:"default"},{value:"/sounds/alert.mp3",label:"Alert",category:"default"},{value:"/sounds/warning.mp3",label:"Warning",category:"default"}],$=[{value:"/sounds/G_hades_curse.wav",label:"Hades Curse",category:"uploaded"},{value:"/sounds/G_hades_demat.wav",label:"Hades Demat",category:"uploaded"},{value:"/sounds/G_hades_mat.wav",label:"Hades Mat",category:"uploaded"},{value:"/sounds/G_hades_sanctify.wav",label:"Hades Sanctify",category:"uploaded"},{value:"/sounds/S_mon1.mp3",label:"Monster 1",category:"uploaded"},{value:"/sounds/S_mon2.mp3",label:"Monster 2",category:"uploaded"},{value:"/sounds/Satyr_atk4.wav",label:"Satyr Attack",category:"uploaded"},{value:"/sounds/bells.wav",label:"Bells",category:"uploaded"},{value:"/sounds/bird1.wav",label:"Bird 1",category:"uploaded"},{value:"/sounds/bird7.wav",label:"Bird 7",category:"uploaded"},{value:"/sounds/cheer.wav",label:"Cheer",category:"uploaded"},{value:"/sounds/chest1.wav",label:"Chest",category:"uploaded"},{value:"/sounds/chime2.wav",label:"Chime 2",category:"uploaded"},{value:"/sounds/dark2.wav",label:"Dark",category:"uploaded"},{value:"/sounds/foundry2.wav",label:"Foundry",category:"uploaded"},{value:"/sounds/goatherd1.wav",label:"Goatherd",category:"uploaded"},{value:"/sounds/marble1.wav",label:"Marble",category:"uploaded"},{value:"/sounds/sanctuary1.wav",label:"Sanctuary",category:"uploaded"},{value:"/sounds/space_bells4a.wav",label:"Space Bells",category:"uploaded"},{value:"/sounds/sparrow1.wav",label:"Sparrow",category:"uploaded"},{value:"/sounds/tax3.wav",label:"Tax",category:"uploaded"},{value:"/sounds/wolf4.wav",label:"Wolf",category:"uploaded"}],M="custom_sound_execution",L="custom_sound_error";function z(e){let{isOpen:s,onClose:t,sessionId:n,sessionName:i,currentAlarmSettings:c,onSave:u}=e,[x,v]=(0,a.useState)(c||F.Oh),p=(0,a.useRef)(null),{toast:j}=(0,g.dj)();(0,a.useEffect)(()=>{var e,s;v({...c||F.Oh,customSoundOrderExecutionDataUri:(null==c?void 0:null===(e=c.soundOrderExecution)||void 0===e?void 0:e.startsWith("data:audio"))?c.soundOrderExecution:void 0,customSoundErrorDataUri:(null==c?void 0:null===(s=c.soundError)||void 0===s?void 0:s.startsWith("data:audio"))?c.soundError:void 0})},[c,s]),(0,a.useEffect)(()=>{p.current=new Audio},[]);let b=(e,s)=>{v(t=>({...t,[e]:s}))},f=(e,s)=>{v(t=>({...t,[e]:s}))},y=(e,s)=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];if(r){let e=new FileReader;e.onload=e=>{var t;let a=null===(t=e.target)||void 0===t?void 0:t.result;"orderExecution"===s?v(e=>({...e,customSoundOrderExecutionDataUri:a,soundOrderExecution:M})):v(e=>({...e,customSoundErrorDataUri:a,soundError:L})),j({title:"File Uploaded",description:"".concat(r.name," ready to be used.")})},e.readAsDataURL(r)}},S=e=>{let s=x[e];if("soundOrderExecution"===e&&x.soundOrderExecution===M?s=x.customSoundOrderExecutionDataUri:"soundError"===e&&x.soundError===L&&(s=x.customSoundErrorDataUri),p.current&&s){p.current.pause(),p.current.currentTime=0,p.current.src=s;let e=p.current.play();void 0!==e&&e.then(()=>{setTimeout(()=>{p.current&&!p.current.paused&&(p.current.pause(),p.current.currentTime=0)},2e3)}).catch(e=>{e.message.includes("interrupted")||(console.error("Error playing sound:",e),j({title:"Sound Error",description:"Could not play test sound. Ensure file is valid or browser permissions are set.",variant:"destructive"}))})}else j({title:"No Sound",description:"No sound selected or sound file not available.",variant:"default"})},N=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],s=[...U.filter(e=>e.value.includes("success")||e.value.includes("bell")||e.value.includes("chime")),...$];return e&&s.push({value:"custom_sound_execution",label:"Upload Custom",category:"default"}),s}(!0),E=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],s=[...U.filter(e=>e.value.includes("error")||e.value.includes("alert")||e.value.includes("warning")),...$];return e&&s.push({value:"custom_sound_error",label:"Upload Custom",category:"default"}),s}(!0);return(0,r.jsx)(R.lG,{open:s,onOpenChange:t,children:(0,r.jsxs)(R.Cf,{className:"sm:max-w-md bg-card border-2 border-border",children:[(0,r.jsxs)(R.c7,{children:[(0,r.jsx)(R.L3,{className:"text-primary",children:"Session Alarm Configuration"}),(0,r.jsxs)(R.rr,{children:["Configure sound alerts for session: ",(0,r.jsx)("strong",{children:i})]})]}),(0,r.jsxs)("div",{className:"space-y-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(d.J,{htmlFor:"soundAlertsEnabled",className:"text-base",children:"Enable Sound Alerts"}),(0,r.jsx)(h,{id:"soundAlertsEnabled",checked:!!x.soundAlertsEnabled,onCheckedChange:e=>b("soundAlertsEnabled",e)})]}),x.soundAlertsEnabled&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"space-y-3 p-4 border-2 border-border rounded-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(d.J,{htmlFor:"alertOnOrderExecution",children:"Alert on Successful Order Execution"}),(0,r.jsx)(h,{id:"alertOnOrderExecution",checked:!!x.alertOnOrderExecution,onCheckedChange:e=>b("alertOnOrderExecution",e)})]}),x.alertOnOrderExecution&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(P.l6,{value:x.soundOrderExecution,onValueChange:e=>f("soundOrderExecution",e),children:[(0,r.jsx)(P.bq,{className:"flex-grow",children:(0,r.jsx)(P.yv,{placeholder:"Select sound"})}),(0,r.jsx)(P.gC,{children:N.map(e=>(0,r.jsx)(P.eb,{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>S("soundOrderExecution"),className:"btn-outline-neo p-2",children:(0,r.jsx)(T.A,{className:"h-4 w-4"})})]}),x.soundOrderExecution===M&&(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"customSoundExecutionFile",className:"text-xs",children:"Upload Execution Sound (.mp3, .wav, etc.)"}),(0,r.jsx)(l.p,{id:"customSoundExecutionFile",type:"file",accept:"audio/*",onChange:e=>y(e,"orderExecution"),className:(0,m.cn)("text-xs mt-1","focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}),x.customSoundOrderExecutionDataUri&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground truncate mt-1",children:"Current: Custom sound uploaded"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3 p-4 border-2 border-border rounded-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(d.J,{htmlFor:"alertOnError",children:"Alert on Errors/Failures"}),(0,r.jsx)(h,{id:"alertOnError",checked:!!x.alertOnError,onCheckedChange:e=>b("alertOnError",e)})]}),x.alertOnError&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(P.l6,{value:x.soundError,onValueChange:e=>f("soundError",e),children:[(0,r.jsx)(P.bq,{className:"flex-grow",children:(0,r.jsx)(P.yv,{placeholder:"Select sound"})}),(0,r.jsx)(P.gC,{children:E.map(e=>(0,r.jsx)(P.eb,{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>S("soundError"),className:"btn-outline-neo p-2",children:(0,r.jsx)(T.A,{className:"h-4 w-4"})})]}),x.soundError===L&&(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"customSoundErrorFile",className:"text-xs",children:"Upload Error Sound (.mp3, .wav, etc.)"}),(0,r.jsx)(l.p,{id:"customSoundErrorFile",type:"file",accept:"audio/*",onChange:e=>y(e,"error"),className:(0,m.cn)("text-xs mt-1","focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:border-primary")}),x.customSoundErrorDataUri&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground truncate mt-1",children:"Current: Custom sound uploaded"})]})]})]})]})]}),(0,r.jsxs)(R.Es,{children:[(0,r.jsx)(R.HM,{asChild:!0,children:(0,r.jsx)(o.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,r.jsx)(o.$,{type:"button",onClick:()=>{let e={...F.Oh,...x};x.soundOrderExecution===M&&x.customSoundOrderExecutionDataUri?e.soundOrderExecution=x.customSoundOrderExecutionDataUri:x.soundOrderExecution!==M||x.customSoundOrderExecutionDataUri||(e.soundOrderExecution=F.Oh.soundOrderExecution,j({title:"Notice",description:"No custom execution sound uploaded, default used.",variant:"default"})),x.soundError===L&&x.customSoundErrorDataUri?e.soundError=x.customSoundErrorDataUri:x.soundError!==L||x.customSoundErrorDataUri||(e.soundError=F.Oh.soundError,j({title:"Notice",description:"No custom error sound uploaded, default used.",variant:"default"})),u(n,e),j({title:"Session Alarm Settings Saved",description:"Alarm preferences updated for ".concat(i)}),t()},className:"btn-neo",children:"Save Settings"})]})]})})}function H(){let{config:e,targetPriceRows:s,orderHistory:t,currentMarketPrice:n,crypto1Balance:i,crypto2Balance:d,stablecoinBalance:u,botSystemStatus:m,dispatch:h}=(0,x.U)(),{toast:p}=(0,g.dj)(),[j,b]=(0,a.useState)([]),[y,S]=(0,a.useState)(null),[N,R]=(0,a.useState)(null),[P,F]=(0,a.useState)(""),[U,$]=(0,a.useState)(0),[M,L]=(0,a.useState)(!1),[H,K]=(0,a.useState)(null),W=B.SessionManager.getInstance();(0,a.useEffect)(()=>{J();let e=W.getCurrentSessionId();S(e),console.log("\uD83D\uDD04 Admin Panel - Initial load:",{sessionId:e,totalSessions:W.getAllSessions().length,activeSessions:W.getCurrentActiveSessions().length}),e&&$(W.getCurrentRuntime(e));let s=e=>{var s,t,r;if(console.log("\uD83D\uDD04 Admin Panel - Storage change detected:",{key:e.key,hasNewValue:!!e.newValue}),"pluto_trading_sessions"===e.key&&e.newValue&&(J(),console.log("\uD83D\uDD04 Sessions synced from another window")),(null===(s=e.key)||void 0===s?void 0:s.includes("active_sessions"))&&(J(),console.log("\uD83D\uDD04 Active sessions updated from another window")),null===(t=e.key)||void 0===t?void 0:t.includes("current_session")){let e=W.getCurrentSessionId();S(e),J(),console.log("\uD83D\uDD04 Current session changed:",e)}(null===(r=e.key)||void 0===r?void 0:r.includes("pluto_"))&&(J(),console.log("\uD83D\uDD04 Pluto storage updated, refreshing sessions"))};window.addEventListener("storage",s);let t=setInterval(()=>{J(),S(W.getCurrentSessionId())},5e3);return()=>{window.removeEventListener("storage",s),clearInterval(t)}},[]),(0,a.useEffect)(()=>{let e=setInterval(()=>{y&&$(W.getCurrentRuntime(y)),J()},2e3);return()=>clearInterval(e)},[y,W]);let J=()=>{let e=W.getAllSessions();console.log("\uD83D\uDD04 Admin Panel - Loading sessions:",{totalSessions:e.length,currentSessionId:W.getCurrentSessionId(),sessions:e.map(e=>({id:e.id,name:e.name,isActive:e.isActive}))}),b(e.sort((e,s)=>s.lastModified-e.lastModified))},Z=async()=>{if(!y){p({title:"Error",description:"No active session to save",variant:"destructive"});return}try{let r,a;let o=W.loadSession(y);if(!o){p({title:"Error",description:"Current session not found",variant:"destructive"});return}let l=W.getAllSessions(),c=o.name.replace(/ \((Saved|AutoSaved).*\)$/,""),m=l.find(e=>e.id!==y&&e.name.startsWith(c)&&e.name.includes("(Saved")&&!e.isActive);if(m){r=m.id;let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});a="".concat(c," (Saved ").concat(e,")"),console.log("\uD83D\uDCDD Updating existing saved session: ".concat(a))}else{let s=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});a="".concat(c," (Saved ").concat(s,")"),r=await W.createNewSession(a,e),console.log("\uD83D\uDCBE Creating new saved session: ".concat(a))}let h=W.getCurrentRuntime(y);m&&W.renameSession(r,a),await W.saveSession(r,e,s,t,n,i,d,u,!1,h)?(J(),p({title:"Session Saved",description:m?"Save checkpoint updated (Runtime: ".concat(ee(h),")"):"Session saved as checkpoint (Runtime: ".concat(ee(h),")")})):p({title:"Error",description:"Failed to save session",variant:"destructive"})}catch(e){console.error("Error saving session:",e),p({title:"Error",description:"Failed to save session",variant:"destructive"})}},V=e=>{try{var s,t,r,a;console.log("\uD83D\uDD04 Attempting to load session:",e);let n=W.loadSession(e);if(!n){console.error("❌ Session not found:",e),console.log("Available sessions:",W.getAllSessions().map(e=>({id:e.id,name:e.name}))),p({title:"Error",description:"Failed to load session - session not found",variant:"destructive"});return}if(console.log("\uD83D\uDD04 Loading session with full data:",{sessionId:e,name:n.name,config:n.config,targetPriceRows:(null===(s=n.targetPriceRows)||void 0===s?void 0:s.length)||0,orderHistory:(null===(t=n.orderHistory)||void 0===t?void 0:t.length)||0,balances:{crypto1:n.crypto1Balance,crypto2:n.crypto2Balance,stablecoin:n.stablecoinBalance},isActive:n.isActive,runtime:n.runtime}),!n.config||!n.config.crypto1||!n.config.crypto2){console.error("❌ Invalid session config:",n.config),p({title:"Error",description:"Session has invalid configuration",variant:"destructive"});return}console.log("\uD83D\uDCDD Setting config:",n.config),h({type:"SET_CONFIG",payload:n.config}),console.log("\uD83D\uDCDD Setting target price rows:",(null===(r=n.targetPriceRows)||void 0===r?void 0:r.length)||0),h({type:"SET_TARGET_PRICE_ROWS",payload:n.targetPriceRows||[]}),console.log("\uD83D\uDCDD Clearing and loading order history:",(null===(a=n.orderHistory)||void 0===a?void 0:a.length)||0),h({type:"CLEAR_ORDER_HISTORY"}),n.orderHistory&&n.orderHistory.length>0&&n.orderHistory.forEach(e=>{h({type:"ADD_ORDER_HISTORY_ENTRY",payload:e})}),console.log("\uD83D\uDCDD Setting market price:",n.currentMarketPrice),h({type:"SET_MARKET_PRICE",payload:n.currentMarketPrice||1e5}),console.log("\uD83D\uDCDD Setting balances:",{crypto1:n.crypto1Balance,crypto2:n.crypto2Balance,stablecoin:n.stablecoinBalance}),h({type:"SET_BALANCES",payload:{crypto1:n.crypto1Balance||1e4,crypto2:n.crypto2Balance||1e4,stablecoin:n.stablecoinBalance||1e4}}),W.setCurrentSession(e),S(e),console.log("\uD83D\uDCDD Setting bot status to Stopped (loaded sessions always start stopped)"),h({type:"SYSTEM_STOP_BOT"}),W.saveSession(e,n.config,n.targetPriceRows||[],n.orderHistory||[],n.currentMarketPrice||1e5,n.crypto1Balance||1e4,n.crypto2Balance||1e4,n.stablecoinBalance||1e4,!1),J(),p({title:"Session Loaded",description:'Session "'.concat(n.name,'" has been loaded successfully')}),console.log("\uD83D\uDD04 Navigating to dashboard..."),setTimeout(()=>{window.location.href="/dashboard"},1e3)}catch(e){console.error("❌ Error loading session:",e),p({title:"Error",description:"Failed to load session: ".concat(e instanceof Error?e.message:"Unknown error"),variant:"destructive"})}},Y=async e=>{await W.deleteSession(e)&&(y===e&&S(null),J(),p({title:"Session Deleted",description:"Session has been deleted successfully"}))},G=e=>{P.trim()&&W.renameSession(e,P.trim())&&(R(null),F(""),J(),p({title:"Session Renamed",description:"Session has been renamed successfully"}))},X=e=>{let s=W.loadSession(e);s&&(K({id:e,name:s.name,alarmSettings:s.alarmSettings}),L(!0))},Q=async(e,s)=>{await W.updateSessionAlarmSettings(e,s)?(J(),p({title:"Alarm Settings Saved",description:"Session alarm settings have been updated"})):p({title:"Error",description:"Failed to save alarm settings",variant:"destructive"})},q=e=>{let s=W.exportSessionToCSV(e);if(!s){p({title:"Error",description:"Failed to export session",variant:"destructive"});return}let t=W.loadSession(e),r=new Blob([s],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),n=URL.createObjectURL(r);a.setAttribute("href",n),a.setAttribute("download","".concat((null==t?void 0:t.name)||"session","_").concat(new Date().toISOString().split("T")[0],".csv")),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a),p({title:"Export Complete",description:"Session data has been exported to CSV"})},ee=e=>{if(!e||e<0)return"0s";let s=Math.floor(e/1e3),t=Math.floor(s/3600),r=Math.floor(s%3600/60),a=s%60;return t>0?"".concat(t,"h ").concat(r,"m ").concat(a,"s"):r>0?"".concat(r,"m ").concat(a,"s"):"".concat(a,"s")},es=()=>{let e=W.getCurrentSessionId(),s=W.getCurrentActiveSessions(),t=j.filter(e=>e.isActive),r=null;if(e&&!(r=j.find(s=>s.id===e))){let s=W.loadSession(e);s&&s.isActive&&(r={id:s.id,name:s.name,pair:"".concat(s.config.crypto1,"/").concat(s.config.crypto2),createdAt:s.createdAt,lastModified:s.lastModified,isActive:s.isActive,runtime:W.getCurrentRuntime(s.id),totalTrades:s.orderHistory.length,totalProfitLoss:s.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,s)=>e+(s.realizedProfitLossCrypto2||0),0)})}let a=new Map;s.forEach(e=>{a.set(e.id,e)}),t.forEach(e=>{a.set(e.id,e)}),r&&a.set(r.id,r);let n=Array.from(a.values());return console.log("\uD83D\uDD0D Admin Panel - Active Sessions (Enhanced):",{currentSessionId:e,trackedCount:s.length,markedCount:t.length,hasCurrentSessionData:!!r,finalCount:n.length,sessions:n.map(e=>({id:e.id,name:e.name,isActive:e.isActive,runtime:e.runtime}))}),n},et=()=>j.filter(e=>!e.isActive);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsxs)(c.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(C.A,{className:"h-5 w-5"}),"Current Sessions"]}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>{console.log("\uD83D\uDD04 Manual refresh triggered"),J()},title:"Refresh Sessions",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})}),(0,r.jsx)(c.Wu,{children:es().length>0?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,r.jsx)("div",{children:"Session Name"}),(0,r.jsx)("div",{children:"Active Status"}),(0,r.jsx)("div",{children:"Runtime"}),(0,r.jsx)("div",{children:"Alarm"}),(0,r.jsx)("div",{children:"Actions"})]}),(0,r.jsx)("div",{className:"space-y-2",children:es().map(e=>(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2",children:N===e.id?(0,r.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,r.jsx)(l.p,{value:P,onChange:e=>F(e.target.value),onKeyPress:s=>"Enter"===s.key&&G(e.id),className:"text-sm"}),(0,r.jsx)(o.$,{size:"sm",onClick:()=>G(e.id),children:(0,r.jsx)(A.A,{className:"h-3 w-3"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"font-medium",children:e.name}),(0,r.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>{R(e.id),F(e.name)},children:(0,r.jsx)(k.A,{className:"h-3 w-3"})})]})}),(0,r.jsx)("div",{children:(0,r.jsx)(E.E,{variant:"default",children:"Active"})}),(0,r.jsx)("div",{className:"text-sm",children:e.id===y?ee(U):ee(e.runtime)}),(0,r.jsx)("div",{children:(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>X(e.id),title:"Configure Alarms",className:"btn-outline-neo",children:(0,r.jsx)(T.A,{className:"h-3 w-3"})})}),(0,r.jsx)("div",{children:e.id===y?(0,r.jsxs)(o.$,{onClick:Z,size:"sm",className:"btn-neo",children:[(0,r.jsx)(A.A,{className:"mr-2 h-3 w-3"}),"Save"]}):(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>V(e.id),title:"Load Session",children:(0,r.jsx)(O.A,{className:"h-3 w-3"})}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>q(e.id),title:"Export Session",children:(0,r.jsx)(D.A,{className:"h-3 w-3"})})]})})]},e.id))})]}):(0,r.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,r.jsx)(C.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No active session"}),(0,r.jsx)("p",{className:"text-xs",children:"Start trading to create a session automatically"})]})})]}),(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"h-5 w-5"}),"Past Sessions (",et().length,")"]}),(0,r.jsxs)(c.BT,{children:["Auto-saved: ",et().length," | Manual: 0"]})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(v.F,{className:"h-[400px]",children:0===et().length?(0,r.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,r.jsx)(_.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No saved sessions yet."}),(0,r.jsx)("p",{className:"text-xs",children:"Save your current session to get started."})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,r.jsx)("div",{children:"Session Name"}),(0,r.jsx)("div",{children:"Active Status"}),(0,r.jsx)("div",{children:"Total Runtime"}),(0,r.jsx)("div",{children:"Alarm"}),(0,r.jsx)("div",{children:"Actions"})]}),(0,r.jsx)("div",{className:"space-y-2",children:et().map(e=>(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2",children:N===e.id?(0,r.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,r.jsx)(l.p,{value:P,onChange:e=>F(e.target.value),onKeyPress:s=>"Enter"===s.key&&G(e.id),className:"text-sm"}),(0,r.jsx)(o.$,{size:"sm",onClick:()=>G(e.id),children:(0,r.jsx)(A.A,{className:"h-3 w-3"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"font-medium",children:e.name}),(0,r.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>{R(e.id),F(e.name)},children:(0,r.jsx)(k.A,{className:"h-3 w-3"})})]})}),(0,r.jsx)("div",{children:(0,r.jsx)(E.E,{variant:"secondary",children:"Inactive"})}),(0,r.jsx)("div",{className:"text-sm",children:ee(W.getCurrentRuntime(e.id))}),(0,r.jsx)("div",{children:(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>X(e.id),title:"Configure Alarms",className:"btn-outline-neo",children:(0,r.jsx)(T.A,{className:"h-3 w-3"})})}),(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>V(e.id),title:"Load Session",children:(0,r.jsx)(O.A,{className:"h-3 w-3"})}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>q(e.id),title:"Export Session",children:(0,r.jsx)(D.A,{className:"h-3 w-3"})}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>Y(e.id),title:"Delete Session",children:(0,r.jsx)(I.A,{className:"h-3 w-3"})})]})]},e.id))})]})})})]}),H&&(0,r.jsx)(z,{isOpen:M,onClose:()=>{L(!1),K(null)},sessionId:H.id,sessionName:H.name,currentAlarmSettings:H.alarmSettings,onSave:Q})]})}function K(){let{botSystemStatus:e}=(0,x.U)(),[s,t]=(0,a.useState)("iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA"),[u,m]=(0,a.useState)("jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp"),[E,C]=(0,a.useState)(!1),[w,A]=(0,a.useState)(!1),[k,T]=(0,a.useState)(""),[O,D]=(0,a.useState)(""),{toast:_}=(0,g.dj)(),I=(0,n.useRouter)(),B=async()=>{try{localStorage.setItem("binance_api_key",s),localStorage.setItem("binance_api_secret",u),console.log("API Keys Saved:",{apiKey:s.substring(0,10)+"...",apiSecret:u.substring(0,10)+"..."}),_({title:"API Keys Saved",description:"Binance API keys have been saved securely."})}catch(e){_({title:"Error",description:"Failed to save API keys.",variant:"destructive"})}},R=async()=>{try{(await fetch("https://api.binance.com/api/v3/ping")).ok?_({title:"API Connection Test",description:"Successfully connected to Binance API!"}):_({title:"Connection Failed",description:"Unable to connect to Binance API.",variant:"destructive"})}catch(e){_({title:"Connection Error",description:"Network error while testing API connection.",variant:"destructive"})}},P=async()=>{if(!k||!O){_({title:"Missing Configuration",description:"Please enter both Telegram bot token and chat ID.",variant:"destructive"});return}try{(await fetch("https://api.telegram.org/bot".concat(k,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:O,text:"\uD83E\uDD16 Test message from Pluto Trading Bot! Your Telegram integration is working correctly."})})).ok?_({title:"Telegram Test Successful",description:"Test message sent successfully!"}):_({title:"Telegram Test Failed",description:"Failed to send test message. Check your token and chat ID.",variant:"destructive"})}catch(e){_({title:"Telegram Error",description:"Network error while testing Telegram integration.",variant:"destructive"})}},F=[{value:"systemTools",label:"System Tools",icon:(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4"})},{value:"apiKeys",label:"Exchange API Keys",icon:(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4"})},{value:"telegram",label:"Telegram Integration",icon:(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"})},{value:"sessionManager",label:"Session Manager",icon:(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4"})}];return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsxs)(c.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(c.aR,{className:"flex flex-row justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.ZB,{className:"text-3xl font-bold text-primary",children:"Admin Panel"}),(0,r.jsx)(c.BT,{children:"Manage global settings and tools for Pluto Trading Bot."})]}),(0,r.jsxs)(o.$,{variant:"outline",onClick:()=>I.push("/dashboard"),className:"btn-outline-neo",children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Return to Dashboard"]})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)(i.tU,{defaultValue:"systemTools",className:"w-full",children:[(0,r.jsx)(v.F,{className:"pb-2",children:(0,r.jsx)(i.j7,{className:"bg-card border-border border-2 p-1",children:F.map(e=>(0,r.jsx)(i.Xi,{value:e.value,className:"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:(0,r.jsxs)("div",{className:"flex items-center",children:[e.icon," ",e.label]})},e.value))})}),(0,r.jsx)(i.av,{value:"systemTools",className:"mt-6",children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"System Tools"})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation)."}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>_({title:"DB Editor Clicked"}),children:"View Database (Read-Only)"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>_({title:"Export Orders Clicked"}),children:"Export Orders to Excel"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>_({title:"Export History Clicked"}),children:"Export History to Excel"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>_({title:"Backup DB Clicked"}),children:"Backup Database"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>_({title:"Restore DB Clicked"}),disabled:!0,children:"Restore Database"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>_({title:"Diagnostics Clicked"}),children:"Run System Diagnostics"})]})]})]})})}),(0,r.jsx)(i.av,{value:"apiKeys",className:"mt-6",children:(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Exchange API Keys (Binance)"})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure your Binance API keys for real trading. Keys are stored securely in browser storage."}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"apiKey",children:"API Key"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.p,{id:"apiKey",type:E?"text":"password",value:s,onChange:e=>t(e.target.value),placeholder:"Enter your Binance API key",className:"pr-10"}),(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C(!E),children:E?(0,r.jsx)(S.A,{className:"h-4 w-4"}):(0,r.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"apiSecret",children:"API Secret"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.p,{id:"apiSecret",type:w?"text":"password",value:u,onChange:e=>m(e.target.value),placeholder:"Enter your Binance API secret",className:"pr-10"}),(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>A(!w),children:w?(0,r.jsx)(S.A,{className:"h-4 w-4"}):(0,r.jsx)(N.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(o.$,{onClick:B,className:"btn-neo",children:"Save API Keys"}),(0,r.jsx)(o.$,{onClick:R,variant:"outline",className:"btn-outline-neo",children:"Test Connection"})]})]})]})}),(0,r.jsx)(i.av,{value:"telegram",className:"mt-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Telegram Configuration"})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure Telegram bot for real-time trading notifications."}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"telegramToken",children:"Telegram Bot Token"}),(0,r.jsx)(l.p,{id:"telegramToken",type:"password",value:k,onChange:e=>T(e.target.value),placeholder:"Enter your Telegram bot token"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(d.J,{htmlFor:"telegramChatId",children:"Telegram Chat ID"}),(0,r.jsx)(l.p,{id:"telegramChatId",value:O,onChange:e=>D(e.target.value),placeholder:"Enter your Telegram chat ID"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h,{id:"notifyOnOrder"}),(0,r.jsx)(d.J,{htmlFor:"notifyOnOrder",children:"Notify on Order Execution"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h,{id:"notifyOnErrors"}),(0,r.jsx)(d.J,{htmlFor:"notifyOnErrors",children:"Notify on Errors"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(o.$,{onClick:()=>{try{localStorage.setItem("telegram_bot_token",k),localStorage.setItem("telegram_chat_id",O),console.log("Telegram Config Saved:",{telegramToken:k.substring(0,10)+"...",telegramChatId:O}),_({title:"Telegram Config Saved",description:"Telegram settings have been saved successfully."})}catch(e){_({title:"Error",description:"Failed to save Telegram configuration.",variant:"destructive"})}},className:"btn-neo",children:"Save Telegram Config"}),(0,r.jsx)(o.$,{onClick:P,variant:"outline",className:"btn-outline-neo",children:"Test Telegram"})]})]})]}),(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Setup Guide"})}),(0,r.jsx)(c.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 1: Create a Telegram Bot"}),(0,r.jsxs)("ol",{className:"text-sm space-y-1 list-decimal list-inside text-muted-foreground",children:[(0,r.jsxs)("li",{children:["Open Telegram and search for ",(0,r.jsx)("code",{className:"bg-muted px-1 rounded",children:"@BotFather"})]}),(0,r.jsxs)("li",{children:["Send ",(0,r.jsx)("code",{className:"bg-muted px-1 rounded",children:"/newbot"})," command"]}),(0,r.jsx)("li",{children:'Choose a name for your bot (e.g., "My Trading Bot")'}),(0,r.jsx)("li",{children:'Choose a username ending with "bot" (e.g., "mytradingbot")'}),(0,r.jsx)("li",{children:"Copy the bot token provided by BotFather"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 2: Get Your Chat ID"}),(0,r.jsxs)("ol",{className:"text-sm space-y-1 list-decimal list-inside text-muted-foreground",children:[(0,r.jsx)("li",{children:"Start a chat with your new bot"}),(0,r.jsx)("li",{children:"Send any message to the bot"}),(0,r.jsxs)("li",{children:["Visit: ",(0,r.jsx)("code",{className:"bg-muted px-1 rounded text-xs",children:"https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates"})]}),(0,r.jsx)("li",{children:'Look for "chat" and "id" fields in the response'}),(0,r.jsx)("li",{children:"Copy the chat ID number"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-yellow-600 mb-2",children:"Step 3: Configure Bot"}),(0,r.jsxs)("ul",{className:"text-sm space-y-1 list-disc list-inside text-muted-foreground",children:[(0,r.jsx)("li",{children:'Paste the bot token in the "Telegram Bot Token" field'}),(0,r.jsx)("li",{children:'Paste the chat ID in the "Telegram Chat ID" field'}),(0,r.jsx)("li",{children:"Choose your notification preferences"}),(0,r.jsx)("li",{children:'Click "Save Telegram Config"'}),(0,r.jsx)("li",{children:'Test the connection with "Test Telegram"'})]})]}),(0,r.jsx)("div",{className:"mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md",children:(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"text-yellow-600",children:"\uD83D\uDCA1"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("p",{className:"font-medium text-yellow-600 mb-1",children:"Pro Tip:"}),(0,r.jsx)("p",{className:"text-yellow-700",children:"Keep your bot token secure and never share it publicly. You can regenerate it anytime via BotFather if needed."})]})]})})]})})]})]})}),(0,r.jsx)(i.av,{value:"sessionManager",className:"mt-6",children:(0,r.jsx)(H,{})})]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[1,64,527,26,655,106,553,318,141,441,684,358],()=>s(2136)),_N_E=e.O()}]);