"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_session-manager_ts"],{

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js":
/*!******************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/native.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  randomUUID\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvbmF0aXZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFx1dWlkXFxkaXN0XFxlc20tYnJvd3NlclxcbmF0aXZlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHJhbmRvbVVVSUQgPSB0eXBlb2YgY3J5cHRvICE9PSAndW5kZWZpbmVkJyAmJiBjcnlwdG8ucmFuZG9tVVVJRCAmJiBjcnlwdG8ucmFuZG9tVVVJRC5iaW5kKGNyeXB0byk7XG5leHBvcnQgZGVmYXVsdCB7XG4gIHJhbmRvbVVVSURcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js":
/*!*****************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/regex.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvcmVnZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWMsRUFBRSxVQUFVLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixFQUFFLFVBQVUsR0FBRyx5Q0FBeUMiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFxyZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLTVdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCkkL2k7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js":
/*!***************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/rng.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\n// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nfunction rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvcm5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFx1dWlkXFxkaXN0XFxlc20tYnJvd3Nlclxccm5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFVuaXF1ZSBJRCBjcmVhdGlvbiByZXF1aXJlcyBhIGhpZ2ggcXVhbGl0eSByYW5kb20gIyBnZW5lcmF0b3IuIEluIHRoZSBicm93c2VyIHdlIHRoZXJlZm9yZVxuLy8gcmVxdWlyZSB0aGUgY3J5cHRvIEFQSSBhbmQgZG8gbm90IHN1cHBvcnQgYnVpbHQtaW4gZmFsbGJhY2sgdG8gbG93ZXIgcXVhbGl0eSByYW5kb20gbnVtYmVyXG4vLyBnZW5lcmF0b3JzIChsaWtlIE1hdGgucmFuZG9tKCkpLlxubGV0IGdldFJhbmRvbVZhbHVlcztcbmNvbnN0IHJuZHM4ID0gbmV3IFVpbnQ4QXJyYXkoMTYpO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcm5nKCkge1xuICAvLyBsYXp5IGxvYWQgc28gdGhhdCBlbnZpcm9ubWVudHMgdGhhdCBuZWVkIHRvIHBvbHlmaWxsIGhhdmUgYSBjaGFuY2UgdG8gZG8gc29cbiAgaWYgKCFnZXRSYW5kb21WYWx1ZXMpIHtcbiAgICAvLyBnZXRSYW5kb21WYWx1ZXMgbmVlZHMgdG8gYmUgaW52b2tlZCBpbiBhIGNvbnRleHQgd2hlcmUgXCJ0aGlzXCIgaXMgYSBDcnlwdG8gaW1wbGVtZW50YXRpb24uXG4gICAgZ2V0UmFuZG9tVmFsdWVzID0gdHlwZW9mIGNyeXB0byAhPT0gJ3VuZGVmaW5lZCcgJiYgY3J5cHRvLmdldFJhbmRvbVZhbHVlcyAmJiBjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzLmJpbmQoY3J5cHRvKTtcblxuICAgIGlmICghZ2V0UmFuZG9tVmFsdWVzKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ2NyeXB0by5nZXRSYW5kb21WYWx1ZXMoKSBub3Qgc3VwcG9ydGVkLiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3V1aWRqcy91dWlkI2dldHJhbmRvbXZhbHVlcy1ub3Qtc3VwcG9ydGVkJyk7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGdldFJhbmRvbVZhbHVlcyhybmRzOCk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js":
/*!*********************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/stringify.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js\");\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nfunction unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js":
/*!**************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/v4.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js\");\n\n\n\n\nfunction v4(options, buf, offset) {\n  if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n    return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNOO0FBQ3NCOztBQUVqRDtBQUNBLE1BQU0sa0RBQU07QUFDWixXQUFXLGtEQUFNO0FBQ2pCOztBQUVBO0FBQ0EsaURBQWlELCtDQUFHLEtBQUs7O0FBRXpEO0FBQ0EsbUNBQW1DOztBQUVuQztBQUNBOztBQUVBLG9CQUFvQixRQUFRO0FBQzVCO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxTQUFTLDhEQUFlO0FBQ3hCOztBQUVBLGlFQUFlLEVBQUUiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFx2NC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbmF0aXZlIGZyb20gJy4vbmF0aXZlLmpzJztcbmltcG9ydCBybmcgZnJvbSAnLi9ybmcuanMnO1xuaW1wb3J0IHsgdW5zYWZlU3RyaW5naWZ5IH0gZnJvbSAnLi9zdHJpbmdpZnkuanMnO1xuXG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICBpZiAobmF0aXZlLnJhbmRvbVVVSUQgJiYgIWJ1ZiAmJiAhb3B0aW9ucykge1xuICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICB9XG5cbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gIGNvbnN0IHJuZHMgPSBvcHRpb25zLnJhbmRvbSB8fCAob3B0aW9ucy5ybmcgfHwgcm5nKSgpOyAvLyBQZXIgNC40LCBzZXQgYml0cyBmb3IgdmVyc2lvbiBhbmQgYGNsb2NrX3NlcV9oaV9hbmRfcmVzZXJ2ZWRgXG5cbiAgcm5kc1s2XSA9IHJuZHNbNl0gJiAweDBmIHwgMHg0MDtcbiAgcm5kc1s4XSA9IHJuZHNbOF0gJiAweDNmIHwgMHg4MDsgLy8gQ29weSBieXRlcyB0byBidWZmZXIsIGlmIHByb3ZpZGVkXG5cbiAgaWYgKGJ1Zikge1xuICAgIG9mZnNldCA9IG9mZnNldCB8fCAwO1xuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCAxNjsgKytpKSB7XG4gICAgICBidWZbb2Zmc2V0ICsgaV0gPSBybmRzW2ldO1xuICAgIH1cblxuICAgIHJldHVybiBidWY7XG4gIH1cblxuICByZXR1cm4gdW5zYWZlU3RyaW5naWZ5KHJuZHMpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2NDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js":
/*!********************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/validate.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js\");\n\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7O0FBRS9CO0FBQ0EscUNBQXFDLGlEQUFLO0FBQzFDOztBQUVBLGlFQUFlLFFBQVEiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFx2YWxpZGF0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUkVHRVggZnJvbSAnLi9yZWdleC5qcyc7XG5cbmZ1bmN0aW9uIHZhbGlkYXRlKHV1aWQpIHtcbiAgcmV0dXJuIHR5cGVvZiB1dWlkID09PSAnc3RyaW5nJyAmJiBSRUdFWC50ZXN0KHV1aWQpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2YWxpZGF0ZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nconst ACTIVE_SESSIONS_KEY = 'pluto_active_sessions'; // Track active sessions across all windows\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    loadActiveSessionsFromStorage() {\n        try {\n            if (false) {}\n            const activeSessionsData = localStorage.getItem(ACTIVE_SESSIONS_KEY);\n            if (activeSessionsData) {\n                const parsedActiveSessions = JSON.parse(activeSessionsData);\n                this.activeSessionsAcrossWindows = new Map(Object.entries(parsedActiveSessions));\n                // Clean up stale entries (older than 5 minutes)\n                const now = Date.now();\n                const staleThreshold = 5 * 60 * 1000; // 5 minutes\n                for (const [key, entry] of this.activeSessionsAcrossWindows.entries()){\n                    if (now - entry.timestamp > staleThreshold) {\n                        this.activeSessionsAcrossWindows.delete(key);\n                    }\n                }\n                this.saveActiveSessionsToStorage();\n            }\n        } catch (error) {\n            console.error('Failed to load active sessions from storage:', error);\n        }\n    }\n    saveActiveSessionsToStorage() {\n        try {\n            if (false) {}\n            const activeSessionsObject = Object.fromEntries(this.activeSessionsAcrossWindows);\n            localStorage.setItem(ACTIVE_SESSIONS_KEY, JSON.stringify(activeSessionsObject));\n            // Trigger storage event to notify other windows/tabs (including admin panel)\n            window.dispatchEvent(new StorageEvent('storage', {\n                key: ACTIVE_SESSIONS_KEY,\n                newValue: JSON.stringify(activeSessionsObject),\n                storageArea: localStorage\n            }));\n        } catch (error) {\n            console.error('Failed to save active sessions to storage:', error);\n        }\n    }\n    addActiveSession(sessionId) {\n        // Validate session ID before adding\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.warn('⚠️ Attempted to add invalid session ID to active sessions:', sessionId);\n            return;\n        }\n        const key = \"\".concat(this.windowId, \"_\").concat(sessionId);\n        this.activeSessionsAcrossWindows.set(key, {\n            sessionId,\n            windowId: this.windowId,\n            timestamp: Date.now()\n        });\n        this.saveActiveSessionsToStorage();\n    }\n    removeActiveSession(sessionId) {\n        const key = \"\".concat(this.windowId, \"_\").concat(sessionId);\n        this.activeSessionsAcrossWindows.delete(key);\n        this.saveActiveSessionsToStorage();\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    handleAppRestart() {\n        try {\n            console.log('🔄 Checking for app restart and cleaning up running sessions...');\n            // Check if this is a fresh app start by looking for a restart marker\n            const lastAppClose = localStorage.getItem('pluto_last_app_close');\n            const appStartTime = Date.now();\n            // If no close marker or it's been more than 5 minutes, consider it an app restart\n            const isAppRestart = !lastAppClose || appStartTime - parseInt(lastAppClose) > 5 * 60 * 1000;\n            if (isAppRestart) {\n                console.log('🔄 App restart detected - cleaning up running sessions');\n                // Find all sessions marked as active/running\n                const runningSessions = Array.from(this.sessions.values()).filter((session)=>session.isActive);\n                if (runningSessions.length > 0) {\n                    console.log(\"\\uD83D\\uDED1 Found \".concat(runningSessions.length, \" running sessions to clean up:\"), runningSessions.map((s)=>({\n                            id: s.id,\n                            name: s.name\n                        })));\n                    runningSessions.forEach((session)=>{\n                        try {\n                            // Create auto-saved version in past sessions\n                            const timestamp = new Date().toLocaleString('en-US', {\n                                month: 'short',\n                                day: 'numeric',\n                                hour: '2-digit',\n                                minute: '2-digit',\n                                hour12: false\n                            });\n                            const autoSavedName = \"\".concat(session.name, \" (AutoSaved \").concat(timestamp, \")\");\n                            // Create new session for the auto-saved version\n                            const autoSavedId = this.createNewSessionSync(autoSavedName, session.config);\n                            // Save the auto-saved session with all progress as inactive\n                            this.saveSession(autoSavedId, session.config, session.targetPriceRows, session.orderHistory, session.currentMarketPrice, session.crypto1Balance, session.crypto2Balance, session.stablecoinBalance, false // Mark as inactive (past session)\n                            );\n                            // Mark original session as inactive\n                            session.isActive = false;\n                            session.lastModified = Date.now();\n                            this.sessions.set(session.id, session);\n                            console.log('✅ Auto-saved running session \"'.concat(session.name, '\" to past sessions as \"').concat(autoSavedName, '\"'));\n                        } catch (error) {\n                            console.error('❌ Failed to auto-save running session \"'.concat(session.name, '\":'), error);\n                            // At minimum, mark the session as inactive\n                            session.isActive = false;\n                            session.lastModified = Date.now();\n                            this.sessions.set(session.id, session);\n                        }\n                    });\n                    // Clear current session since all running sessions have been stopped\n                    this.currentSessionId = null;\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.removeItem(currentSessionKey);\n                    sessionStorage.removeItem(CURRENT_SESSION_KEY);\n                    // Save updated sessions\n                    this.saveSessionsToStorage();\n                    console.log('✅ App restart cleanup completed - all running sessions moved to past sessions');\n                } else {\n                    console.log('✅ No running sessions found - no cleanup needed');\n                }\n            } else {\n                console.log('✅ Normal app continuation - no cleanup needed');\n            }\n            // Set marker for next app close detection\n            localStorage.setItem('pluto_app_start', appStartTime.toString());\n        } catch (error) {\n            console.error('❌ Error during app restart cleanup:', error);\n        }\n    }\n    cleanupStalePersistenceInfo() {\n        try {\n            console.log('🧹 Cleaning up stale persistence info...');\n            const now = Date.now();\n            const staleThreshold = 24 * 60 * 60 * 1000; // 24 hours\n            // Get all localStorage keys that are persistence info\n            const keysToRemove = [];\n            for(let i = 0; i < localStorage.length; i++){\n                const key = localStorage.key(i);\n                if (key && key.startsWith('pluto_session_persistence_')) {\n                    try {\n                        const persistenceInfo = JSON.parse(localStorage.getItem(key) || '{}');\n                        if (persistenceInfo.lastSaved && now - persistenceInfo.lastSaved > staleThreshold) {\n                            keysToRemove.push(key);\n                        }\n                    } catch (error) {\n                        // Invalid JSON, remove it\n                        keysToRemove.push(key);\n                    }\n                }\n            }\n            // Remove stale persistence info\n            keysToRemove.forEach((key)=>{\n                localStorage.removeItem(key);\n                console.log(\"\\uD83D\\uDDD1️ Removed stale persistence info: \".concat(key));\n            });\n            if (keysToRemove.length > 0) {\n                console.log(\"✅ Cleaned up \".concat(keysToRemove.length, \" stale persistence entries\"));\n            } else {\n                console.log('✅ No stale persistence info found');\n            }\n        } catch (error) {\n            console.error('❌ Error cleaning up persistence info:', error);\n        }\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    return session.isActive && session.lastModified && Date.now() - session.lastModified < 30 * 60 * 1000 // Active within last 30 minutes\n                    ;\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>current[1].lastModified > latest[1].lastModified ? current : latest);\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n                // Ensure the current session is marked as active if it exists\n                const currentSession = this.sessions.get(currentSessionId);\n                if (currentSession && currentSession.isActive) {\n                    this.addActiveSession(currentSessionId);\n                    console.log(\"✅ Restored active session tracking for: \".concat(currentSessionId));\n                }\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session and restore it properly\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                    // Store in both localStorage and sessionStorage for persistence\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.setItem(currentSessionKey, activeSession.session_uuid);\n                    sessionStorage.setItem(CURRENT_SESSION_KEY, activeSession.session_uuid);\n                    console.log(\"✅ Restored active session from backend: \".concat(activeSession.session_uuid));\n                }\n                // Also save to localStorage as backup\n                this.saveSessionsToStorage();\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n            // Trigger storage event to notify other windows/tabs (including admin panel)\n            window.dispatchEvent(new StorageEvent('storage', {\n                key: SESSIONS_STORAGE_KEY,\n                newValue: JSON.stringify(sessionsObject),\n                storageArea: localStorage\n            }));\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    createNewSessionSync(name, config, currentBalances) {\n        // Synchronous version for use in beforeunload handlers\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const newSession = {\n            id: sessionId,\n            name,\n            config: {\n                ...config\n            },\n            createdAt: Date.now(),\n            lastModified: Date.now(),\n            isActive: false,\n            runtime: 0,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 100000,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin || 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Add persistence timestamp for recovery\n            const persistenceInfo = {\n                lastSaved: Date.now(),\n                windowId: this.windowId,\n                isActive,\n                sessionId\n            };\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            // Store persistence info for recovery\n            localStorage.setItem(\"pluto_session_persistence_\".concat(sessionId), JSON.stringify(persistenceInfo));\n            console.log(\"\\uD83D\\uDCBE Saving session with complete data:\", {\n                sessionId,\n                targetPriceRows: targetPriceRows.length,\n                orderHistory: orderHistory.length,\n                balances: {\n                    crypto1Balance,\n                    crypto2Balance,\n                    stablecoinBalance\n                },\n                isActive,\n                runtime: currentRuntime\n            });\n            this.sessions.set(sessionId, updatedSession);\n            // If session is active, ensure it's tracked in active sessions\n            if (isActive) {\n                this.addActiveSession(sessionId);\n                console.log(\"✅ Session marked as active and tracked: \".concat(sessionId));\n            } else {\n                this.removeActiveSession(sessionId);\n                console.log(\"⏹️ Session marked as inactive and removed from tracking: \".concat(sessionId));\n            }\n            // Save to backend only if explicitly authenticated\n            if (this.useBackend && \"object\" !== 'undefined') {\n                const token = localStorage.getItem('plutoAuthToken');\n                if (token && token.length > 10) {\n                    try {\n                        // Double-check authentication before making API call\n                        const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n                        const sessionData = {\n                            name: updatedSession.name,\n                            config: config,\n                            targetPriceRows: targetPriceRows,\n                            currentMarketPrice: currentMarketPrice,\n                            crypto1Balance: crypto1Balance,\n                            crypto2Balance: crypto2Balance,\n                            stablecoinBalance: stablecoinBalance,\n                            isActive: isActive,\n                            additionalRuntime: currentRuntime\n                        };\n                        await sessionApi.updateSession(sessionId, sessionData);\n                        console.log('✅ Session saved to backend:', sessionId);\n                    } catch (error) {\n                        const errorMessage = error instanceof Error ? error.message : String(error);\n                        console.warn('❌ Backend session save failed:', errorMessage);\n                        // Handle \"Session not found\" error by creating a new session\n                        if (errorMessage.includes('Session not found')) {\n                            console.log('🔄 Session not found in backend, creating new session...');\n                            try {\n                                // Create a new session in the backend with the same data\n                                const newSessionId = await this.createNewSession(updatedSession.name, config, {\n                                    crypto1: crypto1Balance,\n                                    crypto2: crypto2Balance,\n                                    stablecoin: stablecoinBalance\n                                });\n                                // Update the local session ID and session data\n                                this.sessions.set(newSessionId, {\n                                    ...updatedSession,\n                                    id: newSessionId\n                                });\n                                this.sessions.delete(sessionId); // Remove old session\n                                this.setCurrentSession(newSessionId); // Update current session ID\n                                console.log('✅ Created new session to replace missing one:', newSessionId);\n                                return true;\n                            } catch (createError) {\n                                console.error('❌ Failed to create replacement session:', createError);\n                            // Fall through to disable backend mode\n                            }\n                        }\n                        // Disable backend mode on any authentication-related error\n                        if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication') || errorMessage.includes('required')) {\n                            console.log('🔐 Disabling backend mode due to authentication issue');\n                            this.useBackend = false;\n                        }\n                    // Continue with localStorage save as fallback\n                    }\n                } else {\n                    console.log('⚠️ Invalid or missing auth token, skipping backend session save');\n                    this.useBackend = false; // Disable backend mode if no valid token\n                }\n            }\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    getCurrentActiveSessions() {\n        // Get all sessions that are currently active across all windows\n        const activeSessionIds = new Set(Array.from(this.activeSessionsAcrossWindows.values()).map((entry)=>entry.sessionId).filter((sessionId)=>sessionId && sessionId !== 'undefined') // Filter out invalid session IDs\n        );\n        // Also include current session if it exists (might not be in activeSessionsAcrossWindows yet)\n        if (this.currentSessionId && this.sessions.has(this.currentSessionId)) {\n            activeSessionIds.add(this.currentSessionId);\n        }\n        console.log('🔍 getCurrentActiveSessions debug:', {\n            activeSessionsAcrossWindows: Array.from(this.activeSessionsAcrossWindows.entries()),\n            activeSessionIds: Array.from(activeSessionIds),\n            allSessions: Array.from(this.sessions.keys()),\n            currentSessionId: this.currentSessionId,\n            windowId: this.windowId\n        });\n        // Get sessions that are either in active tracking OR marked as active in their data\n        const allActiveSessions = Array.from(this.sessions.values()).filter((session)=>{\n            const isInActiveTracking = session.id && activeSessionIds.has(session.id);\n            const isMarkedActive = session.isActive;\n            const isCurrentSession = session.id === this.currentSessionId;\n            console.log(\"\\uD83D\\uDD0D Session \".concat(session.id, \" (\").concat(session.name, \"):\"), {\n                isInActiveTracking,\n                isMarkedActive,\n                isCurrentSession,\n                shouldInclude: isInActiveTracking || isMarkedActive || isCurrentSession\n            });\n            return isInActiveTracking || isMarkedActive || isCurrentSession;\n        }).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: true,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            })).filter((session)=>session.id); // Ensure all returned sessions have valid IDs\n        console.log('🔍 Returning active sessions:', allActiveSessions.length, allActiveSessions.map((s)=>({\n                id: s.id,\n                name: s.name\n            })));\n        return allActiveSessions;\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            // Allow multiple sessions to be active simultaneously (for different tabs)\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                // Track this session as active across windows\n                this.addActiveSession(sessionId);\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n                // Trigger storage event to notify admin panel about current session change\n                window.dispatchEvent(new StorageEvent('storage', {\n                    key: 'pluto_current_session',\n                    newValue: sessionId,\n                    storageArea: localStorage\n                }));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Remove session from active tracking and mark as inactive only if no other windows are using it\n        if (this.currentSessionId) {\n            this.removeActiveSession(this.currentSessionId);\n            // Check if any other windows are still using this session\n            const isStillActiveInOtherWindows = Array.from(this.activeSessionsAcrossWindows.values()).some((entry)=>entry.sessionId === this.currentSessionId);\n            if (!isStillActiveInOtherWindows) {\n                const session = this.sessions.get(this.currentSessionId);\n                if (session && session.isActive) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive (no other windows using it)\"));\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD04 Session \".concat(this.currentSessionId, \" still active in other windows\"));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.activeSessionsAcrossWindows = new Map();\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Disable backend by default - use localStorage mode for better reliability\n        this.useBackend = false;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.loadActiveSessionsFromStorage();\n        this.setupStorageListener();\n        // Handle app restart - stop any running sessions and move to past sessions\n        this.handleAppRestart();\n        // Clean up stale persistence info\n        this.cleanupStalePersistenceInfo();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc2Vzc2lvbi1tYW5hZ2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUNvQztBQUNHO0FBRXZDLE1BQU1HLHVCQUF1QjtBQUM3QixNQUFNQyxzQkFBc0I7QUFDNUIsTUFBTUMsc0JBQXNCLHlCQUF5QiwyQ0FBMkM7QUFFaEcsMERBQTBEO0FBQzFELE1BQU1DLG1CQUFtQjtJQUN2QixPQUFPLFVBQXdCQyxPQUFkQyxLQUFLQyxHQUFHLElBQUcsS0FBK0MsT0FBNUNGLEtBQUtHLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHO0FBQ3pFO0FBRUEsdUNBQXVDO0FBQ3ZDLE1BQU1DLGNBQWM7SUFDbEIsSUFBSSxLQUE2QixFQUFFLEVBQWdCO0lBRW5ELGlGQUFpRjtJQUNqRixJQUFJQyxXQUFXQyxlQUFlQyxPQUFPLENBQUM7SUFDdEMsSUFBSSxDQUFDRixVQUFVO1FBQ2JBLFdBQVdSO1FBQ1hTLGVBQWVFLE9BQU8sQ0FBQyxtQkFBbUJIO1FBQzFDSSxRQUFRQyxHQUFHLENBQUMsdUNBQXNDLE9BQVRMO0lBQzNDLE9BQU87UUFDTEksUUFBUUMsR0FBRyxDQUFDLDBDQUF5QyxPQUFUTDtJQUM5QztJQUNBLE9BQU9BO0FBQ1Q7QUFFTyxNQUFNTTtJQXVDWCxPQUFjQyxjQUE4QjtRQUMxQyxJQUFJLENBQUNELGVBQWVFLFFBQVEsRUFBRTtZQUM1QkYsZUFBZUUsUUFBUSxHQUFHLElBQUlGO1FBQ2hDO1FBQ0EsT0FBT0EsZUFBZUUsUUFBUTtJQUNoQztJQUVRQyxvQkFBb0JDLE1BQXFCLEVBQVU7UUFDekQsTUFBTUMsVUFBVUQsT0FBT0MsT0FBTyxJQUFJO1FBQ2xDLE1BQU1DLFVBQVVGLE9BQU9FLE9BQU8sSUFBSTtRQUNsQyxNQUFNQyxjQUFjSCxPQUFPRyxXQUFXLElBQUk7UUFFMUMsTUFBTUMsV0FBVyxHQUFjRixPQUFYRCxTQUFRLEtBQWNFLE9BQVhELFNBQVEsS0FBZSxPQUFaQztRQUUxQyxzREFBc0Q7UUFDdEQsTUFBTUUsbUJBQW1CQyxNQUFNQyxJQUFJLENBQUMsSUFBSSxDQUFDQyxRQUFRLENBQUNDLE1BQU07UUFDeEQsTUFBTUMsa0JBQWtCTCxpQkFBaUJNLE1BQU0sQ0FBQ0MsQ0FBQUEsVUFDOUNBLFFBQVFDLElBQUksQ0FBQ0MsVUFBVSxDQUFDVjtRQUcxQixJQUFJTSxnQkFBZ0JLLE1BQU0sS0FBSyxHQUFHO1lBQ2hDLE9BQU9YO1FBQ1Q7UUFFQSxrQ0FBa0M7UUFDbEMsSUFBSVksWUFBWTtRQUNoQk4sZ0JBQWdCTyxPQUFPLENBQUNMLENBQUFBO1lBQ3RCLE1BQU1NLFFBQVFOLFFBQVFDLElBQUksQ0FBQ0ssS0FBSyxDQUFDLElBQUlDLE9BQU8sSUFBb0QsT0FBaERmLFNBQVNnQixPQUFPLENBQUMsdUJBQXVCLFNBQVE7WUFDaEcsSUFBSUYsT0FBTztnQkFDVCxNQUFNRyxTQUFTQyxTQUFTSixLQUFLLENBQUMsRUFBRSxFQUFFO2dCQUNsQyxJQUFJRyxTQUFTTCxXQUFXO29CQUN0QkEsWUFBWUs7Z0JBQ2Q7WUFDRixPQUFPLElBQUlULFFBQVFDLElBQUksS0FBS1QsVUFBVTtnQkFDcEMscURBQXFEO2dCQUNyRFksWUFBWWpDLEtBQUt3QyxHQUFHLENBQUNQLFdBQVc7WUFDbEM7UUFDRjtRQUVBLE9BQU8sR0FBdUJBLE9BQXBCWixVQUFTLGFBQXlCLE9BQWRZLFlBQVk7SUFDNUM7SUFFQSxNQUFjUSw4QkFBNkM7UUFDekQsMkNBQTJDO1FBQzNDLElBQUksSUFBSSxDQUFDQyxjQUFjLEVBQUU7WUFDdkIvQixRQUFRQyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSSxDQUFDOEIsY0FBYyxHQUFHO1FBRXRCLElBQUk7WUFDRixpQ0FBaUM7WUFDakMsTUFBTUMsWUFBWUMsYUFBYW5DLE9BQU8sQ0FBQztZQUN2QyxJQUFJLENBQUNrQyxXQUFXO2dCQUNkaEMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRztnQkFDbEIsSUFBSSxDQUFDQyx1QkFBdUI7Z0JBQzVCO1lBQ0Y7WUFFQSw4REFBOEQ7WUFDOUQsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLGlDQUFpQztnQkFDNURDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtZQUNGO1lBRUEsSUFBSUgsU0FBU0ksRUFBRSxFQUFFO2dCQUNmeEMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRztnQkFDbEIsNERBQTREO2dCQUM1RCxNQUFNLElBQUksQ0FBQ08sdUJBQXVCO1lBQ3BDLE9BQU87Z0JBQ0wsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2QzQyxRQUFRQyxHQUFHLENBQUMsc0RBQXNEMEM7WUFDbEUsSUFBSSxDQUFDVCxVQUFVLEdBQUc7WUFDbEIsSUFBSSxDQUFDQyx1QkFBdUI7UUFDOUIsU0FBVTtZQUNSLElBQUksQ0FBQ0osY0FBYyxHQUFHO1FBQ3hCO0lBQ0Y7SUFFQSxNQUFjYSx5QkFBMkM7UUFDdkQsSUFBSTtZQUNGLE1BQU1aLFlBQVlDLGFBQWFuQyxPQUFPLENBQUM7WUFDdkMsSUFBSSxDQUFDa0MsV0FBVztnQkFDZCxPQUFPO1lBQ1Q7WUFFQSxNQUFNSSxXQUFXLE1BQU1DLE1BQU0saUNBQWlDO2dCQUM1REMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7b0JBQ2hCLGlCQUFpQixVQUFvQixPQUFWUDtnQkFDN0I7WUFDRjtZQUVBLE9BQU9JLFNBQVNJLEVBQUU7UUFDcEIsRUFBRSxPQUFPRyxPQUFPO1lBQ2QzQyxRQUFRQyxHQUFHLENBQUMsdUNBQXVDMEM7WUFDbkQsT0FBTztRQUNUO0lBQ0Y7SUFFUUUscUJBQXFCQyxPQUFlLEVBQVU7UUFDcEQsT0FBTyxHQUFjLE9BQVhBLFNBQVEsS0FBaUIsT0FBZCxJQUFJLENBQUNsRCxRQUFRO0lBQ3BDO0lBRVFtRCxnQ0FBc0M7UUFDNUMsSUFBSTtZQUNGLElBQUksS0FBNkIsRUFBRSxFQUFPO1lBRTFDLE1BQU1DLHFCQUFxQmYsYUFBYW5DLE9BQU8sQ0FBQ1g7WUFDaEQsSUFBSTZELG9CQUFvQjtnQkFDdEIsTUFBTUMsdUJBQXVCQyxLQUFLQyxLQUFLLENBQUNIO2dCQUN4QyxJQUFJLENBQUNJLDJCQUEyQixHQUFHLElBQUlDLElBQUlDLE9BQU9DLE9BQU8sQ0FBQ047Z0JBRTFELGdEQUFnRDtnQkFDaEQsTUFBTTFELE1BQU1ELEtBQUtDLEdBQUc7Z0JBQ3BCLE1BQU1pRSxpQkFBaUIsSUFBSSxLQUFLLE1BQU0sWUFBWTtnQkFDbEQsS0FBSyxNQUFNLENBQUNDLEtBQUtDLE1BQU0sSUFBSSxJQUFJLENBQUNOLDJCQUEyQixDQUFDRyxPQUFPLEdBQUk7b0JBQ3JFLElBQUloRSxNQUFNbUUsTUFBTUMsU0FBUyxHQUFHSCxnQkFBZ0I7d0JBQzFDLElBQUksQ0FBQ0osMkJBQTJCLENBQUNRLE1BQU0sQ0FBQ0g7b0JBQzFDO2dCQUNGO2dCQUNBLElBQUksQ0FBQ0ksMkJBQTJCO1lBQ2xDO1FBQ0YsRUFBRSxPQUFPbEIsT0FBTztZQUNkM0MsUUFBUTJDLEtBQUssQ0FBQyxnREFBZ0RBO1FBQ2hFO0lBQ0Y7SUFFUWtCLDhCQUFvQztRQUMxQyxJQUFJO1lBQ0YsSUFBSSxLQUE2QixFQUFFLEVBQU87WUFFMUMsTUFBTUMsdUJBQXVCUixPQUFPUyxXQUFXLENBQUMsSUFBSSxDQUFDWCwyQkFBMkI7WUFDaEZuQixhQUFhbEMsT0FBTyxDQUFDWixxQkFBcUIrRCxLQUFLYyxTQUFTLENBQUNGO1lBRXpELDZFQUE2RTtZQUM3RUcsT0FBT0MsYUFBYSxDQUFDLElBQUlDLGFBQWEsV0FBVztnQkFDL0NWLEtBQUt0RTtnQkFDTGlGLFVBQVVsQixLQUFLYyxTQUFTLENBQUNGO2dCQUN6Qk8sYUFBYXBDO1lBQ2Y7UUFDRixFQUFFLE9BQU9VLE9BQU87WUFDZDNDLFFBQVEyQyxLQUFLLENBQUMsOENBQThDQTtRQUM5RDtJQUNGO0lBRVEyQixpQkFBaUJDLFNBQWlCLEVBQVE7UUFDaEQsb0NBQW9DO1FBQ3BDLElBQUksQ0FBQ0EsYUFBYUEsY0FBYyxlQUFlQSxjQUFjLFFBQVE7WUFDbkV2RSxRQUFRd0UsSUFBSSxDQUFDLDhEQUE4REQ7WUFDM0U7UUFDRjtRQUVBLE1BQU1kLE1BQU0sR0FBb0JjLE9BQWpCLElBQUksQ0FBQzNFLFFBQVEsRUFBQyxLQUFhLE9BQVYyRTtRQUNoQyxJQUFJLENBQUNuQiwyQkFBMkIsQ0FBQ3FCLEdBQUcsQ0FBQ2hCLEtBQUs7WUFDeENjO1lBQ0EzRSxVQUFVLElBQUksQ0FBQ0EsUUFBUTtZQUN2QitELFdBQVdyRSxLQUFLQyxHQUFHO1FBQ3JCO1FBQ0EsSUFBSSxDQUFDc0UsMkJBQTJCO0lBQ2xDO0lBRVFhLG9CQUFvQkgsU0FBaUIsRUFBUTtRQUNuRCxNQUFNZCxNQUFNLEdBQW9CYyxPQUFqQixJQUFJLENBQUMzRSxRQUFRLEVBQUMsS0FBYSxPQUFWMkU7UUFDaEMsSUFBSSxDQUFDbkIsMkJBQTJCLENBQUNRLE1BQU0sQ0FBQ0g7UUFDeEMsSUFBSSxDQUFDSSwyQkFBMkI7SUFDbEM7SUFFUWMsdUJBQTZCO1FBQ25DLElBQUksS0FBNkIsRUFBRSxFQUFPO1FBRTFDLGdEQUFnRDtRQUNoRFYsT0FBT1csZ0JBQWdCLENBQUMsV0FBVyxDQUFDQztZQUNsQyxJQUFJQSxNQUFNcEIsR0FBRyxLQUFLeEUsd0JBQXdCNEYsTUFBTVQsUUFBUSxFQUFFO2dCQUN4RCxJQUFJO29CQUNGLHFEQUFxRDtvQkFDckQsTUFBTVUsaUJBQWlCNUIsS0FBS0MsS0FBSyxDQUFDMEIsTUFBTVQsUUFBUTtvQkFDaEQsSUFBSSxDQUFDdEQsUUFBUSxHQUFHLElBQUl1QyxJQUFJQyxPQUFPQyxPQUFPLENBQUN1QjtvQkFDdkM5RSxRQUFRQyxHQUFHLENBQUMscURBQThELE9BQW5CLElBQUksQ0FBQ2EsUUFBUSxDQUFDaUUsSUFBSSxFQUFDO2dCQUM1RSxFQUFFLE9BQU9wQyxPQUFPO29CQUNkM0MsUUFBUTJDLEtBQUssQ0FBQywrQ0FBK0NBO2dCQUMvRDtZQUNGO1FBQ0Y7SUFDRjtJQUVRcUMsbUJBQXlCO1FBQy9CLElBQUk7WUFDRmhGLFFBQVFDLEdBQUcsQ0FBQztZQUVaLHFFQUFxRTtZQUNyRSxNQUFNZ0YsZUFBZWhELGFBQWFuQyxPQUFPLENBQUM7WUFDMUMsTUFBTW9GLGVBQWU1RixLQUFLQyxHQUFHO1lBRTdCLGtGQUFrRjtZQUNsRixNQUFNNEYsZUFBZSxDQUFDRixnQkFBZ0IsZUFBZ0JyRCxTQUFTcUQsZ0JBQWlCLElBQUksS0FBSztZQUV6RixJQUFJRSxjQUFjO2dCQUNoQm5GLFFBQVFDLEdBQUcsQ0FBQztnQkFFWiw2Q0FBNkM7Z0JBQzdDLE1BQU1tRixrQkFBa0J4RSxNQUFNQyxJQUFJLENBQUMsSUFBSSxDQUFDQyxRQUFRLENBQUNDLE1BQU0sSUFBSUUsTUFBTSxDQUFDQyxDQUFBQSxVQUFXQSxRQUFRbUUsUUFBUTtnQkFFN0YsSUFBSUQsZ0JBQWdCL0QsTUFBTSxHQUFHLEdBQUc7b0JBQzlCckIsUUFBUUMsR0FBRyxDQUFDLHNCQUFtQyxPQUF2Qm1GLGdCQUFnQi9ELE1BQU0sRUFBQyxtQ0FDN0MrRCxnQkFBZ0JFLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBTTs0QkFBRUMsSUFBSUQsRUFBRUMsRUFBRTs0QkFBRXJFLE1BQU1vRSxFQUFFcEUsSUFBSTt3QkFBQztvQkFFckRpRSxnQkFBZ0I3RCxPQUFPLENBQUNMLENBQUFBO3dCQUN0QixJQUFJOzRCQUNGLDZDQUE2Qzs0QkFDN0MsTUFBTXlDLFlBQVksSUFBSXJFLE9BQU9tRyxjQUFjLENBQUMsU0FBUztnQ0FDbkRDLE9BQU87Z0NBQ1BDLEtBQUs7Z0NBQ0xDLE1BQU07Z0NBQ05DLFFBQVE7Z0NBQ1JDLFFBQVE7NEJBQ1Y7NEJBQ0EsTUFBTUMsZ0JBQWdCLEdBQThCcEMsT0FBM0J6QyxRQUFRQyxJQUFJLEVBQUMsZ0JBQXdCLE9BQVZ3QyxXQUFVOzRCQUU5RCxnREFBZ0Q7NEJBQ2hELE1BQU1xQyxjQUFjLElBQUksQ0FBQ0Msb0JBQW9CLENBQUNGLGVBQWU3RSxRQUFRWixNQUFNOzRCQUUzRSw0REFBNEQ7NEJBQzVELElBQUksQ0FBQzRGLFdBQVcsQ0FDZEYsYUFDQTlFLFFBQVFaLE1BQU0sRUFDZFksUUFBUWlGLGVBQWUsRUFDdkJqRixRQUFRa0YsWUFBWSxFQUNwQmxGLFFBQVFtRixrQkFBa0IsRUFDMUJuRixRQUFRb0YsY0FBYyxFQUN0QnBGLFFBQVFxRixjQUFjLEVBQ3RCckYsUUFBUXNGLGlCQUFpQixFQUN6QixNQUFNLGtDQUFrQzs7NEJBRzFDLG9DQUFvQzs0QkFDcEN0RixRQUFRbUUsUUFBUSxHQUFHOzRCQUNuQm5FLFFBQVF1RixZQUFZLEdBQUduSCxLQUFLQyxHQUFHOzRCQUMvQixJQUFJLENBQUN1QixRQUFRLENBQUMyRCxHQUFHLENBQUN2RCxRQUFRc0UsRUFBRSxFQUFFdEU7NEJBRTlCbEIsUUFBUUMsR0FBRyxDQUFDLGlDQUF1RThGLE9BQXRDN0UsUUFBUUMsSUFBSSxFQUFDLDJCQUF1QyxPQUFkNEUsZUFBYzt3QkFDbkcsRUFBRSxPQUFPcEQsT0FBTzs0QkFDZDNDLFFBQVEyQyxLQUFLLENBQUMsMENBQXVELE9BQWJ6QixRQUFRQyxJQUFJLEVBQUMsT0FBS3dCOzRCQUUxRSwyQ0FBMkM7NEJBQzNDekIsUUFBUW1FLFFBQVEsR0FBRzs0QkFDbkJuRSxRQUFRdUYsWUFBWSxHQUFHbkgsS0FBS0MsR0FBRzs0QkFDL0IsSUFBSSxDQUFDdUIsUUFBUSxDQUFDMkQsR0FBRyxDQUFDdkQsUUFBUXNFLEVBQUUsRUFBRXRFO3dCQUNoQztvQkFDRjtvQkFFQSxxRUFBcUU7b0JBQ3JFLElBQUksQ0FBQ3dGLGdCQUFnQixHQUFHO29CQUN4QixNQUFNQyxvQkFBb0IsSUFBSSxDQUFDOUQsb0JBQW9CLENBQUMzRDtvQkFDcEQrQyxhQUFhMkUsVUFBVSxDQUFDRDtvQkFDeEI5RyxlQUFlK0csVUFBVSxDQUFDMUg7b0JBRTFCLHdCQUF3QjtvQkFDeEIsSUFBSSxDQUFDMkgscUJBQXFCO29CQUUxQjdHLFFBQVFDLEdBQUcsQ0FBQztnQkFDZCxPQUFPO29CQUNMRCxRQUFRQyxHQUFHLENBQUM7Z0JBQ2Q7WUFDRixPQUFPO2dCQUNMRCxRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLDBDQUEwQztZQUMxQ2dDLGFBQWFsQyxPQUFPLENBQUMsbUJBQW1CbUYsYUFBYXpGLFFBQVE7UUFFL0QsRUFBRSxPQUFPa0QsT0FBTztZQUNkM0MsUUFBUTJDLEtBQUssQ0FBQyx1Q0FBdUNBO1FBQ3ZEO0lBQ0Y7SUFFUW1FLDhCQUFvQztRQUMxQyxJQUFJO1lBQ0Y5RyxRQUFRQyxHQUFHLENBQUM7WUFFWixNQUFNVixNQUFNRCxLQUFLQyxHQUFHO1lBQ3BCLE1BQU1pRSxpQkFBaUIsS0FBSyxLQUFLLEtBQUssTUFBTSxXQUFXO1lBRXZELHNEQUFzRDtZQUN0RCxNQUFNdUQsZUFBeUIsRUFBRTtZQUNqQyxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSS9FLGFBQWFaLE1BQU0sRUFBRTJGLElBQUs7Z0JBQzVDLE1BQU12RCxNQUFNeEIsYUFBYXdCLEdBQUcsQ0FBQ3VEO2dCQUM3QixJQUFJdkQsT0FBT0EsSUFBSXJDLFVBQVUsQ0FBQywrQkFBK0I7b0JBQ3ZELElBQUk7d0JBQ0YsTUFBTTZGLGtCQUFrQi9ELEtBQUtDLEtBQUssQ0FBQ2xCLGFBQWFuQyxPQUFPLENBQUMyRCxRQUFRO3dCQUNoRSxJQUFJd0QsZ0JBQWdCQyxTQUFTLElBQUksTUFBT0QsZ0JBQWdCQyxTQUFTLEdBQUkxRCxnQkFBZ0I7NEJBQ25GdUQsYUFBYUksSUFBSSxDQUFDMUQ7d0JBQ3BCO29CQUNGLEVBQUUsT0FBT2QsT0FBTzt3QkFDZCwwQkFBMEI7d0JBQzFCb0UsYUFBYUksSUFBSSxDQUFDMUQ7b0JBQ3BCO2dCQUNGO1lBQ0Y7WUFFQSxnQ0FBZ0M7WUFDaENzRCxhQUFheEYsT0FBTyxDQUFDa0MsQ0FBQUE7Z0JBQ25CeEIsYUFBYTJFLFVBQVUsQ0FBQ25EO2dCQUN4QnpELFFBQVFDLEdBQUcsQ0FBQyxpREFBMkMsT0FBSndEO1lBQ3JEO1lBRUEsSUFBSXNELGFBQWExRixNQUFNLEdBQUcsR0FBRztnQkFDM0JyQixRQUFRQyxHQUFHLENBQUMsZ0JBQW9DLE9BQXBCOEcsYUFBYTFGLE1BQU0sRUFBQztZQUNsRCxPQUFPO2dCQUNMckIsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7UUFDRixFQUFFLE9BQU8wQyxPQUFPO1lBQ2QzQyxRQUFRMkMsS0FBSyxDQUFDLHlDQUF5Q0E7UUFDekQ7SUFDRjtJQUVRUiwwQkFBZ0M7UUFDdEMsSUFBSTtZQUNGLElBQUksS0FBNkIsRUFBRSxFQUFPO1lBRTFDLG9FQUFvRTtZQUNwRSxNQUFNaUYsZUFBZW5GLGFBQWFuQyxPQUFPLENBQUNiO1lBRTFDLDJFQUEyRTtZQUMzRSxJQUFJeUgsbUJBQWtDO1lBRXRDLHNEQUFzRDtZQUN0RCxNQUFNVyxvQkFBb0IsSUFBSSxDQUFDeEUsb0JBQW9CLENBQUMzRDtZQUNwRHdILG1CQUFtQnpFLGFBQWFuQyxPQUFPLENBQUN1SDtZQUV4Qyw4REFBOEQ7WUFDOUQsSUFBSSxDQUFDWCxrQkFBa0I7Z0JBQ3JCQSxtQkFBbUI3RyxlQUFlQyxPQUFPLENBQUNaO1lBQzVDO1lBRUEsZ0VBQWdFO1lBQ2hFLElBQUksQ0FBQ3dILG9CQUFvQlUsY0FBYztnQkFDckMsTUFBTXRDLGlCQUFpQjVCLEtBQUtDLEtBQUssQ0FBQ2lFO2dCQUNsQyxNQUFNRSxpQkFBaUJoRSxPQUFPQyxPQUFPLENBQUN1QixnQkFBZ0I3RCxNQUFNLENBQUM7d0JBQUMsQ0FBQ3NHLEdBQUdyRyxRQUF1QjsyQkFDdkZBLFFBQVFtRSxRQUFRLElBQUluRSxRQUFRdUYsWUFBWSxJQUFJLEtBQU1sSCxHQUFHLEtBQUsyQixRQUFRdUYsWUFBWSxHQUFJLEtBQUssS0FBSyxLQUFLLGdDQUFnQzs7O2dCQUVuSSxJQUFJYSxlQUFlakcsTUFBTSxHQUFHLEdBQUc7b0JBQzdCLHVDQUF1QztvQkFDdkMsTUFBTW1HLG9CQUFvQkYsZUFBZUcsTUFBTSxDQUFDLENBQUNDLFFBQXVCQyxVQUN0RSxPQUFRLENBQUMsRUFBRSxDQUFTbEIsWUFBWSxHQUFHLE1BQU8sQ0FBQyxFQUFFLENBQVNBLFlBQVksR0FBR2tCLFVBQVVEO29CQUVqRmhCLG1CQUFtQmMsaUJBQWlCLENBQUMsRUFBRTtvQkFDdkN4SCxRQUFRQyxHQUFHLENBQUMscURBQTRELE9BQWpCeUc7Z0JBQ3pEO1lBQ0Y7WUFFQSxJQUFJVSxjQUFjO2dCQUNoQixNQUFNdEMsaUJBQWlCNUIsS0FBS0MsS0FBSyxDQUFDaUU7Z0JBQ2xDLElBQUksQ0FBQ3RHLFFBQVEsR0FBRyxJQUFJdUMsSUFBSUMsT0FBT0MsT0FBTyxDQUFDdUI7WUFDekM7WUFFQSxJQUFJLENBQUM0QixnQkFBZ0IsR0FBR0E7WUFFeEIsdUVBQXVFO1lBQ3ZFLElBQUlBLGtCQUFrQjtnQkFDcEI3RyxlQUFlRSxPQUFPLENBQUNiLHFCQUFxQndIO2dCQUU1Qyw4REFBOEQ7Z0JBQzlELE1BQU1rQixpQkFBaUIsSUFBSSxDQUFDOUcsUUFBUSxDQUFDK0csR0FBRyxDQUFDbkI7Z0JBQ3pDLElBQUlrQixrQkFBa0JBLGVBQWV2QyxRQUFRLEVBQUU7b0JBQzdDLElBQUksQ0FBQ2YsZ0JBQWdCLENBQUNvQztvQkFDdEIxRyxRQUFRQyxHQUFHLENBQUMsMkNBQTRELE9BQWpCeUc7Z0JBQ3pEO1lBQ0Y7WUFFQTFHLFFBQVFDLEdBQUcsQ0FBQyx1QkFBOEQsT0FBakQsSUFBSSxDQUFDYSxRQUFRLENBQUNpRSxJQUFJLEVBQUMsZ0NBQXlEMkIsT0FBM0IsSUFBSSxDQUFDOUcsUUFBUSxFQUFDLGVBQThCLE9BQWpCOEc7UUFDdkcsRUFBRSxPQUFPL0QsT0FBTztZQUNkM0MsUUFBUTJDLEtBQUssQ0FBQyx5Q0FBeUNBO1FBQ3pEO0lBQ0Y7SUFFQSxNQUFjRiwwQkFBeUM7UUFDckQsSUFBSTtZQUNGLHlEQUF5RDtZQUN6RCxNQUFNcUYsUUFBUSxLQUE2QixHQUFHN0YsYUFBYW5DLE9BQU8sQ0FBQyxvQkFBb0IsQ0FBSTtZQUMzRixJQUFJLENBQUNnSSxTQUFTQSxNQUFNekcsTUFBTSxHQUFHLElBQUk7Z0JBQy9CckIsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRyxPQUFPLHVCQUF1QjtnQkFDaEQsSUFBSSxDQUFDQyx1QkFBdUI7Z0JBQzVCO1lBQ0Y7WUFFQW5DLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU0sRUFBRWpCLFVBQVUsRUFBRSxHQUFHLE1BQU0sNklBQW1CO1lBQ2hELE1BQU1vRCxXQUFXLE1BQU1wRCxXQUFXK0ksY0FBYyxDQUFDO1lBQ2pEL0gsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ21DO1lBRTVDLElBQUlBLFlBQVlBLFNBQVN0QixRQUFRLEVBQUU7Z0JBQ2pDLGtEQUFrRDtnQkFDbEQsSUFBSSxDQUFDQSxRQUFRLENBQUNrSCxLQUFLO2dCQUNuQjVGLFNBQVN0QixRQUFRLENBQUNTLE9BQU8sQ0FBQyxDQUFDTDtvQkFDekIsTUFBTStHLGNBQThCO3dCQUNsQ3pDLElBQUl0RSxRQUFRZ0gsWUFBWTt3QkFDeEIvRyxNQUFNRCxRQUFRQyxJQUFJO3dCQUNsQmIsUUFBUTRDLEtBQUtDLEtBQUssQ0FBQ2pDLFFBQVFpSCxlQUFlLElBQUk7d0JBQzlDQyxXQUFXLElBQUk5SSxLQUFLNEIsUUFBUW1ILFVBQVUsRUFBRUMsT0FBTzt3QkFDL0M3QixjQUFjLElBQUluSCxLQUFLNEIsUUFBUXFILGFBQWEsRUFBRUQsT0FBTzt3QkFDckRqRCxVQUFVbkUsUUFBUXNILFNBQVM7d0JBQzNCQyxTQUFTdkgsUUFBUXVILE9BQU8sSUFBSTt3QkFDNUJ0QyxpQkFBaUJqRixRQUFRd0gsaUJBQWlCLEdBQUd4RixLQUFLQyxLQUFLLENBQUNqQyxRQUFRd0gsaUJBQWlCLElBQUksRUFBRTt3QkFDdkZ0QyxjQUFjbEYsUUFBUXlILGFBQWEsR0FBR3pGLEtBQUtDLEtBQUssQ0FBQ2pDLFFBQVF5SCxhQUFhLElBQUksRUFBRTt3QkFDNUV0QyxvQkFBb0JuRixRQUFRMEgsb0JBQW9CLElBQUk7d0JBQ3BEdEMsZ0JBQWdCcEYsUUFBUTJILGVBQWUsSUFBSTt3QkFDM0N0QyxnQkFBZ0JyRixRQUFRNEgsZUFBZSxJQUFJO3dCQUMzQ3RDLG1CQUFtQnRGLFFBQVE2SCxrQkFBa0IsSUFBSTt3QkFDakRDLGVBQWU5SCxRQUFRK0gsY0FBYyxHQUFHL0YsS0FBS0MsS0FBSyxDQUFDakMsUUFBUStILGNBQWMsSUFBSUM7b0JBQy9FO29CQUNBLElBQUksQ0FBQ3BJLFFBQVEsQ0FBQzJELEdBQUcsQ0FBQ3ZELFFBQVFnSCxZQUFZLEVBQUVEO2dCQUMxQztnQkFFQSw4Q0FBOEM7Z0JBQzlDLE1BQU1rQixnQkFBZ0IvRyxTQUFTdEIsUUFBUSxDQUFDc0ksSUFBSSxDQUFDLENBQUM3RCxJQUFXQSxFQUFFaUQsU0FBUztnQkFDcEUsSUFBSVcsZUFBZTtvQkFDakIsSUFBSSxDQUFDekMsZ0JBQWdCLEdBQUd5QyxjQUFjakIsWUFBWTtvQkFDbEQsZ0VBQWdFO29CQUNoRSxNQUFNdkIsb0JBQW9CLElBQUksQ0FBQzlELG9CQUFvQixDQUFDM0Q7b0JBQ3BEK0MsYUFBYWxDLE9BQU8sQ0FBQzRHLG1CQUFtQndDLGNBQWNqQixZQUFZO29CQUNsRXJJLGVBQWVFLE9BQU8sQ0FBQ2IscUJBQXFCaUssY0FBY2pCLFlBQVk7b0JBQ3RFbEksUUFBUUMsR0FBRyxDQUFDLDJDQUFzRSxPQUEzQmtKLGNBQWNqQixZQUFZO2dCQUNuRjtnQkFFQSxzQ0FBc0M7Z0JBQ3RDLElBQUksQ0FBQ3JCLHFCQUFxQjtnQkFFMUI3RyxRQUFRQyxHQUFHLENBQUMsdUJBQWdDLE9BQW5CLElBQUksQ0FBQ2EsUUFBUSxDQUFDaUUsSUFBSSxFQUFDO1lBQzlDO1FBQ0YsRUFBRSxPQUFPcEMsT0FBTztZQUNkLE1BQU0wRyxlQUFlMUcsaUJBQWlCRCxRQUFRQyxNQUFNMkcsT0FBTyxHQUFHQyxPQUFPNUc7WUFFckUsMENBQTBDO1lBQzFDLElBQUkwRyxhQUFhRyxRQUFRLENBQUMscUJBQXFCSCxhQUFhRyxRQUFRLENBQUMsVUFBVUgsYUFBYUcsUUFBUSxDQUFDLFFBQVE7Z0JBQzNHeEosUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRyxPQUFPLG1EQUFtRDtZQUM5RSxPQUFPLElBQUltSCxhQUFhRyxRQUFRLENBQUMsNkJBQTZCO2dCQUM1RHhKLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0wsaURBQWlEO2dCQUNqREQsUUFBUXdFLElBQUksQ0FBQyxxRUFBcUU2RTtZQUNwRjtZQUVBLDJCQUEyQjtZQUMzQixJQUFJLENBQUNsSCx1QkFBdUI7UUFDOUI7SUFDRjtJQUVRMEUsd0JBQThCO1FBQ3BDLElBQUk7WUFDRixJQUFJLEtBQTZCLEVBQUUsRUFBTztZQUUxQyxrRUFBa0U7WUFDbEUsTUFBTTRDLGlCQUFpQm5HLE9BQU9TLFdBQVcsQ0FBQyxJQUFJLENBQUNqRCxRQUFRO1lBQ3ZEbUIsYUFBYWxDLE9BQU8sQ0FBQ2Qsc0JBQXNCaUUsS0FBS2MsU0FBUyxDQUFDeUY7WUFFMUQsa0RBQWtEO1lBQ2xELE1BQU05QyxvQkFBb0IsSUFBSSxDQUFDOUQsb0JBQW9CLENBQUMzRDtZQUNwRCxJQUFJLElBQUksQ0FBQ3dILGdCQUFnQixFQUFFO2dCQUN6QnpFLGFBQWFsQyxPQUFPLENBQUM0RyxtQkFBbUIsSUFBSSxDQUFDRCxnQkFBZ0I7WUFDL0Q7WUFFQSw2RUFBNkU7WUFDN0V6QyxPQUFPQyxhQUFhLENBQUMsSUFBSUMsYUFBYSxXQUFXO2dCQUMvQ1YsS0FBS3hFO2dCQUNMbUYsVUFBVWxCLEtBQUtjLFNBQVMsQ0FBQ3lGO2dCQUN6QnBGLGFBQWFwQztZQUNmO1FBQ0YsRUFBRSxPQUFPVSxPQUFPO1lBQ2QzQyxRQUFRMkMsS0FBSyxDQUFDLHVDQUF1Q0E7UUFDdkQ7SUFDRjtJQUVBLE1BQWErRyw2QkFBNkJwSixNQUFxQixFQUFFcUosVUFBbUIsRUFBRUMsZUFBMEUsRUFBbUI7UUFDakwsTUFBTUMsY0FBY0YsY0FBYyxJQUFJLENBQUN0SixtQkFBbUIsQ0FBQ0M7UUFDM0QsT0FBTyxJQUFJLENBQUN3SixnQkFBZ0IsQ0FBQ0QsYUFBYXZKLFFBQVFzSjtJQUNwRDtJQUVPM0QscUJBQXFCOUUsSUFBWSxFQUFFYixNQUFxQixFQUFFc0osZUFBMEUsRUFBVTtRQUNuSix1REFBdUQ7UUFDdkQsTUFBTUcsV0FBV0gsbUJBQW1CO1lBQUVySixTQUFTO1lBQUlDLFNBQVM7WUFBUXdKLFlBQVk7UUFBRTtRQUNsRixNQUFNekYsWUFBWXhGLGdEQUFNQTtRQUV4QixNQUFNa0wsYUFBNkI7WUFDakN6RSxJQUFJakI7WUFDSnBEO1lBQ0FiLFFBQVE7Z0JBQUUsR0FBR0EsTUFBTTtZQUFDO1lBQ3BCOEgsV0FBVzlJLEtBQUtDLEdBQUc7WUFDbkJrSCxjQUFjbkgsS0FBS0MsR0FBRztZQUN0QjhGLFVBQVU7WUFDVm9ELFNBQVM7WUFDVHRDLGlCQUFpQixFQUFFO1lBQ25CQyxjQUFjLEVBQUU7WUFDaEJDLG9CQUFvQjtZQUNwQkMsZ0JBQWdCeUQsU0FBU3hKLE9BQU87WUFDaENnRyxnQkFBZ0J3RCxTQUFTdkosT0FBTztZQUNoQ2dHLG1CQUFtQnVELFNBQVNDLFVBQVUsSUFBSTtRQUM1QztRQUVBLElBQUksQ0FBQ2xKLFFBQVEsQ0FBQzJELEdBQUcsQ0FBQ0YsV0FBVzBGO1FBQzdCLElBQUksQ0FBQ3BELHFCQUFxQjtRQUMxQixPQUFPdEM7SUFDVDtJQUVBLE1BQWF1RixpQkFBaUIzSSxJQUFZLEVBQUViLE1BQXFCLEVBQUVzSixlQUEwRSxFQUFtQjtRQUM5SiwwQ0FBMEM7UUFDMUMsTUFBTUcsV0FBV0gsbUJBQW1CO1lBQUVySixTQUFTO1lBQUlDLFNBQVM7WUFBUXdKLFlBQVk7UUFBRTtRQUVsRixJQUFJLElBQUksQ0FBQzlILFVBQVUsRUFBRTtZQUNuQixJQUFJO2dCQUNGLE1BQU0rRixjQUFjO29CQUNsQjlHO29CQUNBYixRQUFRQTtvQkFDUjZGLGlCQUFpQixFQUFFO29CQUNuQkUsb0JBQW9CO29CQUNwQkMsZ0JBQWdCeUQsU0FBU3hKLE9BQU87b0JBQ2hDZ0csZ0JBQWdCd0QsU0FBU3ZKLE9BQU87b0JBQ2hDZ0csbUJBQW1CdUQsU0FBU0MsVUFBVTtnQkFDeEM7Z0JBRUEsTUFBTTVILFdBQVcsTUFBTXBELGdEQUFVQSxDQUFDa0wsYUFBYSxDQUFDakM7Z0JBRWhELHFDQUFxQztnQkFDckMsTUFBTWdDLGFBQTZCO29CQUNqQ3pFLElBQUlwRCxTQUFTbEIsT0FBTyxDQUFDZ0gsWUFBWTtvQkFDakMvRyxNQUFNaUIsU0FBU2xCLE9BQU8sQ0FBQ0MsSUFBSTtvQkFDM0JiO29CQUNBOEgsV0FBVyxJQUFJOUksS0FBSzhDLFNBQVNsQixPQUFPLENBQUNtSCxVQUFVLEVBQUVDLE9BQU87b0JBQ3hEN0IsY0FBYyxJQUFJbkgsS0FBSzhDLFNBQVNsQixPQUFPLENBQUNxSCxhQUFhLEVBQUVELE9BQU87b0JBQzlEakQsVUFBVWpELFNBQVNsQixPQUFPLENBQUNzSCxTQUFTO29CQUNwQ0MsU0FBU3JHLFNBQVNsQixPQUFPLENBQUN1SCxPQUFPLElBQUk7b0JBQ3JDdEMsaUJBQWlCLEVBQUU7b0JBQ25CQyxjQUFjLEVBQUU7b0JBQ2hCQyxvQkFBb0I7b0JBQ3BCQyxnQkFBZ0J5RCxTQUFTeEosT0FBTztvQkFDaENnRyxnQkFBZ0J3RCxTQUFTdkosT0FBTztvQkFDaENnRyxtQkFBbUJ1RCxTQUFTQyxVQUFVO2dCQUN4QztnQkFFQSxJQUFJLENBQUNsSixRQUFRLENBQUMyRCxHQUFHLENBQUNyQyxTQUFTbEIsT0FBTyxDQUFDZ0gsWUFBWSxFQUFFK0I7Z0JBQ2pEakssUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ21DLFNBQVNsQixPQUFPLENBQUNnSCxZQUFZO2dCQUMxRSxPQUFPOUYsU0FBU2xCLE9BQU8sQ0FBQ2dILFlBQVk7WUFDdEMsRUFBRSxPQUFPdkYsT0FBTztnQkFDZDNDLFFBQVEyQyxLQUFLLENBQUMsd0VBQXdFQTtnQkFDdEYsSUFBSSxDQUFDVCxVQUFVLEdBQUc7WUFDcEI7UUFDRjtRQUVBLDJCQUEyQjtRQUMzQixNQUFNcUMsWUFBWXhGLGdEQUFNQTtRQUN4QixNQUFNUSxNQUFNRCxLQUFLQyxHQUFHO1FBRXBCLE1BQU0wSyxhQUE2QjtZQUNqQ3pFLElBQUlqQjtZQUNKcEQ7WUFDQWI7WUFDQTZGLGlCQUFpQixFQUFFO1lBQ25CQyxjQUFjLEVBQUU7WUFDaEJDLG9CQUFvQjtZQUNwQkMsZ0JBQWdCeUQsU0FBU3hKLE9BQU87WUFDaENnRyxnQkFBZ0J3RCxTQUFTdkosT0FBTztZQUNoQ2dHLG1CQUFtQnVELFNBQVNDLFVBQVU7WUFDdEM1QixXQUFXN0k7WUFDWGtILGNBQWNsSDtZQUNkOEYsVUFBVTtZQUNWb0QsU0FBUztRQUNYO1FBRUEsSUFBSSxDQUFDM0gsUUFBUSxDQUFDMkQsR0FBRyxDQUFDRixXQUFXMEY7UUFDN0IsSUFBSSxDQUFDcEQscUJBQXFCO1FBQzFCLE9BQU90QztJQUNUO0lBRUEsTUFBYTJCLFlBQ1gzQixTQUFpQixFQUNqQmpFLE1BQXFCLEVBQ3JCNkYsZUFBaUMsRUFDakNDLFlBQWlDLEVBQ2pDQyxrQkFBMEIsRUFDMUJDLGNBQXNCLEVBQ3RCQyxjQUFzQixFQUN0QkMsaUJBQXlCLEVBR1A7WUFGbEJuQixXQUFBQSxpRUFBb0IsT0FDcEI4RSw4REFBeUIsNkNBQTZDOztRQUV0RSxJQUFJO1lBQ0YsTUFBTWpKLFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUMrRyxHQUFHLENBQUN0RDtZQUNsQyxJQUFJLENBQUNyRCxTQUFTO2dCQUNabEIsUUFBUTJDLEtBQUssQ0FBQyxzQkFBc0I0QjtnQkFDcEMsT0FBTztZQUNUO1lBRUEseUNBQXlDO1lBQ3pDLE1BQU0wQyxrQkFBa0I7Z0JBQ3RCQyxXQUFXNUgsS0FBS0MsR0FBRztnQkFDbkJLLFVBQVUsSUFBSSxDQUFDQSxRQUFRO2dCQUN2QnlGO2dCQUNBZDtZQUNGO1lBRUEsMEVBQTBFO1lBQzFFLElBQUk2RjtZQUVKLElBQUlELG9CQUFvQmpCLFdBQVc7Z0JBQ2pDLGdEQUFnRDtnQkFDaERrQixpQkFBaUJEO2dCQUNqQm5LLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBOERzRSxPQUFoQzZGLGdCQUFlLG1CQUEyQixPQUFWN0Y7WUFDNUUsT0FBTztnQkFDTCxpREFBaUQ7Z0JBQ2pENkYsaUJBQWlCbEosUUFBUXVILE9BQU8sSUFBSTtnQkFDcEMsTUFBTTRCLFlBQVksSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ3pDLEdBQUcsQ0FBQ3REO2dCQUM3QyxJQUFJOEYsYUFBYWhGLFVBQVU7b0JBQ3pCLHFDQUFxQztvQkFDckMrRSxpQkFBaUIsQ0FBQ2xKLFFBQVF1SCxPQUFPLElBQUksS0FBTW5KLENBQUFBLEtBQUtDLEdBQUcsS0FBSzhLLFNBQVE7b0JBQ2hFLHFDQUFxQztvQkFDckMsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQzdGLEdBQUcsQ0FBQ0YsV0FBV2pGLEtBQUtDLEdBQUc7Z0JBQ2hELE9BQU8sSUFBSSxDQUFDOEYsWUFBWWdGLFdBQVc7b0JBQ2pDLG9DQUFvQztvQkFDcENELGlCQUFpQixDQUFDbEosUUFBUXVILE9BQU8sSUFBSSxLQUFNbkosQ0FBQUEsS0FBS0MsR0FBRyxLQUFLOEssU0FBUTtvQkFDaEUsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQzFHLE1BQU0sQ0FBQ1c7Z0JBQ2hDLE9BQU8sSUFBSWMsWUFBWSxDQUFDZ0YsV0FBVztvQkFDakMsMENBQTBDO29CQUMxQyxJQUFJLENBQUNDLGlCQUFpQixDQUFDN0YsR0FBRyxDQUFDRixXQUFXakYsS0FBS0MsR0FBRztnQkFDaEQ7WUFDRjtZQUVBLE1BQU1nTCxpQkFBaUM7Z0JBQ3JDLEdBQUdySixPQUFPO2dCQUNWWjtnQkFDQTZGLGlCQUFpQjt1QkFBSUE7aUJBQWdCO2dCQUNyQ0MsY0FBYzt1QkFBSUE7aUJBQWE7Z0JBQy9CQztnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQW5CO2dCQUNBb0IsY0FBY25ILEtBQUtDLEdBQUc7Z0JBQ3RCa0osU0FBUzJCO1lBQ1g7WUFFQSxzQ0FBc0M7WUFDdENuSSxhQUFhbEMsT0FBTyxDQUFDLDZCQUF1QyxPQUFWd0UsWUFBYXJCLEtBQUtjLFNBQVMsQ0FBQ2lEO1lBRTlFakgsUUFBUUMsR0FBRyxDQUFFLG1EQUF3QztnQkFDbkRzRTtnQkFDQTRCLGlCQUFpQkEsZ0JBQWdCOUUsTUFBTTtnQkFDdkMrRSxjQUFjQSxhQUFhL0UsTUFBTTtnQkFDakMwSSxVQUFVO29CQUFFekQ7b0JBQWdCQztvQkFBZ0JDO2dCQUFrQjtnQkFDOURuQjtnQkFDQW9ELFNBQVMyQjtZQUNYO1lBRUEsSUFBSSxDQUFDdEosUUFBUSxDQUFDMkQsR0FBRyxDQUFDRixXQUFXZ0c7WUFFN0IsK0RBQStEO1lBQy9ELElBQUlsRixVQUFVO2dCQUNaLElBQUksQ0FBQ2YsZ0JBQWdCLENBQUNDO2dCQUN0QnZFLFFBQVFDLEdBQUcsQ0FBQywyQ0FBcUQsT0FBVnNFO1lBQ3pELE9BQU87Z0JBQ0wsSUFBSSxDQUFDRyxtQkFBbUIsQ0FBQ0g7Z0JBQ3pCdkUsUUFBUUMsR0FBRyxDQUFDLDREQUFzRSxPQUFWc0U7WUFDMUU7WUFFQSxtREFBbUQ7WUFDbkQsSUFBSSxJQUFJLENBQUNyQyxVQUFVLElBQUksYUFBa0IsYUFBYTtnQkFDcEQsTUFBTTRGLFFBQVE3RixhQUFhbkMsT0FBTyxDQUFDO2dCQUNuQyxJQUFJZ0ksU0FBU0EsTUFBTXpHLE1BQU0sR0FBRyxJQUFJO29CQUM5QixJQUFJO3dCQUNGLHFEQUFxRDt3QkFDckQsTUFBTSxFQUFFckMsVUFBVSxFQUFFLEdBQUcsTUFBTSw2SUFBbUI7d0JBQ2hELE1BQU1pSixjQUFjOzRCQUNsQjlHLE1BQU1vSixlQUFlcEosSUFBSTs0QkFDekJiLFFBQVFBOzRCQUNSNkYsaUJBQWlCQTs0QkFDakJFLG9CQUFvQkE7NEJBQ3BCQyxnQkFBZ0JBOzRCQUNoQkMsZ0JBQWdCQTs0QkFDaEJDLG1CQUFtQkE7NEJBQ25CbkIsVUFBVUE7NEJBQ1ZtRixtQkFBbUJKO3dCQUNyQjt3QkFFQSxNQUFNcEwsV0FBV3lMLGFBQWEsQ0FBQ2xHLFdBQVcwRDt3QkFDMUNqSSxRQUFRQyxHQUFHLENBQUMsK0JBQStCc0U7b0JBQzdDLEVBQUUsT0FBTzVCLE9BQU87d0JBQ2QsTUFBTTBHLGVBQWUxRyxpQkFBaUJELFFBQVFDLE1BQU0yRyxPQUFPLEdBQUdDLE9BQU81Rzt3QkFDckUzQyxRQUFRd0UsSUFBSSxDQUFDLGtDQUFrQzZFO3dCQUUvQyw2REFBNkQ7d0JBQzdELElBQUlBLGFBQWFHLFFBQVEsQ0FBQyxzQkFBc0I7NEJBQzlDeEosUUFBUUMsR0FBRyxDQUFDOzRCQUNaLElBQUk7Z0NBQ0YseURBQXlEO2dDQUN6RCxNQUFNeUssZUFBZSxNQUFNLElBQUksQ0FBQ1osZ0JBQWdCLENBQUNTLGVBQWVwSixJQUFJLEVBQUViLFFBQVE7b0NBQzVFQyxTQUFTK0Y7b0NBQ1Q5RixTQUFTK0Y7b0NBQ1R5RCxZQUFZeEQ7Z0NBQ2Q7Z0NBRUEsK0NBQStDO2dDQUMvQyxJQUFJLENBQUMxRixRQUFRLENBQUMyRCxHQUFHLENBQUNpRyxjQUFjO29DQUM5QixHQUFHSCxjQUFjO29DQUNqQi9FLElBQUlrRjtnQ0FDTjtnQ0FDQSxJQUFJLENBQUM1SixRQUFRLENBQUM4QyxNQUFNLENBQUNXLFlBQVkscUJBQXFCO2dDQUN0RCxJQUFJLENBQUNvRyxpQkFBaUIsQ0FBQ0QsZUFBZSw0QkFBNEI7Z0NBRWxFMUssUUFBUUMsR0FBRyxDQUFDLGlEQUFpRHlLO2dDQUM3RCxPQUFPOzRCQUNULEVBQUUsT0FBT0UsYUFBYTtnQ0FDcEI1SyxRQUFRMkMsS0FBSyxDQUFDLDJDQUEyQ2lJOzRCQUN6RCx1Q0FBdUM7NEJBQ3pDO3dCQUNGO3dCQUVBLDJEQUEyRDt3QkFDM0QsSUFBSXZCLGFBQWFHLFFBQVEsQ0FBQyxVQUFVSCxhQUFhRyxRQUFRLENBQUMsVUFBVUgsYUFBYUcsUUFBUSxDQUFDLHFCQUFxQkgsYUFBYUcsUUFBUSxDQUFDLGFBQWE7NEJBQ2hKeEosUUFBUUMsR0FBRyxDQUFDOzRCQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRzt3QkFDcEI7b0JBQ0EsOENBQThDO29CQUNoRDtnQkFDRixPQUFPO29CQUNMbEMsUUFBUUMsR0FBRyxDQUFDO29CQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRyxPQUFPLHlDQUF5QztnQkFDcEU7WUFDRjtZQUVBLElBQUksQ0FBQzJFLHFCQUFxQjtZQUMxQixPQUFPO1FBQ1QsRUFBRSxPQUFPbEUsT0FBTztZQUNkM0MsUUFBUTJDLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDLE9BQU87UUFDVDtJQUNGO0lBRU9rSSxZQUFZdEcsU0FBaUIsRUFBeUI7UUFDM0QsT0FBTyxJQUFJLENBQUN6RCxRQUFRLENBQUMrRyxHQUFHLENBQUN0RCxjQUFjO0lBQ3pDO0lBRUEsTUFBYXVHLGNBQWN2RyxTQUFpQixFQUFvQjtRQUM5RCxtRUFBbUU7UUFDbkUsSUFBSSxJQUFJLENBQUNyQyxVQUFVLElBQUksYUFBa0IsYUFBYTtZQUNwRCxNQUFNNEYsUUFBUTdGLGFBQWFuQyxPQUFPLENBQUM7WUFDbkMsSUFBSWdJLE9BQU87Z0JBQ1QsSUFBSTtvQkFDRixNQUFNOUksZ0RBQVVBLENBQUM4TCxhQUFhLENBQUN2RztvQkFDL0J2RSxRQUFRQyxHQUFHLENBQUMsbUNBQW1Dc0U7Z0JBQ2pELEVBQUUsT0FBTzVCLE9BQU87b0JBQ2QsTUFBTTBHLGVBQWUxRyxpQkFBaUJELFFBQVFDLE1BQU0yRyxPQUFPLEdBQUdDLE9BQU81RztvQkFDckUsSUFBSTBHLGFBQWFHLFFBQVEsQ0FBQyxVQUFVSCxhQUFhRyxRQUFRLENBQUMsVUFBVUgsYUFBYUcsUUFBUSxDQUFDLG1CQUFtQjt3QkFDM0d4SixRQUFRQyxHQUFHLENBQUM7b0JBQ2QsT0FBTzt3QkFDTEQsUUFBUTJDLEtBQUssQ0FBQyw0Q0FBNENBO29CQUM1RDtnQkFDQSwyQ0FBMkM7Z0JBQzdDO1lBQ0YsT0FBTztnQkFDTDNDLFFBQVFDLEdBQUcsQ0FBQztZQUNkO1FBQ0Y7UUFFQSxNQUFNOEssVUFBVSxJQUFJLENBQUNqSyxRQUFRLENBQUM4QyxNQUFNLENBQUNXO1FBQ3JDLElBQUl3RyxTQUFTO1lBQ1gsSUFBSSxJQUFJLENBQUNyRSxnQkFBZ0IsS0FBS25DLFdBQVc7Z0JBQ3ZDLElBQUksQ0FBQ21DLGdCQUFnQixHQUFHO2dCQUN4QixNQUFNQyxvQkFBb0IsSUFBSSxDQUFDOUQsb0JBQW9CLENBQUMzRDtnQkFDcEQrQyxhQUFhMkUsVUFBVSxDQUFDRDtZQUMxQjtZQUNBLElBQUksQ0FBQ0UscUJBQXFCO1FBQzVCO1FBQ0EsT0FBT2tFO0lBQ1Q7SUFFT2hELGlCQUFvQztRQUN6QyxPQUFPbkgsTUFBTUMsSUFBSSxDQUFDLElBQUksQ0FBQ0MsUUFBUSxDQUFDQyxNQUFNLElBQUl1RSxHQUFHLENBQUNwRSxDQUFBQSxVQUFZO2dCQUN4RHNFLElBQUl0RSxRQUFRc0UsRUFBRTtnQkFDZHJFLE1BQU1ELFFBQVFDLElBQUk7Z0JBQ2xCNkosTUFBTSxHQUE2QjlKLE9BQTFCQSxRQUFRWixNQUFNLENBQUNDLE9BQU8sRUFBQyxLQUEwQixPQUF2QlcsUUFBUVosTUFBTSxDQUFDRSxPQUFPO2dCQUN6RDRILFdBQVdsSCxRQUFRa0gsU0FBUztnQkFDNUIzQixjQUFjdkYsUUFBUXVGLFlBQVk7Z0JBQ2xDcEIsVUFBVW5FLFFBQVFtRSxRQUFRO2dCQUMxQm9ELFNBQVMsSUFBSSxDQUFDd0MsaUJBQWlCLENBQUMvSixRQUFRc0UsRUFBRTtnQkFDMUMwRixhQUFhaEssUUFBUWtGLFlBQVksQ0FBQy9FLE1BQU07Z0JBQ3hDOEosaUJBQWlCakssUUFBUWtGLFlBQVksQ0FDbENuRixNQUFNLENBQUNtSyxDQUFBQSxRQUFTQSxNQUFNQyxTQUFTLEtBQUssVUFBVUQsTUFBTUUseUJBQXlCLEtBQUtwQyxXQUNsRnpCLE1BQU0sQ0FBQyxDQUFDOEQsS0FBS0gsUUFBVUcsTUFBT0gsQ0FBQUEsTUFBTUUseUJBQXlCLElBQUksSUFBSTtZQUMxRTtJQUNGO0lBRU9FLDJCQUE4QztRQUNuRCxnRUFBZ0U7UUFDaEUsTUFBTUMsbUJBQW1CLElBQUlDLElBQzNCOUssTUFBTUMsSUFBSSxDQUFDLElBQUksQ0FBQ3VDLDJCQUEyQixDQUFDckMsTUFBTSxJQUMvQ3VFLEdBQUcsQ0FBQzVCLENBQUFBLFFBQVNBLE1BQU1hLFNBQVMsRUFDNUJ0RCxNQUFNLENBQUNzRCxDQUFBQSxZQUFhQSxhQUFhQSxjQUFjLGFBQWEsaUNBQWlDOztRQUdsRyw4RkFBOEY7UUFDOUYsSUFBSSxJQUFJLENBQUNtQyxnQkFBZ0IsSUFBSSxJQUFJLENBQUM1RixRQUFRLENBQUM2SyxHQUFHLENBQUMsSUFBSSxDQUFDakYsZ0JBQWdCLEdBQUc7WUFDckUrRSxpQkFBaUJHLEdBQUcsQ0FBQyxJQUFJLENBQUNsRixnQkFBZ0I7UUFDNUM7UUFFQTFHLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0M7WUFDaERtRCw2QkFBNkJ4QyxNQUFNQyxJQUFJLENBQUMsSUFBSSxDQUFDdUMsMkJBQTJCLENBQUNHLE9BQU87WUFDaEZrSSxrQkFBa0I3SyxNQUFNQyxJQUFJLENBQUM0SztZQUM3QkksYUFBYWpMLE1BQU1DLElBQUksQ0FBQyxJQUFJLENBQUNDLFFBQVEsQ0FBQ2dMLElBQUk7WUFDMUNwRixrQkFBa0IsSUFBSSxDQUFDQSxnQkFBZ0I7WUFDdkM5RyxVQUFVLElBQUksQ0FBQ0EsUUFBUTtRQUN6QjtRQUVBLG9GQUFvRjtRQUNwRixNQUFNbU0sb0JBQW9CbkwsTUFBTUMsSUFBSSxDQUFDLElBQUksQ0FBQ0MsUUFBUSxDQUFDQyxNQUFNLElBQ3RERSxNQUFNLENBQUNDLENBQUFBO1lBQ04sTUFBTThLLHFCQUFxQjlLLFFBQVFzRSxFQUFFLElBQUlpRyxpQkFBaUJFLEdBQUcsQ0FBQ3pLLFFBQVFzRSxFQUFFO1lBQ3hFLE1BQU15RyxpQkFBaUIvSyxRQUFRbUUsUUFBUTtZQUN2QyxNQUFNNkcsbUJBQW1CaEwsUUFBUXNFLEVBQUUsS0FBSyxJQUFJLENBQUNrQixnQkFBZ0I7WUFFN0QxRyxRQUFRQyxHQUFHLENBQUMsd0JBQTZCaUIsT0FBZkEsUUFBUXNFLEVBQUUsRUFBQyxNQUFpQixPQUFidEUsUUFBUUMsSUFBSSxFQUFDLE9BQUs7Z0JBQ3pENks7Z0JBQ0FDO2dCQUNBQztnQkFDQUMsZUFBZUgsc0JBQXNCQyxrQkFBa0JDO1lBQ3pEO1lBRUEsT0FBT0Ysc0JBQXNCQyxrQkFBa0JDO1FBQ2pELEdBQ0M1RyxHQUFHLENBQUNwRSxDQUFBQSxVQUFZO2dCQUNmc0UsSUFBSXRFLFFBQVFzRSxFQUFFO2dCQUNkckUsTUFBTUQsUUFBUUMsSUFBSTtnQkFDbEI2SixNQUFNLEdBQTZCOUosT0FBMUJBLFFBQVFaLE1BQU0sQ0FBQ0MsT0FBTyxFQUFDLEtBQTBCLE9BQXZCVyxRQUFRWixNQUFNLENBQUNFLE9BQU87Z0JBQ3pENEgsV0FBV2xILFFBQVFrSCxTQUFTO2dCQUM1QjNCLGNBQWN2RixRQUFRdUYsWUFBWTtnQkFDbENwQixVQUFVO2dCQUNWb0QsU0FBUyxJQUFJLENBQUN3QyxpQkFBaUIsQ0FBQy9KLFFBQVFzRSxFQUFFO2dCQUMxQzBGLGFBQWFoSyxRQUFRa0YsWUFBWSxDQUFDL0UsTUFBTTtnQkFDeEM4SixpQkFBaUJqSyxRQUFRa0YsWUFBWSxDQUNsQ25GLE1BQU0sQ0FBQ21LLENBQUFBLFFBQVNBLE1BQU1DLFNBQVMsS0FBSyxVQUFVRCxNQUFNRSx5QkFBeUIsS0FBS3BDLFdBQ2xGekIsTUFBTSxDQUFDLENBQUM4RCxLQUFLSCxRQUFVRyxNQUFPSCxDQUFBQSxNQUFNRSx5QkFBeUIsSUFBSSxJQUFJO1lBQzFFLElBQ0NySyxNQUFNLENBQUNDLENBQUFBLFVBQVdBLFFBQVFzRSxFQUFFLEdBQUcsOENBQThDO1FBRWhGeEYsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQzhMLGtCQUFrQjFLLE1BQU0sRUFBRTBLLGtCQUFrQnpHLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBTTtnQkFBRUMsSUFBSUQsRUFBRUMsRUFBRTtnQkFBRXJFLE1BQU1vRSxFQUFFcEUsSUFBSTtZQUFDO1FBQzVILE9BQU80SztJQUNUO0lBRU9wQixrQkFBa0JwRyxTQUFpQixFQUFRO1FBQ2hELElBQUksSUFBSSxDQUFDekQsUUFBUSxDQUFDNkssR0FBRyxDQUFDcEgsWUFBWTtZQUNoQyxJQUFJLENBQUNtQyxnQkFBZ0IsR0FBR25DO1lBRXhCLDZGQUE2RjtZQUM3RixNQUFNb0Msb0JBQW9CLElBQUksQ0FBQzlELG9CQUFvQixDQUFDM0Q7WUFDcEQrQyxhQUFhbEMsT0FBTyxDQUFDNEcsbUJBQW1CcEM7WUFDeEMxRSxlQUFlRSxPQUFPLENBQUNiLHFCQUFxQnFGO1lBRTVDLGtFQUFrRTtZQUNsRSwyRUFBMkU7WUFDM0UsTUFBTXJELFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUMrRyxHQUFHLENBQUN0RDtZQUNsQyxJQUFJckQsU0FBUztnQkFDWEEsUUFBUW1FLFFBQVEsR0FBRztnQkFDbkJuRSxRQUFRdUYsWUFBWSxHQUFHbkgsS0FBS0MsR0FBRztnQkFDL0IsSUFBSSxDQUFDdUIsUUFBUSxDQUFDMkQsR0FBRyxDQUFDRixXQUFXckQ7Z0JBQzdCLElBQUksQ0FBQzJGLHFCQUFxQjtnQkFFMUIsOENBQThDO2dCQUM5QyxJQUFJLENBQUN2QyxnQkFBZ0IsQ0FBQ0M7Z0JBQ3RCdkUsUUFBUUMsR0FBRyxDQUFDLGFBQXNELE9BQXpDc0UsV0FBVSxpQ0FBNkMsT0FBZCxJQUFJLENBQUMzRSxRQUFRO2dCQUUvRSwyRUFBMkU7Z0JBQzNFcUUsT0FBT0MsYUFBYSxDQUFDLElBQUlDLGFBQWEsV0FBVztvQkFDL0NWLEtBQUs7b0JBQ0xXLFVBQVVHO29CQUNWRixhQUFhcEM7Z0JBQ2Y7WUFDRjtRQUNGO0lBQ0Y7SUFFT21LLHNCQUFxQztRQUMxQyxPQUFPLElBQUksQ0FBQzFGLGdCQUFnQjtJQUM5QjtJQUVPMkYsc0JBQTRCO1FBQ2pDLGlHQUFpRztRQUNqRyxJQUFJLElBQUksQ0FBQzNGLGdCQUFnQixFQUFFO1lBQ3pCLElBQUksQ0FBQ2hDLG1CQUFtQixDQUFDLElBQUksQ0FBQ2dDLGdCQUFnQjtZQUU5QywwREFBMEQ7WUFDMUQsTUFBTTRGLDhCQUE4QjFMLE1BQU1DLElBQUksQ0FBQyxJQUFJLENBQUN1QywyQkFBMkIsQ0FBQ3JDLE1BQU0sSUFDbkZ3TCxJQUFJLENBQUM3SSxDQUFBQSxRQUFTQSxNQUFNYSxTQUFTLEtBQUssSUFBSSxDQUFDbUMsZ0JBQWdCO1lBRTFELElBQUksQ0FBQzRGLDZCQUE2QjtnQkFDaEMsTUFBTXBMLFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUMrRyxHQUFHLENBQUMsSUFBSSxDQUFDbkIsZ0JBQWdCO2dCQUN2RCxJQUFJeEYsV0FBV0EsUUFBUW1FLFFBQVEsRUFBRTtvQkFDL0JuRSxRQUFRbUUsUUFBUSxHQUFHO29CQUNuQm5FLFFBQVF1RixZQUFZLEdBQUduSCxLQUFLQyxHQUFHO29CQUMvQixJQUFJLENBQUN1QixRQUFRLENBQUMyRCxHQUFHLENBQUMsSUFBSSxDQUFDaUMsZ0JBQWdCLEVBQUV4RjtvQkFDekMsSUFBSSxDQUFDMkYscUJBQXFCO29CQUMxQjdHLFFBQVFDLEdBQUcsQ0FBQyxjQUFvQyxPQUF0QixJQUFJLENBQUN5RyxnQkFBZ0IsRUFBQztnQkFDbEQ7WUFDRixPQUFPO2dCQUNMMUcsUUFBUUMsR0FBRyxDQUFDLHdCQUFvQyxPQUF0QixJQUFJLENBQUN5RyxnQkFBZ0IsRUFBQztZQUNsRDtRQUNGO1FBRUEsSUFBSSxDQUFDQSxnQkFBZ0IsR0FBRztRQUN4QixJQUFJLElBQTZCLEVBQUU7WUFDakMsTUFBTUMsb0JBQW9CLElBQUksQ0FBQzlELG9CQUFvQixDQUFDM0Q7WUFDcEQrQyxhQUFhMkUsVUFBVSxDQUFDRDtZQUN4QjlHLGVBQWUrRyxVQUFVLENBQUMxSDtRQUM1QjtRQUNBYyxRQUFRQyxHQUFHLENBQUMsb0RBQXdELE9BQWQsSUFBSSxDQUFDTCxRQUFRO0lBQ3JFO0lBRU80TSxvQkFBb0JqSSxTQUFpQixFQUFRO1FBQ2xELElBQUksQ0FBQytGLGlCQUFpQixDQUFDN0YsR0FBRyxDQUFDRixXQUFXakYsS0FBS0MsR0FBRztJQUNoRDtJQUVPa04sbUJBQW1CbEksU0FBaUIsRUFBUTtRQUNqRCxNQUFNOEYsWUFBWSxJQUFJLENBQUNDLGlCQUFpQixDQUFDekMsR0FBRyxDQUFDdEQ7UUFDN0MsSUFBSThGLFdBQVc7WUFDYixNQUFNbkosVUFBVSxJQUFJLENBQUNKLFFBQVEsQ0FBQytHLEdBQUcsQ0FBQ3REO1lBQ2xDLElBQUlyRCxTQUFTO2dCQUNYLE1BQU1zSixvQkFBb0JsTCxLQUFLQyxHQUFHLEtBQUs4SztnQkFDdkNuSixRQUFRdUgsT0FBTyxHQUFHLENBQUN2SCxRQUFRdUgsT0FBTyxJQUFJLEtBQUsrQjtnQkFDM0N0SixRQUFRdUYsWUFBWSxHQUFHbkgsS0FBS0MsR0FBRztnQkFDL0IsZ0dBQWdHO2dCQUNoRyxJQUFJLENBQUN1QixRQUFRLENBQUMyRCxHQUFHLENBQUNGLFdBQVdyRDtnQkFDN0IsSUFBSSxDQUFDMkYscUJBQXFCO1lBQzVCO1lBQ0EsSUFBSSxDQUFDeUQsaUJBQWlCLENBQUMxRyxNQUFNLENBQUNXO1FBQ2hDO0lBQ0Y7SUFFT21JLGtCQUFrQm5JLFNBQWlCLEVBQVE7UUFDaEQsTUFBTXJELFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUMrRyxHQUFHLENBQUN0RDtRQUNsQyxJQUFJckQsV0FBV0EsUUFBUW1FLFFBQVEsRUFBRTtZQUMvQm5FLFFBQVFtRSxRQUFRLEdBQUc7WUFDbkJuRSxRQUFRdUYsWUFBWSxHQUFHbkgsS0FBS0MsR0FBRztZQUMvQixJQUFJLENBQUN1QixRQUFRLENBQUMyRCxHQUFHLENBQUNGLFdBQVdyRDtZQUM3QixJQUFJLENBQUMyRixxQkFBcUI7WUFDMUI3RyxRQUFRQyxHQUFHLENBQUMsY0FBd0IsT0FBVnNFLFdBQVU7UUFDdEM7SUFDRjtJQUVPMEcsa0JBQWtCMUcsU0FBaUIsRUFBVTtRQUNsRCxNQUFNckQsVUFBVSxJQUFJLENBQUNKLFFBQVEsQ0FBQytHLEdBQUcsQ0FBQ3REO1FBQ2xDLElBQUksQ0FBQ3JELFNBQVMsT0FBTztRQUVyQixNQUFNbUosWUFBWSxJQUFJLENBQUNDLGlCQUFpQixDQUFDekMsR0FBRyxDQUFDdEQ7UUFDN0MsSUFBSThGLFdBQVc7WUFDYixzRUFBc0U7WUFDdEUsT0FBTyxDQUFDbkosUUFBUXVILE9BQU8sSUFBSSxLQUFNbkosQ0FBQUEsS0FBS0MsR0FBRyxLQUFLOEssU0FBUTtRQUN4RDtRQUVBLGdEQUFnRDtRQUNoRCxPQUFPbkosUUFBUXVILE9BQU8sSUFBSTtJQUM1QjtJQUVBLDBEQUEwRDtJQUMxRCxNQUFha0UsMkJBQTBDO1FBQ3JEM00sUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTSxJQUFJLENBQUM2QiwyQkFBMkI7SUFDeEM7SUFFQSw4REFBOEQ7SUFDdkQ4SyxxQkFBMkI7UUFDaEM1TSxRQUFRQyxHQUFHLENBQUM7UUFDWixJQUFJLENBQUNpQyxVQUFVLEdBQUc7UUFDbEIsSUFBSSxDQUFDSCxjQUFjLEdBQUc7SUFDeEI7SUFFQSx3REFBd0Q7SUFDakQ4SyxlQUFxQjtRQUMxQjdNLFFBQVFDLEdBQUcsQ0FBQztRQUNaLElBQUksQ0FBQ2lDLFVBQVUsR0FBRztRQUNsQixJQUFJLENBQUNwQixRQUFRLENBQUNrSCxLQUFLO1FBQ25CLElBQUksQ0FBQ3RCLGdCQUFnQixHQUFHO1FBQ3hCLElBQUksQ0FBQ3ZFLHVCQUF1QjtJQUM5QjtJQUVPMkssb0JBQW9CdkksU0FBaUIsRUFBaUI7UUFDM0QsTUFBTXJELFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUMrRyxHQUFHLENBQUN0RDtRQUNsQyxJQUFJLENBQUNyRCxTQUFTLE9BQU87UUFFckIsT0FBT2dDLEtBQUtjLFNBQVMsQ0FBQzlDLFNBQVMsTUFBTTtJQUN2QztJQUVPNkwsc0JBQXNCQyxRQUFnQixFQUFpQjtRQUM1RCxJQUFJO1lBQ0YsTUFBTS9FLGNBQWMvRSxLQUFLQyxLQUFLLENBQUM2SjtZQUMvQixNQUFNekksWUFBWXhGLGdEQUFNQTtZQUV4QixNQUFNa08sa0JBQWtDO2dCQUN0QyxHQUFHaEYsV0FBVztnQkFDZHpDLElBQUlqQjtnQkFDSmMsVUFBVTtnQkFDVm9CLGNBQWNuSCxLQUFLQyxHQUFHO1lBQ3hCO1lBRUEsSUFBSSxDQUFDdUIsUUFBUSxDQUFDMkQsR0FBRyxDQUFDRixXQUFXMEk7WUFDN0IsSUFBSSxDQUFDcEcscUJBQXFCO1lBQzFCLE9BQU90QztRQUNULEVBQUUsT0FBTzVCLE9BQU87WUFDZDNDLFFBQVEyQyxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxPQUFPO1FBQ1Q7SUFDRjtJQUVPdUssY0FBYzNJLFNBQWlCLEVBQUU0SSxPQUFlLEVBQVc7UUFDaEUsTUFBTWpNLFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUMrRyxHQUFHLENBQUN0RDtRQUNsQyxJQUFJLENBQUNyRCxTQUFTLE9BQU87UUFFckJBLFFBQVFDLElBQUksR0FBR2dNO1FBQ2ZqTSxRQUFRdUYsWUFBWSxHQUFHbkgsS0FBS0MsR0FBRztRQUMvQixJQUFJLENBQUN1QixRQUFRLENBQUMyRCxHQUFHLENBQUNGLFdBQVdyRDtRQUM3QixJQUFJLENBQUMyRixxQkFBcUI7UUFDMUIsT0FBTztJQUNUO0lBRUEsTUFBYXVHLDJCQUEyQjdJLFNBQWlCLEVBQUV5RSxhQUEwQixFQUFvQjtRQUN2RyxNQUFNOUgsVUFBVSxJQUFJLENBQUNKLFFBQVEsQ0FBQytHLEdBQUcsQ0FBQ3REO1FBQ2xDLElBQUksQ0FBQ3JELFNBQVMsT0FBTztRQUVyQkEsUUFBUThILGFBQWEsR0FBR0E7UUFDeEI5SCxRQUFRdUYsWUFBWSxHQUFHbkgsS0FBS0MsR0FBRztRQUMvQixJQUFJLENBQUN1QixRQUFRLENBQUMyRCxHQUFHLENBQUNGLFdBQVdyRDtRQUU3QiwrQkFBK0I7UUFDL0IsSUFBSSxJQUFJLENBQUNnQixVQUFVLEVBQUU7WUFDbkIsSUFBSTtnQkFDRixNQUFNK0YsY0FBYztvQkFDbEI5RyxNQUFNRCxRQUFRQyxJQUFJO29CQUNsQmIsUUFBUVksUUFBUVosTUFBTTtvQkFDdEI2RixpQkFBaUJqRixRQUFRaUYsZUFBZTtvQkFDeENFLG9CQUFvQm5GLFFBQVFtRixrQkFBa0I7b0JBQzlDQyxnQkFBZ0JwRixRQUFRb0YsY0FBYztvQkFDdENDLGdCQUFnQnJGLFFBQVFxRixjQUFjO29CQUN0Q0MsbUJBQW1CdEYsUUFBUXNGLGlCQUFpQjtvQkFDNUNuQixVQUFVbkUsUUFBUW1FLFFBQVE7b0JBQzFCNEQsZ0JBQWdCRDtnQkFDbEI7Z0JBRUEsTUFBTWhLLGdEQUFVQSxDQUFDeUwsYUFBYSxDQUFDbEcsV0FBVzBEO2dCQUMxQ2pJLFFBQVFDLEdBQUcsQ0FBQyw4Q0FBOENzRTtZQUM1RCxFQUFFLE9BQU81QixPQUFPO2dCQUNkM0MsUUFBUTJDLEtBQUssQ0FBQyx1REFBdURBO1lBQ3JFLDhDQUE4QztZQUNoRDtRQUNGO1FBRUEsSUFBSSxDQUFDa0UscUJBQXFCO1FBQzFCLE9BQU87SUFDVDtJQUVPd0csa0JBQWtCOUksU0FBaUIsRUFBdUI7UUFDL0QsTUFBTXJELFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUMrRyxHQUFHLENBQUN0RDtRQUNsQyxPQUFPckQsVUFBVTtlQUFJQSxRQUFRa0YsWUFBWTtTQUFDLEdBQUcsRUFBRTtJQUNqRDtJQUVPa0gsbUJBQW1CL0ksU0FBaUIsRUFBaUI7UUFDMUQsTUFBTXJELFVBQVUsSUFBSSxDQUFDSixRQUFRLENBQUMrRyxHQUFHLENBQUN0RDtRQUNsQyxJQUFJLENBQUNyRCxTQUFTLE9BQU87UUFFckIsTUFBTXFCLFVBQVU7WUFDZDtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFFRCxNQUFNZ0wsYUFBYTtZQUNqQmhMLFFBQVFpTCxJQUFJLENBQUM7ZUFDVnRNLFFBQVFrRixZQUFZLENBQUNkLEdBQUcsQ0FBQzVCLENBQUFBO29CQU0xQkEsc0JBQ0FBLGlCQUNBQSxxQkFDQUEsZUFFQUEsZUFFQUEsa0NBQ0FBO3VCQWRtQztvQkFDbkMsSUFBSXBFLEtBQUtvRSxNQUFNQyxTQUFTLEVBQUU4SixXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQkFDckQsSUFBSXBPLEtBQUtvRSxNQUFNQyxTQUFTLEVBQUVnSyxZQUFZLEdBQUdELEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQkFDdERoSyxNQUFNc0gsSUFBSTtvQkFDVnRILE1BQU1rSyxhQUFhO29CQUNuQmxLLE1BQU0ySCxTQUFTO29CQUNmM0gsRUFBQUEsdUJBQUFBLE1BQU1tSyxhQUFhLGNBQW5CbkssMkNBQUFBLHFCQUFxQm9LLE9BQU8sQ0FBQzVNLFFBQVFaLE1BQU0sQ0FBQ3lOLFNBQVMsTUFBSztvQkFDMURySyxFQUFBQSxrQkFBQUEsTUFBTXNLLFFBQVEsY0FBZHRLLHNDQUFBQSxnQkFBZ0JvSyxPQUFPLENBQUM1TSxRQUFRWixNQUFNLENBQUN5TixTQUFTLE1BQUs7b0JBQ3JEckssRUFBQUEsc0JBQUFBLE1BQU11SyxZQUFZLGNBQWxCdkssMENBQUFBLG9CQUFvQm9LLE9BQU8sQ0FBQzVNLFFBQVFaLE1BQU0sQ0FBQ3lOLFNBQVMsTUFBSztvQkFDekRySyxFQUFBQSxnQkFBQUEsTUFBTXdLLE1BQU0sY0FBWnhLLG9DQUFBQSxjQUFjb0ssT0FBTyxDQUFDNU0sUUFBUVosTUFBTSxDQUFDeU4sU0FBUyxNQUFLO29CQUNuRHJLLE1BQU1rSyxhQUFhO29CQUNuQmxLLEVBQUFBLGdCQUFBQSxNQUFNeUssTUFBTSxjQUFaekssb0NBQUFBLGNBQWNvSyxPQUFPLENBQUM1TSxRQUFRWixNQUFNLENBQUN5TixTQUFTLE1BQUs7b0JBQ25EckssTUFBTTBLLGFBQWE7b0JBQ25CMUssRUFBQUEsbUNBQUFBLE1BQU0ySyx5QkFBeUIsY0FBL0IzSyx1REFBQUEsaUNBQWlDb0ssT0FBTyxDQUFDNU0sUUFBUVosTUFBTSxDQUFDeU4sU0FBUyxNQUFLO29CQUN0RXJLLEVBQUFBLG1DQUFBQSxNQUFNNEgseUJBQXlCLGNBQS9CNUgsdURBQUFBLGlDQUFpQ29LLE9BQU8sQ0FBQzVNLFFBQVFaLE1BQU0sQ0FBQ3lOLFNBQVMsTUFBSztpQkFDdkUsQ0FBQ1AsSUFBSSxDQUFDOztTQUNSLENBQUNBLElBQUksQ0FBQztRQUVQLE9BQU9EO0lBQ1Q7SUFFT2UsbUJBQXlCO1FBQzlCLElBQUksQ0FBQ3hOLFFBQVEsQ0FBQ2tILEtBQUs7UUFDbkIsSUFBSSxDQUFDdEIsZ0JBQWdCLEdBQUc7UUFDeEIsZ0NBQWdDO1FBQ2hDekUsYUFBYTJFLFVBQVUsQ0FBQzNIO1FBQ3hCLHdDQUF3QztRQUN4QyxNQUFNMEgsb0JBQW9CLElBQUksQ0FBQzlELG9CQUFvQixDQUFDM0Q7UUFDcEQrQyxhQUFhMkUsVUFBVSxDQUFDRDtJQUMxQjtJQUVBLDBCQUEwQjtJQUNuQjRILGVBQ0xoSyxTQUFpQixFQUNqQmlLLGNBU0MsRUFFVztZQURaQyxhQUFBQSxnREFBMkIsYUFBYTswQkFBbkI7UUFFckIsTUFBTUMsV0FBV0MsWUFBWTtZQUMzQixNQUFNQyxPQUFPSjtZQUNiLElBQUksQ0FBQ3RJLFdBQVcsQ0FDZDNCLFdBQ0FxSyxLQUFLdE8sTUFBTSxFQUNYc08sS0FBS3pJLGVBQWUsRUFDcEJ5SSxLQUFLeEksWUFBWSxFQUNqQndJLEtBQUt2SSxrQkFBa0IsRUFDdkJ1SSxLQUFLdEksY0FBYyxFQUNuQnNJLEtBQUtySSxjQUFjLEVBQ25CcUksS0FBS3BJLGlCQUFpQixFQUN0Qm9JLEtBQUt2SixRQUFRO1FBRWpCLEdBQUdvSjtRQUVILE9BQU8sSUFBTUksY0FBY0g7SUFDN0I7SUFocUNBLGFBQXNCO2FBUmQ1TixXQUF3QyxJQUFJdUM7YUFDNUNxRCxtQkFBa0M7YUFDbEN4RSxhQUFzQixLQUFNLGlEQUFpRDs7YUFDN0VILGlCQUEwQixNQUFPLDJDQUEyQzs7YUFDNUV1SSxvQkFBeUMsSUFBSWpILE1BQU8sc0NBQXNDOzthQUUxRkQsOEJBQXVHLElBQUlDO1FBR2pILElBQUksQ0FBQ3pELFFBQVEsR0FBR0Q7UUFDaEJLLFFBQVFDLEdBQUcsQ0FBQyx1REFBMkQsT0FBZCxJQUFJLENBQUNMLFFBQVE7UUFFdEUsd0RBQXdEO1FBQ3hELElBQUksQ0FBQzBLLGlCQUFpQixDQUFDdEMsS0FBSztRQUU1Qiw0RUFBNEU7UUFDNUUsSUFBSSxDQUFDOUYsVUFBVSxHQUFHO1FBRWxCLDRDQUE0QztRQUM1QyxJQUFJLENBQUNDLHVCQUF1QjtRQUM1QixJQUFJLENBQUNZLDZCQUE2QjtRQUNsQyxJQUFJLENBQUM0QixvQkFBb0I7UUFFekIsMkVBQTJFO1FBQzNFLElBQUksQ0FBQ0ssZ0JBQWdCO1FBRXJCLGtDQUFrQztRQUNsQyxJQUFJLENBQUM4QiwyQkFBMkI7UUFFaEMsK0ZBQStGO1FBQy9GZ0ksV0FBVztZQUNULElBQUksQ0FBQ2hOLDJCQUEyQjtRQUNsQyxHQUFHLE9BQU8sMERBQTBEO1FBRXBFOUIsUUFBUUMsR0FBRyxDQUFDLHNEQUEwRCxPQUFkLElBQUksQ0FBQ0wsUUFBUTtJQUN2RTtBQXNvQ0YiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxzcmNcXGxpYlxcc2Vzc2lvbi1tYW5hZ2VyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgVHJhZGluZ1Nlc3Npb24sIFNlc3Npb25NZXRhZGF0YSwgVHJhZGluZ0NvbmZpZywgVGFyZ2V0UHJpY2VSb3csIE9yZGVySGlzdG9yeUVudHJ5LCBBcHBTZXR0aW5ncyB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gJ3V1aWQnO1xuaW1wb3J0IHsgc2Vzc2lvbkFwaSB9IGZyb20gJ0AvbGliL2FwaSc7XG5cbmNvbnN0IFNFU1NJT05TX1NUT1JBR0VfS0VZID0gJ3BsdXRvX3RyYWRpbmdfc2Vzc2lvbnMnO1xuY29uc3QgQ1VSUkVOVF9TRVNTSU9OX0tFWSA9ICdwbHV0b19jdXJyZW50X3Nlc3Npb24nO1xuY29uc3QgQUNUSVZFX1NFU1NJT05TX0tFWSA9ICdwbHV0b19hY3RpdmVfc2Vzc2lvbnMnOyAvLyBUcmFjayBhY3RpdmUgc2Vzc2lvbnMgYWNyb3NzIGFsbCB3aW5kb3dzXG5cbi8vIEdlbmVyYXRlIGEgdW5pcXVlIHdpbmRvdyBJRCBmb3IgdGhpcyBicm93c2VyIHRhYi93aW5kb3dcbmNvbnN0IGdlbmVyYXRlV2luZG93SWQgPSAoKTogc3RyaW5nID0+IHtcbiAgcmV0dXJuIGB3aW5kb3dfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCAxMSl9YDtcbn07XG5cbi8vIEdldCBvciBjcmVhdGUgd2luZG93IElEIGZvciB0aGlzIHRhYlxuY29uc3QgZ2V0V2luZG93SWQgPSAoKTogc3RyaW5nID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gJ3NlcnZlcic7XG5cbiAgLy8gVXNlIHNlc3Npb25TdG9yYWdlICh0YWItc3BlY2lmaWMpIGluc3RlYWQgb2YgbG9jYWxTdG9yYWdlIChzaGFyZWQgYWNyb3NzIHRhYnMpXG4gIGxldCB3aW5kb3dJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3BsdXRvX3dpbmRvd19pZCcpO1xuICBpZiAoIXdpbmRvd0lkKSB7XG4gICAgd2luZG93SWQgPSBnZW5lcmF0ZVdpbmRvd0lkKCk7XG4gICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbSgncGx1dG9fd2luZG93X2lkJywgd2luZG93SWQpO1xuICAgIGNvbnNvbGUubG9nKGDwn4aVIENyZWF0ZWQgbmV3IHdpbmRvdyBJRDogJHt3aW5kb3dJZH1gKTtcbiAgfSBlbHNlIHtcbiAgICBjb25zb2xlLmxvZyhg8J+UhCBVc2luZyBleGlzdGluZyB3aW5kb3cgSUQ6ICR7d2luZG93SWR9YCk7XG4gIH1cbiAgcmV0dXJuIHdpbmRvd0lkO1xufTtcblxuZXhwb3J0IGNsYXNzIFNlc3Npb25NYW5hZ2VyIHtcbiAgcHJpdmF0ZSBzdGF0aWMgaW5zdGFuY2U6IFNlc3Npb25NYW5hZ2VyO1xuICBwcml2YXRlIHNlc3Npb25zOiBNYXA8c3RyaW5nLCBUcmFkaW5nU2Vzc2lvbj4gPSBuZXcgTWFwKCk7XG4gIHByaXZhdGUgY3VycmVudFNlc3Npb25JZDogc3RyaW5nIHwgbnVsbCA9IG51bGw7XG4gIHByaXZhdGUgdXNlQmFja2VuZDogYm9vbGVhbiA9IHRydWU7IC8vIEZsYWcgdG8gZGV0ZXJtaW5lIGlmIHdlIHNob3VsZCB1c2UgYmFja2VuZCBBUElcbiAgcHJpdmF0ZSBpc0luaXRpYWxpemluZzogYm9vbGVhbiA9IGZhbHNlOyAvLyBQcmV2ZW50IG11bHRpcGxlIGluaXRpYWxpemF0aW9uIGF0dGVtcHRzXG4gIHByaXZhdGUgc2Vzc2lvblN0YXJ0VGltZXM6IE1hcDxzdHJpbmcsIG51bWJlcj4gPSBuZXcgTWFwKCk7IC8vIFRyYWNrIHdoZW4gc2Vzc2lvbnMgc3RhcnRlZCBydW5uaW5nXG4gIHByaXZhdGUgd2luZG93SWQ6IHN0cmluZztcbiAgcHJpdmF0ZSBhY3RpdmVTZXNzaW9uc0Fjcm9zc1dpbmRvd3M6IE1hcDxzdHJpbmcsIHsgc2Vzc2lvbklkOiBzdHJpbmc7IHdpbmRvd0lkOiBzdHJpbmc7IHRpbWVzdGFtcDogbnVtYmVyIH0+ID0gbmV3IE1hcCgpO1xuXG4gIHByaXZhdGUgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy53aW5kb3dJZCA9IGdldFdpbmRvd0lkKCk7XG4gICAgY29uc29sZS5sb2coYPCfqp8gU2Vzc2lvbk1hbmFnZXIgaW5pdGlhbGl6ZWQgZm9yIHdpbmRvdzogJHt0aGlzLndpbmRvd0lkfWApO1xuXG4gICAgLy8gQ2xlYXIgYW55IHN0YWxlIHNlc3Npb24gc3RhcnQgdGltZXMgb24gaW5pdGlhbGl6YXRpb25cbiAgICB0aGlzLnNlc3Npb25TdGFydFRpbWVzLmNsZWFyKCk7XG5cbiAgICAvLyBEaXNhYmxlIGJhY2tlbmQgYnkgZGVmYXVsdCAtIHVzZSBsb2NhbFN0b3JhZ2UgbW9kZSBmb3IgYmV0dGVyIHJlbGlhYmlsaXR5XG4gICAgdGhpcy51c2VCYWNrZW5kID0gZmFsc2U7XG5cbiAgICAvLyBMb2FkIHNlc3Npb25zIGFuZCBzZXR1cCBjcm9zcy13aW5kb3cgc3luY1xuICAgIHRoaXMubG9hZFNlc3Npb25zRnJvbVN0b3JhZ2UoKTtcbiAgICB0aGlzLmxvYWRBY3RpdmVTZXNzaW9uc0Zyb21TdG9yYWdlKCk7XG4gICAgdGhpcy5zZXR1cFN0b3JhZ2VMaXN0ZW5lcigpO1xuXG4gICAgLy8gSGFuZGxlIGFwcCByZXN0YXJ0IC0gc3RvcCBhbnkgcnVubmluZyBzZXNzaW9ucyBhbmQgbW92ZSB0byBwYXN0IHNlc3Npb25zXG4gICAgdGhpcy5oYW5kbGVBcHBSZXN0YXJ0KCk7XG5cbiAgICAvLyBDbGVhbiB1cCBzdGFsZSBwZXJzaXN0ZW5jZSBpbmZvXG4gICAgdGhpcy5jbGVhbnVwU3RhbGVQZXJzaXN0ZW5jZUluZm8oKTtcblxuICAgIC8vIENoZWNrIGJhY2tlbmQgY29ubmVjdGlvbiBhbmQgbG9hZCBzZXNzaW9ucyBmcm9tIGJhY2tlbmQgKHdpdGggZGVsYXkgdG8gZW5zdXJlIGF1dGggaXMgcmVhZHkpXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICB0aGlzLmluaXRpYWxpemVCYWNrZW5kQ29ubmVjdGlvbigpO1xuICAgIH0sIDEwMDApOyAvLyAxIHNlY29uZCBkZWxheSB0byBhbGxvdyBhdXRoIHRvIGJlIHByb3Blcmx5IGluaXRpYWxpemVkXG5cbiAgICBjb25zb2xlLmxvZyhg8J+qnyBTZXNzaW9uTWFuYWdlciBpbml0aWFsaXplZCBmb3Igd2luZG93ICR7dGhpcy53aW5kb3dJZH1gKTtcbiAgfVxuXG4gIHB1YmxpYyBzdGF0aWMgZ2V0SW5zdGFuY2UoKTogU2Vzc2lvbk1hbmFnZXIge1xuICAgIGlmICghU2Vzc2lvbk1hbmFnZXIuaW5zdGFuY2UpIHtcbiAgICAgIFNlc3Npb25NYW5hZ2VyLmluc3RhbmNlID0gbmV3IFNlc3Npb25NYW5hZ2VyKCk7XG4gICAgfVxuICAgIHJldHVybiBTZXNzaW9uTWFuYWdlci5pbnN0YW5jZTtcbiAgfVxuXG4gIHByaXZhdGUgZ2VuZXJhdGVTZXNzaW9uTmFtZShjb25maWc6IFRyYWRpbmdDb25maWcpOiBzdHJpbmcge1xuICAgIGNvbnN0IGNyeXB0bzEgPSBjb25maWcuY3J5cHRvMSB8fCAnQ3J5cHRvMSc7XG4gICAgY29uc3QgY3J5cHRvMiA9IGNvbmZpZy5jcnlwdG8yIHx8ICdDcnlwdG8yJztcbiAgICBjb25zdCB0cmFkaW5nTW9kZSA9IGNvbmZpZy50cmFkaW5nTW9kZSB8fCAnU2ltcGxlU3BvdCc7XG5cbiAgICBjb25zdCBiYXNlTmFtZSA9IGAke2NyeXB0bzF9LyR7Y3J5cHRvMn0gJHt0cmFkaW5nTW9kZX1gO1xuXG4gICAgLy8gQ2hlY2sgZm9yIGV4aXN0aW5nIHNlc3Npb25zIHdpdGggdGhlIHNhbWUgYmFzZSBuYW1lXG4gICAgY29uc3QgZXhpc3RpbmdTZXNzaW9ucyA9IEFycmF5LmZyb20odGhpcy5zZXNzaW9ucy52YWx1ZXMoKSk7XG4gICAgY29uc3Qgc2ltaWxhclNlc3Npb25zID0gZXhpc3RpbmdTZXNzaW9ucy5maWx0ZXIoc2Vzc2lvbiA9PlxuICAgICAgc2Vzc2lvbi5uYW1lLnN0YXJ0c1dpdGgoYmFzZU5hbWUpXG4gICAgKTtcblxuICAgIGlmIChzaW1pbGFyU2Vzc2lvbnMubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gYmFzZU5hbWU7XG4gICAgfVxuXG4gICAgLy8gRmluZCB0aGUgaGlnaGVzdCBzZXNzaW9uIG51bWJlclxuICAgIGxldCBtYXhOdW1iZXIgPSAwO1xuICAgIHNpbWlsYXJTZXNzaW9ucy5mb3JFYWNoKHNlc3Npb24gPT4ge1xuICAgICAgY29uc3QgbWF0Y2ggPSBzZXNzaW9uLm5hbWUubWF0Y2gobmV3IFJlZ0V4cChgXiR7YmFzZU5hbWUucmVwbGFjZSgvWy4qKz9eJHt9KCl8W1xcXVxcXFxdL2csICdcXFxcJCYnKX0gU2Vzc2lvbiAoXFxcXGQrKSRgKSk7XG4gICAgICBpZiAobWF0Y2gpIHtcbiAgICAgICAgY29uc3QgbnVtYmVyID0gcGFyc2VJbnQobWF0Y2hbMV0sIDEwKTtcbiAgICAgICAgaWYgKG51bWJlciA+IG1heE51bWJlcikge1xuICAgICAgICAgIG1heE51bWJlciA9IG51bWJlcjtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChzZXNzaW9uLm5hbWUgPT09IGJhc2VOYW1lKSB7XG4gICAgICAgIC8vIElmIHRoZXJlJ3MgYW4gZXhhY3QgbWF0Y2gsIHRyZWF0IGl0IGFzIFwiU2Vzc2lvbiAxXCJcbiAgICAgICAgbWF4TnVtYmVyID0gTWF0aC5tYXgobWF4TnVtYmVyLCAxKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJldHVybiBgJHtiYXNlTmFtZX0gU2Vzc2lvbiAke21heE51bWJlciArIDF9YDtcbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgaW5pdGlhbGl6ZUJhY2tlbmRDb25uZWN0aW9uKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIC8vIFByZXZlbnQgbXVsdGlwbGUgaW5pdGlhbGl6YXRpb24gYXR0ZW1wdHNcbiAgICBpZiAodGhpcy5pc0luaXRpYWxpemluZykge1xuICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyBCYWNrZW5kIGluaXRpYWxpemF0aW9uIGFscmVhZHkgaW4gcHJvZ3Jlc3MsIHNraXBwaW5nJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdGhpcy5pc0luaXRpYWxpemluZyA9IHRydWU7XG5cbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhdXRoZW50aWNhdGVkXG4gICAgICBjb25zdCBhdXRoVG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncGx1dG9BdXRoVG9rZW4nKTtcbiAgICAgIGlmICghYXV0aFRva2VuKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8gTm8gYXV0aCB0b2tlbiBmb3VuZCwgdXNpbmcgbG9jYWxTdG9yYWdlIG1vZGUgdW50aWwgbG9naW4nKTtcbiAgICAgICAgdGhpcy51c2VCYWNrZW5kID0gZmFsc2U7XG4gICAgICAgIHRoaXMubG9hZFNlc3Npb25zRnJvbVN0b3JhZ2UoKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBUZXN0IGJhY2tlbmQgY29ubmVjdGlvbiAoaGVhbHRoIGVuZHBvaW50IGRvZXNuJ3QgbmVlZCBhdXRoKVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnaHR0cDovL2xvY2FsaG9zdDo1MDAwL2hlYWx0aC8nLCB7XG4gICAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgQmFja2VuZCBjb25uZWN0aW9uIGVzdGFibGlzaGVkLCB0ZXN0aW5nIGF1dGggYW5kIGxvYWRpbmcgc2Vzc2lvbnMnKTtcbiAgICAgICAgdGhpcy51c2VCYWNrZW5kID0gdHJ1ZTtcbiAgICAgICAgLy8gTG9hZCBzZXNzaW9ucyBmcm9tIGJhY2tlbmQgd2hlbiBjb25uZWN0aW9uIGlzIGVzdGFibGlzaGVkXG4gICAgICAgIGF3YWl0IHRoaXMubG9hZFNlc3Npb25zRnJvbUJhY2tlbmQoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQmFja2VuZCBoZWFsdGggY2hlY2sgZmFpbGVkJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8gQmFja2VuZCBub3QgYXZhaWxhYmxlLCB1c2luZyBsb2NhbFN0b3JhZ2UgbW9kZTonLCBlcnJvcik7XG4gICAgICB0aGlzLnVzZUJhY2tlbmQgPSBmYWxzZTtcbiAgICAgIHRoaXMubG9hZFNlc3Npb25zRnJvbVN0b3JhZ2UoKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgdGhpcy5pc0luaXRpYWxpemluZyA9IGZhbHNlO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgY2hlY2tCYWNrZW5kQ29ubmVjdGlvbigpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgYXV0aFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3BsdXRvQXV0aFRva2VuJyk7XG4gICAgICBpZiAoIWF1dGhUb2tlbikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9oZWFsdGgvJywge1xuICAgICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHthdXRoVG9rZW59YFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiByZXNwb25zZS5vaztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyBCYWNrZW5kIGNvbm5lY3Rpb24gY2hlY2sgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGdldFdpbmRvd1NwZWNpZmljS2V5KGJhc2VLZXk6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgcmV0dXJuIGAke2Jhc2VLZXl9XyR7dGhpcy53aW5kb3dJZH1gO1xuICB9XG5cbiAgcHJpdmF0ZSBsb2FkQWN0aXZlU2Vzc2lvbnNGcm9tU3RvcmFnZSgpOiB2b2lkIHtcbiAgICB0cnkge1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm47XG5cbiAgICAgIGNvbnN0IGFjdGl2ZVNlc3Npb25zRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKEFDVElWRV9TRVNTSU9OU19LRVkpO1xuICAgICAgaWYgKGFjdGl2ZVNlc3Npb25zRGF0YSkge1xuICAgICAgICBjb25zdCBwYXJzZWRBY3RpdmVTZXNzaW9ucyA9IEpTT04ucGFyc2UoYWN0aXZlU2Vzc2lvbnNEYXRhKTtcbiAgICAgICAgdGhpcy5hY3RpdmVTZXNzaW9uc0Fjcm9zc1dpbmRvd3MgPSBuZXcgTWFwKE9iamVjdC5lbnRyaWVzKHBhcnNlZEFjdGl2ZVNlc3Npb25zKSk7XG5cbiAgICAgICAgLy8gQ2xlYW4gdXAgc3RhbGUgZW50cmllcyAob2xkZXIgdGhhbiA1IG1pbnV0ZXMpXG4gICAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgICAgIGNvbnN0IHN0YWxlVGhyZXNob2xkID0gNSAqIDYwICogMTAwMDsgLy8gNSBtaW51dGVzXG4gICAgICAgIGZvciAoY29uc3QgW2tleSwgZW50cnldIG9mIHRoaXMuYWN0aXZlU2Vzc2lvbnNBY3Jvc3NXaW5kb3dzLmVudHJpZXMoKSkge1xuICAgICAgICAgIGlmIChub3cgLSBlbnRyeS50aW1lc3RhbXAgPiBzdGFsZVRocmVzaG9sZCkge1xuICAgICAgICAgICAgdGhpcy5hY3RpdmVTZXNzaW9uc0Fjcm9zc1dpbmRvd3MuZGVsZXRlKGtleSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuc2F2ZUFjdGl2ZVNlc3Npb25zVG9TdG9yYWdlKCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGFjdGl2ZSBzZXNzaW9ucyBmcm9tIHN0b3JhZ2U6JywgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc2F2ZUFjdGl2ZVNlc3Npb25zVG9TdG9yYWdlKCk6IHZvaWQge1xuICAgIHRyeSB7XG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybjtcblxuICAgICAgY29uc3QgYWN0aXZlU2Vzc2lvbnNPYmplY3QgPSBPYmplY3QuZnJvbUVudHJpZXModGhpcy5hY3RpdmVTZXNzaW9uc0Fjcm9zc1dpbmRvd3MpO1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oQUNUSVZFX1NFU1NJT05TX0tFWSwgSlNPTi5zdHJpbmdpZnkoYWN0aXZlU2Vzc2lvbnNPYmplY3QpKTtcblxuICAgICAgLy8gVHJpZ2dlciBzdG9yYWdlIGV2ZW50IHRvIG5vdGlmeSBvdGhlciB3aW5kb3dzL3RhYnMgKGluY2x1ZGluZyBhZG1pbiBwYW5lbClcbiAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBTdG9yYWdlRXZlbnQoJ3N0b3JhZ2UnLCB7XG4gICAgICAgIGtleTogQUNUSVZFX1NFU1NJT05TX0tFWSxcbiAgICAgICAgbmV3VmFsdWU6IEpTT04uc3RyaW5naWZ5KGFjdGl2ZVNlc3Npb25zT2JqZWN0KSxcbiAgICAgICAgc3RvcmFnZUFyZWE6IGxvY2FsU3RvcmFnZVxuICAgICAgfSkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBhY3RpdmUgc2Vzc2lvbnMgdG8gc3RvcmFnZTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBhZGRBY3RpdmVTZXNzaW9uKHNlc3Npb25JZDogc3RyaW5nKTogdm9pZCB7XG4gICAgLy8gVmFsaWRhdGUgc2Vzc2lvbiBJRCBiZWZvcmUgYWRkaW5nXG4gICAgaWYgKCFzZXNzaW9uSWQgfHwgc2Vzc2lvbklkID09PSAndW5kZWZpbmVkJyB8fCBzZXNzaW9uSWQgPT09ICdudWxsJykge1xuICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gQXR0ZW1wdGVkIHRvIGFkZCBpbnZhbGlkIHNlc3Npb24gSUQgdG8gYWN0aXZlIHNlc3Npb25zOicsIHNlc3Npb25JZCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3Qga2V5ID0gYCR7dGhpcy53aW5kb3dJZH1fJHtzZXNzaW9uSWR9YDtcbiAgICB0aGlzLmFjdGl2ZVNlc3Npb25zQWNyb3NzV2luZG93cy5zZXQoa2V5LCB7XG4gICAgICBzZXNzaW9uSWQsXG4gICAgICB3aW5kb3dJZDogdGhpcy53aW5kb3dJZCxcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgIH0pO1xuICAgIHRoaXMuc2F2ZUFjdGl2ZVNlc3Npb25zVG9TdG9yYWdlKCk7XG4gIH1cblxuICBwcml2YXRlIHJlbW92ZUFjdGl2ZVNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBjb25zdCBrZXkgPSBgJHt0aGlzLndpbmRvd0lkfV8ke3Nlc3Npb25JZH1gO1xuICAgIHRoaXMuYWN0aXZlU2Vzc2lvbnNBY3Jvc3NXaW5kb3dzLmRlbGV0ZShrZXkpO1xuICAgIHRoaXMuc2F2ZUFjdGl2ZVNlc3Npb25zVG9TdG9yYWdlKCk7XG4gIH1cblxuICBwcml2YXRlIHNldHVwU3RvcmFnZUxpc3RlbmVyKCk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuO1xuXG4gICAgLy8gTGlzdGVuIGZvciBzdG9yYWdlIGNoYW5nZXMgZnJvbSBvdGhlciB3aW5kb3dzXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3N0b3JhZ2UnLCAoZXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC5rZXkgPT09IFNFU1NJT05TX1NUT1JBR0VfS0VZICYmIGV2ZW50Lm5ld1ZhbHVlKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgLy8gUmVsb2FkIHNlc3Npb25zIHdoZW4gdGhleSBjaGFuZ2UgaW4gYW5vdGhlciB3aW5kb3dcbiAgICAgICAgICBjb25zdCBwYXJzZWRTZXNzaW9ucyA9IEpTT04ucGFyc2UoZXZlbnQubmV3VmFsdWUpO1xuICAgICAgICAgIHRoaXMuc2Vzc2lvbnMgPSBuZXcgTWFwKE9iamVjdC5lbnRyaWVzKHBhcnNlZFNlc3Npb25zKSk7XG4gICAgICAgICAgY29uc29sZS5sb2coYPCflIQgU2Vzc2lvbnMgc3luY2VkIGZyb20gYW5vdGhlciB3aW5kb3cgKCR7dGhpcy5zZXNzaW9ucy5zaXplfSBzZXNzaW9ucylgKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc3luYyBzZXNzaW9ucyBmcm9tIHN0b3JhZ2UgZXZlbnQ6JywgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSk7XG4gIH1cblxuICBwcml2YXRlIGhhbmRsZUFwcFJlc3RhcnQoKTogdm9pZCB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIENoZWNraW5nIGZvciBhcHAgcmVzdGFydCBhbmQgY2xlYW5pbmcgdXAgcnVubmluZyBzZXNzaW9ucy4uLicpO1xuXG4gICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGEgZnJlc2ggYXBwIHN0YXJ0IGJ5IGxvb2tpbmcgZm9yIGEgcmVzdGFydCBtYXJrZXJcbiAgICAgIGNvbnN0IGxhc3RBcHBDbG9zZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdwbHV0b19sYXN0X2FwcF9jbG9zZScpO1xuICAgICAgY29uc3QgYXBwU3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcblxuICAgICAgLy8gSWYgbm8gY2xvc2UgbWFya2VyIG9yIGl0J3MgYmVlbiBtb3JlIHRoYW4gNSBtaW51dGVzLCBjb25zaWRlciBpdCBhbiBhcHAgcmVzdGFydFxuICAgICAgY29uc3QgaXNBcHBSZXN0YXJ0ID0gIWxhc3RBcHBDbG9zZSB8fCAoYXBwU3RhcnRUaW1lIC0gcGFyc2VJbnQobGFzdEFwcENsb3NlKSkgPiA1ICogNjAgKiAxMDAwO1xuXG4gICAgICBpZiAoaXNBcHBSZXN0YXJ0KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIEFwcCByZXN0YXJ0IGRldGVjdGVkIC0gY2xlYW5pbmcgdXAgcnVubmluZyBzZXNzaW9ucycpO1xuXG4gICAgICAgIC8vIEZpbmQgYWxsIHNlc3Npb25zIG1hcmtlZCBhcyBhY3RpdmUvcnVubmluZ1xuICAgICAgICBjb25zdCBydW5uaW5nU2Vzc2lvbnMgPSBBcnJheS5mcm9tKHRoaXMuc2Vzc2lvbnMudmFsdWVzKCkpLmZpbHRlcihzZXNzaW9uID0+IHNlc3Npb24uaXNBY3RpdmUpO1xuXG4gICAgICAgIGlmIChydW5uaW5nU2Vzc2lvbnMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5uRIEZvdW5kICR7cnVubmluZ1Nlc3Npb25zLmxlbmd0aH0gcnVubmluZyBzZXNzaW9ucyB0byBjbGVhbiB1cDpgLFxuICAgICAgICAgICAgcnVubmluZ1Nlc3Npb25zLm1hcChzID0+ICh7IGlkOiBzLmlkLCBuYW1lOiBzLm5hbWUgfSkpKTtcblxuICAgICAgICAgIHJ1bm5pbmdTZXNzaW9ucy5mb3JFYWNoKHNlc3Npb24gPT4ge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgLy8gQ3JlYXRlIGF1dG8tc2F2ZWQgdmVyc2lvbiBpbiBwYXN0IHNlc3Npb25zXG4gICAgICAgICAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywge1xuICAgICAgICAgICAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgICAgICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICAgICAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgICAgICAgICAgICBtaW51dGU6ICcyLWRpZ2l0JyxcbiAgICAgICAgICAgICAgICBob3VyMTI6IGZhbHNlXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICBjb25zdCBhdXRvU2F2ZWROYW1lID0gYCR7c2Vzc2lvbi5uYW1lfSAoQXV0b1NhdmVkICR7dGltZXN0YW1wfSlgO1xuXG4gICAgICAgICAgICAgIC8vIENyZWF0ZSBuZXcgc2Vzc2lvbiBmb3IgdGhlIGF1dG8tc2F2ZWQgdmVyc2lvblxuICAgICAgICAgICAgICBjb25zdCBhdXRvU2F2ZWRJZCA9IHRoaXMuY3JlYXRlTmV3U2Vzc2lvblN5bmMoYXV0b1NhdmVkTmFtZSwgc2Vzc2lvbi5jb25maWcpO1xuXG4gICAgICAgICAgICAgIC8vIFNhdmUgdGhlIGF1dG8tc2F2ZWQgc2Vzc2lvbiB3aXRoIGFsbCBwcm9ncmVzcyBhcyBpbmFjdGl2ZVxuICAgICAgICAgICAgICB0aGlzLnNhdmVTZXNzaW9uKFxuICAgICAgICAgICAgICAgIGF1dG9TYXZlZElkLFxuICAgICAgICAgICAgICAgIHNlc3Npb24uY29uZmlnLFxuICAgICAgICAgICAgICAgIHNlc3Npb24udGFyZ2V0UHJpY2VSb3dzLFxuICAgICAgICAgICAgICAgIHNlc3Npb24ub3JkZXJIaXN0b3J5LFxuICAgICAgICAgICAgICAgIHNlc3Npb24uY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgICAgICAgIHNlc3Npb24uY3J5cHRvMUJhbGFuY2UsXG4gICAgICAgICAgICAgICAgc2Vzc2lvbi5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgICAgICAgICBzZXNzaW9uLnN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICAgICAgICAgIGZhbHNlIC8vIE1hcmsgYXMgaW5hY3RpdmUgKHBhc3Qgc2Vzc2lvbilcbiAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICAvLyBNYXJrIG9yaWdpbmFsIHNlc3Npb24gYXMgaW5hY3RpdmVcbiAgICAgICAgICAgICAgc2Vzc2lvbi5pc0FjdGl2ZSA9IGZhbHNlO1xuICAgICAgICAgICAgICBzZXNzaW9uLmxhc3RNb2RpZmllZCA9IERhdGUubm93KCk7XG4gICAgICAgICAgICAgIHRoaXMuc2Vzc2lvbnMuc2V0KHNlc3Npb24uaWQsIHNlc3Npb24pO1xuXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgQXV0by1zYXZlZCBydW5uaW5nIHNlc3Npb24gXCIke3Nlc3Npb24ubmFtZX1cIiB0byBwYXN0IHNlc3Npb25zIGFzIFwiJHthdXRvU2F2ZWROYW1lfVwiYCk7XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGDinYwgRmFpbGVkIHRvIGF1dG8tc2F2ZSBydW5uaW5nIHNlc3Npb24gXCIke3Nlc3Npb24ubmFtZX1cIjpgLCBlcnJvcik7XG5cbiAgICAgICAgICAgICAgLy8gQXQgbWluaW11bSwgbWFyayB0aGUgc2Vzc2lvbiBhcyBpbmFjdGl2ZVxuICAgICAgICAgICAgICBzZXNzaW9uLmlzQWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICAgIHNlc3Npb24ubGFzdE1vZGlmaWVkID0gRGF0ZS5ub3coKTtcbiAgICAgICAgICAgICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbi5pZCwgc2Vzc2lvbik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICAvLyBDbGVhciBjdXJyZW50IHNlc3Npb24gc2luY2UgYWxsIHJ1bm5pbmcgc2Vzc2lvbnMgaGF2ZSBiZWVuIHN0b3BwZWRcbiAgICAgICAgICB0aGlzLmN1cnJlbnRTZXNzaW9uSWQgPSBudWxsO1xuICAgICAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uS2V5ID0gdGhpcy5nZXRXaW5kb3dTcGVjaWZpY0tleShDVVJSRU5UX1NFU1NJT05fS0VZKTtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShjdXJyZW50U2Vzc2lvbktleSk7XG4gICAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbShDVVJSRU5UX1NFU1NJT05fS0VZKTtcblxuICAgICAgICAgIC8vIFNhdmUgdXBkYXRlZCBzZXNzaW9uc1xuICAgICAgICAgIHRoaXMuc2F2ZVNlc3Npb25zVG9TdG9yYWdlKCk7XG5cbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIEFwcCByZXN0YXJ0IGNsZWFudXAgY29tcGxldGVkIC0gYWxsIHJ1bm5pbmcgc2Vzc2lvbnMgbW92ZWQgdG8gcGFzdCBzZXNzaW9ucycpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgTm8gcnVubmluZyBzZXNzaW9ucyBmb3VuZCAtIG5vIGNsZWFudXAgbmVlZGVkJyk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgTm9ybWFsIGFwcCBjb250aW51YXRpb24gLSBubyBjbGVhbnVwIG5lZWRlZCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBTZXQgbWFya2VyIGZvciBuZXh0IGFwcCBjbG9zZSBkZXRlY3Rpb25cbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdwbHV0b19hcHBfc3RhcnQnLCBhcHBTdGFydFRpbWUudG9TdHJpbmcoKSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGR1cmluZyBhcHAgcmVzdGFydCBjbGVhbnVwOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGNsZWFudXBTdGFsZVBlcnNpc3RlbmNlSW5mbygpOiB2b2lkIHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfp7kgQ2xlYW5pbmcgdXAgc3RhbGUgcGVyc2lzdGVuY2UgaW5mby4uLicpO1xuXG4gICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuICAgICAgY29uc3Qgc3RhbGVUaHJlc2hvbGQgPSAyNCAqIDYwICogNjAgKiAxMDAwOyAvLyAyNCBob3Vyc1xuXG4gICAgICAvLyBHZXQgYWxsIGxvY2FsU3RvcmFnZSBrZXlzIHRoYXQgYXJlIHBlcnNpc3RlbmNlIGluZm9cbiAgICAgIGNvbnN0IGtleXNUb1JlbW92ZTogc3RyaW5nW10gPSBbXTtcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbG9jYWxTdG9yYWdlLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGtleSA9IGxvY2FsU3RvcmFnZS5rZXkoaSk7XG4gICAgICAgIGlmIChrZXkgJiYga2V5LnN0YXJ0c1dpdGgoJ3BsdXRvX3Nlc3Npb25fcGVyc2lzdGVuY2VfJykpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgcGVyc2lzdGVuY2VJbmZvID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbShrZXkpIHx8ICd7fScpO1xuICAgICAgICAgICAgaWYgKHBlcnNpc3RlbmNlSW5mby5sYXN0U2F2ZWQgJiYgKG5vdyAtIHBlcnNpc3RlbmNlSW5mby5sYXN0U2F2ZWQpID4gc3RhbGVUaHJlc2hvbGQpIHtcbiAgICAgICAgICAgICAga2V5c1RvUmVtb3ZlLnB1c2goa2V5KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgLy8gSW52YWxpZCBKU09OLCByZW1vdmUgaXRcbiAgICAgICAgICAgIGtleXNUb1JlbW92ZS5wdXNoKGtleSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIFJlbW92ZSBzdGFsZSBwZXJzaXN0ZW5jZSBpbmZvXG4gICAgICBrZXlzVG9SZW1vdmUuZm9yRWFjaChrZXkgPT4ge1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShrZXkpO1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+Xke+4jyBSZW1vdmVkIHN0YWxlIHBlcnNpc3RlbmNlIGluZm86ICR7a2V5fWApO1xuICAgICAgfSk7XG5cbiAgICAgIGlmIChrZXlzVG9SZW1vdmUubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zb2xlLmxvZyhg4pyFIENsZWFuZWQgdXAgJHtrZXlzVG9SZW1vdmUubGVuZ3RofSBzdGFsZSBwZXJzaXN0ZW5jZSBlbnRyaWVzYCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIE5vIHN0YWxlIHBlcnNpc3RlbmNlIGluZm8gZm91bmQnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGNsZWFuaW5nIHVwIHBlcnNpc3RlbmNlIGluZm86JywgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgbG9hZFNlc3Npb25zRnJvbVN0b3JhZ2UoKTogdm9pZCB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuO1xuXG4gICAgICAvLyBMb2FkIHNlc3Npb25zIGZyb20gc2hhcmVkIHN0b3JhZ2UgKGFsbCB3aW5kb3dzIHNlZSBzYW1lIHNlc3Npb25zKVxuICAgICAgY29uc3Qgc2Vzc2lvbnNEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oU0VTU0lPTlNfU1RPUkFHRV9LRVkpO1xuXG4gICAgICAvLyBUcnkgdG8gbG9hZCBjdXJyZW50IHNlc3Npb24gZnJvbSBtdWx0aXBsZSBzb3VyY2VzIGZvciBiZXR0ZXIgcGVyc2lzdGVuY2VcbiAgICAgIGxldCBjdXJyZW50U2Vzc2lvbklkOiBzdHJpbmcgfCBudWxsID0gbnVsbDtcblxuICAgICAgLy8gMS4gRmlyc3QgdHJ5IHdpbmRvdy1zcGVjaWZpYyBzdG9yYWdlIChmb3IgbmV3IHRhYnMpXG4gICAgICBjb25zdCB3aW5kb3dTcGVjaWZpY0tleSA9IHRoaXMuZ2V0V2luZG93U3BlY2lmaWNLZXkoQ1VSUkVOVF9TRVNTSU9OX0tFWSk7XG4gICAgICBjdXJyZW50U2Vzc2lvbklkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0od2luZG93U3BlY2lmaWNLZXkpO1xuXG4gICAgICAvLyAyLiBJZiBub3QgZm91bmQsIHRyeSBzZXNzaW9uU3RvcmFnZSAoc3Vydml2ZXMgcGFnZSByZWZyZXNoKVxuICAgICAgaWYgKCFjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICAgIGN1cnJlbnRTZXNzaW9uSWQgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKENVUlJFTlRfU0VTU0lPTl9LRVkpO1xuICAgICAgfVxuXG4gICAgICAvLyAzLiBJZiBzdGlsbCBub3QgZm91bmQsIGxvb2sgZm9yIGFueSBhY3RpdmUgc2Vzc2lvbiAoZmFsbGJhY2spXG4gICAgICBpZiAoIWN1cnJlbnRTZXNzaW9uSWQgJiYgc2Vzc2lvbnNEYXRhKSB7XG4gICAgICAgIGNvbnN0IHBhcnNlZFNlc3Npb25zID0gSlNPTi5wYXJzZShzZXNzaW9uc0RhdGEpO1xuICAgICAgICBjb25zdCBhY3RpdmVTZXNzaW9ucyA9IE9iamVjdC5lbnRyaWVzKHBhcnNlZFNlc3Npb25zKS5maWx0ZXIoKFtfLCBzZXNzaW9uXTogW3N0cmluZywgYW55XSkgPT5cbiAgICAgICAgICBzZXNzaW9uLmlzQWN0aXZlICYmIHNlc3Npb24ubGFzdE1vZGlmaWVkICYmIChEYXRlLm5vdygpIC0gc2Vzc2lvbi5sYXN0TW9kaWZpZWQpIDwgMzAgKiA2MCAqIDEwMDAgLy8gQWN0aXZlIHdpdGhpbiBsYXN0IDMwIG1pbnV0ZXNcbiAgICAgICAgKTtcbiAgICAgICAgaWYgKGFjdGl2ZVNlc3Npb25zLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAvLyBHZXQgdGhlIG1vc3QgcmVjZW50bHkgYWN0aXZlIHNlc3Npb25cbiAgICAgICAgICBjb25zdCBtb3N0UmVjZW50U2Vzc2lvbiA9IGFjdGl2ZVNlc3Npb25zLnJlZHVjZSgobGF0ZXN0OiBbc3RyaW5nLCBhbnldLCBjdXJyZW50OiBbc3RyaW5nLCBhbnldKSA9PlxuICAgICAgICAgICAgKGN1cnJlbnRbMV0gYXMgYW55KS5sYXN0TW9kaWZpZWQgPiAobGF0ZXN0WzFdIGFzIGFueSkubGFzdE1vZGlmaWVkID8gY3VycmVudCA6IGxhdGVzdFxuICAgICAgICAgICk7XG4gICAgICAgICAgY3VycmVudFNlc3Npb25JZCA9IG1vc3RSZWNlbnRTZXNzaW9uWzBdO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIFJlc3RvcmVkIG1vc3QgcmVjZW50IGFjdGl2ZSBzZXNzaW9uOiAke2N1cnJlbnRTZXNzaW9uSWR9YCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKHNlc3Npb25zRGF0YSkge1xuICAgICAgICBjb25zdCBwYXJzZWRTZXNzaW9ucyA9IEpTT04ucGFyc2Uoc2Vzc2lvbnNEYXRhKTtcbiAgICAgICAgdGhpcy5zZXNzaW9ucyA9IG5ldyBNYXAoT2JqZWN0LmVudHJpZXMocGFyc2VkU2Vzc2lvbnMpKTtcbiAgICAgIH1cblxuICAgICAgdGhpcy5jdXJyZW50U2Vzc2lvbklkID0gY3VycmVudFNlc3Npb25JZDtcblxuICAgICAgLy8gU3RvcmUgY3VycmVudCBzZXNzaW9uIGluIHNlc3Npb25TdG9yYWdlIGZvciBwYWdlIHJlZnJlc2ggcGVyc2lzdGVuY2VcbiAgICAgIGlmIChjdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oQ1VSUkVOVF9TRVNTSU9OX0tFWSwgY3VycmVudFNlc3Npb25JZCk7XG5cbiAgICAgICAgLy8gRW5zdXJlIHRoZSBjdXJyZW50IHNlc3Npb24gaXMgbWFya2VkIGFzIGFjdGl2ZSBpZiBpdCBleGlzdHNcbiAgICAgICAgY29uc3QgY3VycmVudFNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChjdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgICAgaWYgKGN1cnJlbnRTZXNzaW9uICYmIGN1cnJlbnRTZXNzaW9uLmlzQWN0aXZlKSB7XG4gICAgICAgICAgdGhpcy5hZGRBY3RpdmVTZXNzaW9uKGN1cnJlbnRTZXNzaW9uSWQpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgUmVzdG9yZWQgYWN0aXZlIHNlc3Npb24gdHJhY2tpbmcgZm9yOiAke2N1cnJlbnRTZXNzaW9uSWR9YCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYPCfk4IgTG9hZGVkICR7dGhpcy5zZXNzaW9ucy5zaXplfSBzaGFyZWQgc2Vzc2lvbnMgZm9yIHdpbmRvdyAke3RoaXMud2luZG93SWR9LCBjdXJyZW50OiAke2N1cnJlbnRTZXNzaW9uSWR9YCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHNlc3Npb25zIGZyb20gc3RvcmFnZTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBsb2FkU2Vzc2lvbnNGcm9tQmFja2VuZCgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBpcyBhdXRoZW50aWNhdGVkIGJlZm9yZSBtYWtpbmcgQVBJIGNhbGxzXG4gICAgICBjb25zdCB0b2tlbiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3BsdXRvQXV0aFRva2VuJykgOiBudWxsO1xuICAgICAgaWYgKCF0b2tlbiB8fCB0b2tlbi5sZW5ndGggPCAxMCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pqg77iPIEludmFsaWQgb3IgbWlzc2luZyBhdXRoIHRva2VuLCBza2lwcGluZyBiYWNrZW5kIHNlc3Npb24gbG9hZGluZycpO1xuICAgICAgICB0aGlzLnVzZUJhY2tlbmQgPSBmYWxzZTsgLy8gRGlzYWJsZSBiYWNrZW5kIG1vZGVcbiAgICAgICAgdGhpcy5sb2FkU2Vzc2lvbnNGcm9tU3RvcmFnZSgpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIExvYWRpbmcgc2Vzc2lvbnMgZnJvbSBiYWNrZW5kLi4uJyk7XG4gICAgICBjb25zdCB7IHNlc3Npb25BcGkgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvYXBpJyk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNlc3Npb25BcGkuZ2V0QWxsU2Vzc2lvbnModHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIEJhY2tlbmQgcmVzcG9uc2UgcmVjZWl2ZWQ6JywgcmVzcG9uc2UpO1xuXG4gICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2Uuc2Vzc2lvbnMpIHtcbiAgICAgICAgLy8gQ29udmVydCBiYWNrZW5kIHNlc3Npb25zIHRvIG91ciBpbnRlcm5hbCBmb3JtYXRcbiAgICAgICAgdGhpcy5zZXNzaW9ucy5jbGVhcigpO1xuICAgICAgICByZXNwb25zZS5zZXNzaW9ucy5mb3JFYWNoKChzZXNzaW9uOiBhbnkpID0+IHtcbiAgICAgICAgICBjb25zdCBzZXNzaW9uRGF0YTogVHJhZGluZ1Nlc3Npb24gPSB7XG4gICAgICAgICAgICBpZDogc2Vzc2lvbi5zZXNzaW9uX3V1aWQsXG4gICAgICAgICAgICBuYW1lOiBzZXNzaW9uLm5hbWUsXG4gICAgICAgICAgICBjb25maWc6IEpTT04ucGFyc2Uoc2Vzc2lvbi5jb25maWdfc25hcHNob3QgfHwgJ3t9JyksXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKHNlc3Npb24uY3JlYXRlZF9hdCkuZ2V0VGltZSgpLFxuICAgICAgICAgICAgbGFzdE1vZGlmaWVkOiBuZXcgRGF0ZShzZXNzaW9uLmxhc3RfbW9kaWZpZWQpLmdldFRpbWUoKSxcbiAgICAgICAgICAgIGlzQWN0aXZlOiBzZXNzaW9uLmlzX2FjdGl2ZSxcbiAgICAgICAgICAgIHJ1bnRpbWU6IHNlc3Npb24ucnVudGltZSB8fCAwLFxuICAgICAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBzZXNzaW9uLnRhcmdldF9wcmljZV9yb3dzID8gSlNPTi5wYXJzZShzZXNzaW9uLnRhcmdldF9wcmljZV9yb3dzKSA6IFtdLFxuICAgICAgICAgICAgb3JkZXJIaXN0b3J5OiBzZXNzaW9uLm9yZGVyX2hpc3RvcnkgPyBKU09OLnBhcnNlKHNlc3Npb24ub3JkZXJfaGlzdG9yeSkgOiBbXSxcbiAgICAgICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZTogc2Vzc2lvbi5jdXJyZW50X21hcmtldF9wcmljZSB8fCAxMDAwMDAsXG4gICAgICAgICAgICBjcnlwdG8xQmFsYW5jZTogc2Vzc2lvbi5jcnlwdG8xX2JhbGFuY2UgfHwgMTAwMDAsXG4gICAgICAgICAgICBjcnlwdG8yQmFsYW5jZTogc2Vzc2lvbi5jcnlwdG8yX2JhbGFuY2UgfHwgMTAwMDAsXG4gICAgICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogc2Vzc2lvbi5zdGFibGVjb2luX2JhbGFuY2UgfHwgMTAwMDAsXG4gICAgICAgICAgICBhbGFybVNldHRpbmdzOiBzZXNzaW9uLmFsYXJtX3NldHRpbmdzID8gSlNPTi5wYXJzZShzZXNzaW9uLmFsYXJtX3NldHRpbmdzKSA6IHVuZGVmaW5lZFxuICAgICAgICAgIH07XG4gICAgICAgICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbi5zZXNzaW9uX3V1aWQsIHNlc3Npb25EYXRhKTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gRmluZCBhY3RpdmUgc2Vzc2lvbiBhbmQgcmVzdG9yZSBpdCBwcm9wZXJseVxuICAgICAgICBjb25zdCBhY3RpdmVTZXNzaW9uID0gcmVzcG9uc2Uuc2Vzc2lvbnMuZmluZCgoczogYW55KSA9PiBzLmlzX2FjdGl2ZSk7XG4gICAgICAgIGlmIChhY3RpdmVTZXNzaW9uKSB7XG4gICAgICAgICAgdGhpcy5jdXJyZW50U2Vzc2lvbklkID0gYWN0aXZlU2Vzc2lvbi5zZXNzaW9uX3V1aWQ7XG4gICAgICAgICAgLy8gU3RvcmUgaW4gYm90aCBsb2NhbFN0b3JhZ2UgYW5kIHNlc3Npb25TdG9yYWdlIGZvciBwZXJzaXN0ZW5jZVxuICAgICAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uS2V5ID0gdGhpcy5nZXRXaW5kb3dTcGVjaWZpY0tleShDVVJSRU5UX1NFU1NJT05fS0VZKTtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShjdXJyZW50U2Vzc2lvbktleSwgYWN0aXZlU2Vzc2lvbi5zZXNzaW9uX3V1aWQpO1xuICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oQ1VSUkVOVF9TRVNTSU9OX0tFWSwgYWN0aXZlU2Vzc2lvbi5zZXNzaW9uX3V1aWQpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDinIUgUmVzdG9yZWQgYWN0aXZlIHNlc3Npb24gZnJvbSBiYWNrZW5kOiAke2FjdGl2ZVNlc3Npb24uc2Vzc2lvbl91dWlkfWApO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQWxzbyBzYXZlIHRvIGxvY2FsU3RvcmFnZSBhcyBiYWNrdXBcbiAgICAgICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcblxuICAgICAgICBjb25zb2xlLmxvZyhg8J+TgiBMb2FkZWQgJHt0aGlzLnNlc3Npb25zLnNpemV9IHNlc3Npb25zIGZyb20gYmFja2VuZGApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFN0cmluZyhlcnJvcik7XG5cbiAgICAgIC8vIEhhbmRsZSBhdXRoZW50aWNhdGlvbiBlcnJvcnMgZ3JhY2VmdWxseVxuICAgICAgaWYgKGVycm9yTWVzc2FnZS5pbmNsdWRlcygnQXV0aGVudGljYXRpb24nKSB8fCBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJzQwMScpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnNDIyJykpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflJAgQXV0aGVudGljYXRpb24gaXNzdWUgZGV0ZWN0ZWQsIGRpc2FibGluZyBiYWNrZW5kIG1vZGUnKTtcbiAgICAgICAgdGhpcy51c2VCYWNrZW5kID0gZmFsc2U7IC8vIERpc2FibGUgYmFja2VuZCBtb2RlIHRvIHByZXZlbnQgZnV0dXJlIEFQSSBjYWxsc1xuICAgICAgfSBlbHNlIGlmIChlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJ0Nhbm5vdCBjb25uZWN0IHRvIHNlcnZlcicpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn4yQIEJhY2tlbmQgc2VydmVyIG5vdCBhdmFpbGFibGUsIHVzaW5nIGxvY2FsIHN0b3JhZ2Ugb25seScpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gT25seSBsb2cgZGV0YWlsZWQgZXJyb3JzIGZvciB1bmV4cGVjdGVkIGlzc3Vlc1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBCYWNrZW5kIHNlc3Npb24gbG9hZGluZyBmYWlsZWQsIGZhbGxpbmcgYmFjayB0byBsb2NhbCBzdG9yYWdlOicsIGVycm9yTWVzc2FnZSk7XG4gICAgICB9XG5cbiAgICAgIC8vIEZhbGxiYWNrIHRvIGxvY2FsU3RvcmFnZVxuICAgICAgdGhpcy5sb2FkU2Vzc2lvbnNGcm9tU3RvcmFnZSgpO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc2F2ZVNlc3Npb25zVG9TdG9yYWdlKCk6IHZvaWQge1xuICAgIHRyeSB7XG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybjtcblxuICAgICAgLy8gU2F2ZSBzZXNzaW9ucyB0byBzaGFyZWQgc3RvcmFnZSAoYWxsIHdpbmRvd3Mgc2VlIHNhbWUgc2Vzc2lvbnMpXG4gICAgICBjb25zdCBzZXNzaW9uc09iamVjdCA9IE9iamVjdC5mcm9tRW50cmllcyh0aGlzLnNlc3Npb25zKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFNFU1NJT05TX1NUT1JBR0VfS0VZLCBKU09OLnN0cmluZ2lmeShzZXNzaW9uc09iamVjdCkpO1xuXG4gICAgICAvLyBTYXZlIGN1cnJlbnQgc2Vzc2lvbiB0byB3aW5kb3ctc3BlY2lmaWMgc3RvcmFnZVxuICAgICAgY29uc3QgY3VycmVudFNlc3Npb25LZXkgPSB0aGlzLmdldFdpbmRvd1NwZWNpZmljS2V5KENVUlJFTlRfU0VTU0lPTl9LRVkpO1xuICAgICAgaWYgKHRoaXMuY3VycmVudFNlc3Npb25JZCkge1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShjdXJyZW50U2Vzc2lvbktleSwgdGhpcy5jdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgIH1cblxuICAgICAgLy8gVHJpZ2dlciBzdG9yYWdlIGV2ZW50IHRvIG5vdGlmeSBvdGhlciB3aW5kb3dzL3RhYnMgKGluY2x1ZGluZyBhZG1pbiBwYW5lbClcbiAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBTdG9yYWdlRXZlbnQoJ3N0b3JhZ2UnLCB7XG4gICAgICAgIGtleTogU0VTU0lPTlNfU1RPUkFHRV9LRVksXG4gICAgICAgIG5ld1ZhbHVlOiBKU09OLnN0cmluZ2lmeShzZXNzaW9uc09iamVjdCksXG4gICAgICAgIHN0b3JhZ2VBcmVhOiBsb2NhbFN0b3JhZ2VcbiAgICAgIH0pKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNhdmUgc2Vzc2lvbnMgdG8gc3RvcmFnZTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgcHVibGljIGFzeW5jIGNyZWF0ZU5ld1Nlc3Npb25XaXRoQXV0b05hbWUoY29uZmlnOiBUcmFkaW5nQ29uZmlnLCBjdXN0b21OYW1lPzogc3RyaW5nLCBjdXJyZW50QmFsYW5jZXM/OiB7IGNyeXB0bzE6IG51bWJlcjsgY3J5cHRvMjogbnVtYmVyOyBzdGFibGVjb2luOiBudW1iZXIgfSk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgY29uc3Qgc2Vzc2lvbk5hbWUgPSBjdXN0b21OYW1lIHx8IHRoaXMuZ2VuZXJhdGVTZXNzaW9uTmFtZShjb25maWcpO1xuICAgIHJldHVybiB0aGlzLmNyZWF0ZU5ld1Nlc3Npb24oc2Vzc2lvbk5hbWUsIGNvbmZpZywgY3VycmVudEJhbGFuY2VzKTtcbiAgfVxuXG4gIHB1YmxpYyBjcmVhdGVOZXdTZXNzaW9uU3luYyhuYW1lOiBzdHJpbmcsIGNvbmZpZzogVHJhZGluZ0NvbmZpZywgY3VycmVudEJhbGFuY2VzPzogeyBjcnlwdG8xOiBudW1iZXI7IGNyeXB0bzI6IG51bWJlcjsgc3RhYmxlY29pbjogbnVtYmVyIH0pOiBzdHJpbmcge1xuICAgIC8vIFN5bmNocm9ub3VzIHZlcnNpb24gZm9yIHVzZSBpbiBiZWZvcmV1bmxvYWQgaGFuZGxlcnNcbiAgICBjb25zdCBiYWxhbmNlcyA9IGN1cnJlbnRCYWxhbmNlcyB8fCB7IGNyeXB0bzE6IDEwLCBjcnlwdG8yOiAxMDAwMDAsIHN0YWJsZWNvaW46IDAgfTtcbiAgICBjb25zdCBzZXNzaW9uSWQgPSB1dWlkdjQoKTtcblxuICAgIGNvbnN0IG5ld1Nlc3Npb246IFRyYWRpbmdTZXNzaW9uID0ge1xuICAgICAgaWQ6IHNlc3Npb25JZCxcbiAgICAgIG5hbWUsXG4gICAgICBjb25maWc6IHsgLi4uY29uZmlnIH0sXG4gICAgICBjcmVhdGVkQXQ6IERhdGUubm93KCksXG4gICAgICBsYXN0TW9kaWZpZWQ6IERhdGUubm93KCksXG4gICAgICBpc0FjdGl2ZTogZmFsc2UsXG4gICAgICBydW50aW1lOiAwLFxuICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBbXSxcbiAgICAgIG9yZGVySGlzdG9yeTogW10sXG4gICAgICBjdXJyZW50TWFya2V0UHJpY2U6IDEwMDAwMCxcbiAgICAgIGNyeXB0bzFCYWxhbmNlOiBiYWxhbmNlcy5jcnlwdG8xLFxuICAgICAgY3J5cHRvMkJhbGFuY2U6IGJhbGFuY2VzLmNyeXB0bzIsXG4gICAgICBzdGFibGVjb2luQmFsYW5jZTogYmFsYW5jZXMuc3RhYmxlY29pbiB8fCAwXG4gICAgfTtcblxuICAgIHRoaXMuc2Vzc2lvbnMuc2V0KHNlc3Npb25JZCwgbmV3U2Vzc2lvbik7XG4gICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcbiAgICByZXR1cm4gc2Vzc2lvbklkO1xuICB9XG5cbiAgcHVibGljIGFzeW5jIGNyZWF0ZU5ld1Nlc3Npb24obmFtZTogc3RyaW5nLCBjb25maWc6IFRyYWRpbmdDb25maWcsIGN1cnJlbnRCYWxhbmNlcz86IHsgY3J5cHRvMTogbnVtYmVyOyBjcnlwdG8yOiBudW1iZXI7IHN0YWJsZWNvaW46IG51bWJlciB9KTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICAvLyBVc2UgcHJvdmlkZWQgYmFsYW5jZXMgb3IgZGVmYXVsdCB2YWx1ZXNcbiAgICBjb25zdCBiYWxhbmNlcyA9IGN1cnJlbnRCYWxhbmNlcyB8fCB7IGNyeXB0bzE6IDEwLCBjcnlwdG8yOiAxMDAwMDAsIHN0YWJsZWNvaW46IDAgfTtcblxuICAgIGlmICh0aGlzLnVzZUJhY2tlbmQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHNlc3Npb25EYXRhID0ge1xuICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgY29uZmlnOiBjb25maWcsIC8vIEJhY2tlbmQgZXhwZWN0cyAnY29uZmlnJywgbm90ICdjb25maWdfc25hcHNob3QnXG4gICAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiBbXSwgLy8gQmFja2VuZCBleHBlY3RzIGNhbWVsQ2FzZVxuICAgICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZTogMTAwMDAwLFxuICAgICAgICAgIGNyeXB0bzFCYWxhbmNlOiBiYWxhbmNlcy5jcnlwdG8xLFxuICAgICAgICAgIGNyeXB0bzJCYWxhbmNlOiBiYWxhbmNlcy5jcnlwdG8yLFxuICAgICAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBiYWxhbmNlcy5zdGFibGVjb2luXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzZXNzaW9uQXBpLmNyZWF0ZVNlc3Npb24oc2Vzc2lvbkRhdGEpO1xuXG4gICAgICAgIC8vIEFkZCB0aGUgc2Vzc2lvbiB0byBvdXIgbG9jYWwgY2FjaGVcbiAgICAgICAgY29uc3QgbmV3U2Vzc2lvbjogVHJhZGluZ1Nlc3Npb24gPSB7XG4gICAgICAgICAgaWQ6IHJlc3BvbnNlLnNlc3Npb24uc2Vzc2lvbl91dWlkLFxuICAgICAgICAgIG5hbWU6IHJlc3BvbnNlLnNlc3Npb24ubmFtZSxcbiAgICAgICAgICBjb25maWcsXG4gICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShyZXNwb25zZS5zZXNzaW9uLmNyZWF0ZWRfYXQpLmdldFRpbWUoKSxcbiAgICAgICAgICBsYXN0TW9kaWZpZWQ6IG5ldyBEYXRlKHJlc3BvbnNlLnNlc3Npb24ubGFzdF9tb2RpZmllZCkuZ2V0VGltZSgpLFxuICAgICAgICAgIGlzQWN0aXZlOiByZXNwb25zZS5zZXNzaW9uLmlzX2FjdGl2ZSxcbiAgICAgICAgICBydW50aW1lOiByZXNwb25zZS5zZXNzaW9uLnJ1bnRpbWUgfHwgMCxcbiAgICAgICAgICB0YXJnZXRQcmljZVJvd3M6IFtdLFxuICAgICAgICAgIG9yZGVySGlzdG9yeTogW10sXG4gICAgICAgICAgY3VycmVudE1hcmtldFByaWNlOiAxMDAwMDAsXG4gICAgICAgICAgY3J5cHRvMUJhbGFuY2U6IGJhbGFuY2VzLmNyeXB0bzEsXG4gICAgICAgICAgY3J5cHRvMkJhbGFuY2U6IGJhbGFuY2VzLmNyeXB0bzIsXG4gICAgICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IGJhbGFuY2VzLnN0YWJsZWNvaW5cbiAgICAgICAgfTtcblxuICAgICAgICB0aGlzLnNlc3Npb25zLnNldChyZXNwb25zZS5zZXNzaW9uLnNlc3Npb25fdXVpZCwgbmV3U2Vzc2lvbik7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgU2Vzc2lvbiBjcmVhdGVkIG9uIGJhY2tlbmQ6JywgcmVzcG9uc2Uuc2Vzc2lvbi5zZXNzaW9uX3V1aWQpO1xuICAgICAgICByZXR1cm4gcmVzcG9uc2Uuc2Vzc2lvbi5zZXNzaW9uX3V1aWQ7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGNyZWF0ZSBzZXNzaW9uIG9uIGJhY2tlbmQsIGZhbGxpbmcgYmFjayB0byBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpO1xuICAgICAgICB0aGlzLnVzZUJhY2tlbmQgPSBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byBsb2NhbFN0b3JhZ2VcbiAgICBjb25zdCBzZXNzaW9uSWQgPSB1dWlkdjQoKTtcbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuXG4gICAgY29uc3QgbmV3U2Vzc2lvbjogVHJhZGluZ1Nlc3Npb24gPSB7XG4gICAgICBpZDogc2Vzc2lvbklkLFxuICAgICAgbmFtZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIHRhcmdldFByaWNlUm93czogW10sXG4gICAgICBvcmRlckhpc3Rvcnk6IFtdLFxuICAgICAgY3VycmVudE1hcmtldFByaWNlOiAwLFxuICAgICAgY3J5cHRvMUJhbGFuY2U6IGJhbGFuY2VzLmNyeXB0bzEsXG4gICAgICBjcnlwdG8yQmFsYW5jZTogYmFsYW5jZXMuY3J5cHRvMixcbiAgICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBiYWxhbmNlcy5zdGFibGVjb2luLFxuICAgICAgY3JlYXRlZEF0OiBub3csXG4gICAgICBsYXN0TW9kaWZpZWQ6IG5vdyxcbiAgICAgIGlzQWN0aXZlOiBmYWxzZSxcbiAgICAgIHJ1bnRpbWU6IDBcbiAgICB9O1xuXG4gICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbklkLCBuZXdTZXNzaW9uKTtcbiAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuICAgIHJldHVybiBzZXNzaW9uSWQ7XG4gIH1cblxuICBwdWJsaWMgYXN5bmMgc2F2ZVNlc3Npb24oXG4gICAgc2Vzc2lvbklkOiBzdHJpbmcsXG4gICAgY29uZmlnOiBUcmFkaW5nQ29uZmlnLFxuICAgIHRhcmdldFByaWNlUm93czogVGFyZ2V0UHJpY2VSb3dbXSxcbiAgICBvcmRlckhpc3Rvcnk6IE9yZGVySGlzdG9yeUVudHJ5W10sXG4gICAgY3VycmVudE1hcmtldFByaWNlOiBudW1iZXIsXG4gICAgY3J5cHRvMUJhbGFuY2U6IG51bWJlcixcbiAgICBjcnlwdG8yQmFsYW5jZTogbnVtYmVyLFxuICAgIHN0YWJsZWNvaW5CYWxhbmNlOiBudW1iZXIsXG4gICAgaXNBY3RpdmU6IGJvb2xlYW4gPSBmYWxzZSxcbiAgICBvdmVycmlkZVJ1bnRpbWU/OiBudW1iZXIgLy8gT3B0aW9uYWwgcGFyYW1ldGVyIHRvIHNldCBzcGVjaWZpYyBydW50aW1lXG4gICk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZXNzaW9uID0gdGhpcy5zZXNzaW9ucy5nZXQoc2Vzc2lvbklkKTtcbiAgICAgIGlmICghc2Vzc2lvbikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdTZXNzaW9uIG5vdCBmb3VuZDonLCBzZXNzaW9uSWQpO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCBwZXJzaXN0ZW5jZSB0aW1lc3RhbXAgZm9yIHJlY292ZXJ5XG4gICAgICBjb25zdCBwZXJzaXN0ZW5jZUluZm8gPSB7XG4gICAgICAgIGxhc3RTYXZlZDogRGF0ZS5ub3coKSxcbiAgICAgICAgd2luZG93SWQ6IHRoaXMud2luZG93SWQsXG4gICAgICAgIGlzQWN0aXZlLFxuICAgICAgICBzZXNzaW9uSWRcbiAgICAgIH07XG5cbiAgICAgIC8vIFVwZGF0ZSBydW50aW1lIC0gdXNlIG92ZXJyaWRlIGlmIHByb3ZpZGVkLCBvdGhlcndpc2UgY2FsY3VsYXRlIG5vcm1hbGx5XG4gICAgICBsZXQgY3VycmVudFJ1bnRpbWU6IG51bWJlcjtcblxuICAgICAgaWYgKG92ZXJyaWRlUnVudGltZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIC8vIFVzZSB0aGUgcHJvdmlkZWQgcnVudGltZSAoZm9yIHNhdmVkIHNlc3Npb25zKVxuICAgICAgICBjdXJyZW50UnVudGltZSA9IG92ZXJyaWRlUnVudGltZTtcbiAgICAgICAgY29uc29sZS5sb2coYPCfk4ogVXNpbmcgb3ZlcnJpZGUgcnVudGltZTogJHtjdXJyZW50UnVudGltZX1tcyBmb3Igc2Vzc2lvbiAke3Nlc3Npb25JZH1gKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIENhbGN1bGF0ZSBydW50aW1lIG5vcm1hbGx5IGZvciBhY3RpdmUgc2Vzc2lvbnNcbiAgICAgICAgY3VycmVudFJ1bnRpbWUgPSBzZXNzaW9uLnJ1bnRpbWUgfHwgMDtcbiAgICAgICAgY29uc3Qgc3RhcnRUaW1lID0gdGhpcy5zZXNzaW9uU3RhcnRUaW1lcy5nZXQoc2Vzc2lvbklkKTtcbiAgICAgICAgaWYgKHN0YXJ0VGltZSAmJiBpc0FjdGl2ZSkge1xuICAgICAgICAgIC8vIFNlc3Npb24gaXMgcnVubmluZywgdXBkYXRlIHJ1bnRpbWVcbiAgICAgICAgICBjdXJyZW50UnVudGltZSA9IChzZXNzaW9uLnJ1bnRpbWUgfHwgMCkgKyAoRGF0ZS5ub3coKSAtIHN0YXJ0VGltZSk7XG4gICAgICAgICAgLy8gUmVzZXQgc3RhcnQgdGltZSBmb3IgbmV4dCBpbnRlcnZhbFxuICAgICAgICAgIHRoaXMuc2Vzc2lvblN0YXJ0VGltZXMuc2V0KHNlc3Npb25JZCwgRGF0ZS5ub3coKSk7XG4gICAgICAgIH0gZWxzZSBpZiAoIWlzQWN0aXZlICYmIHN0YXJ0VGltZSkge1xuICAgICAgICAgIC8vIFNlc3Npb24gc3RvcHBlZCwgZmluYWxpemUgcnVudGltZVxuICAgICAgICAgIGN1cnJlbnRSdW50aW1lID0gKHNlc3Npb24ucnVudGltZSB8fCAwKSArIChEYXRlLm5vdygpIC0gc3RhcnRUaW1lKTtcbiAgICAgICAgICB0aGlzLnNlc3Npb25TdGFydFRpbWVzLmRlbGV0ZShzZXNzaW9uSWQpO1xuICAgICAgICB9IGVsc2UgaWYgKGlzQWN0aXZlICYmICFzdGFydFRpbWUpIHtcbiAgICAgICAgICAvLyBTZXNzaW9uIGp1c3Qgc3RhcnRlZCwgcmVjb3JkIHN0YXJ0IHRpbWVcbiAgICAgICAgICB0aGlzLnNlc3Npb25TdGFydFRpbWVzLnNldChzZXNzaW9uSWQsIERhdGUubm93KCkpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHVwZGF0ZWRTZXNzaW9uOiBUcmFkaW5nU2Vzc2lvbiA9IHtcbiAgICAgICAgLi4uc2Vzc2lvbixcbiAgICAgICAgY29uZmlnLFxuICAgICAgICB0YXJnZXRQcmljZVJvd3M6IFsuLi50YXJnZXRQcmljZVJvd3NdLFxuICAgICAgICBvcmRlckhpc3Rvcnk6IFsuLi5vcmRlckhpc3RvcnldLFxuICAgICAgICBjdXJyZW50TWFya2V0UHJpY2UsXG4gICAgICAgIGNyeXB0bzFCYWxhbmNlLFxuICAgICAgICBjcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgc3RhYmxlY29pbkJhbGFuY2UsXG4gICAgICAgIGlzQWN0aXZlLFxuICAgICAgICBsYXN0TW9kaWZpZWQ6IERhdGUubm93KCksXG4gICAgICAgIHJ1bnRpbWU6IGN1cnJlbnRSdW50aW1lXG4gICAgICB9O1xuXG4gICAgICAvLyBTdG9yZSBwZXJzaXN0ZW5jZSBpbmZvIGZvciByZWNvdmVyeVxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oYHBsdXRvX3Nlc3Npb25fcGVyc2lzdGVuY2VfJHtzZXNzaW9uSWR9YCwgSlNPTi5zdHJpbmdpZnkocGVyc2lzdGVuY2VJbmZvKSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGDwn5K+IFNhdmluZyBzZXNzaW9uIHdpdGggY29tcGxldGUgZGF0YTpgLCB7XG4gICAgICAgIHNlc3Npb25JZCxcbiAgICAgICAgdGFyZ2V0UHJpY2VSb3dzOiB0YXJnZXRQcmljZVJvd3MubGVuZ3RoLFxuICAgICAgICBvcmRlckhpc3Rvcnk6IG9yZGVySGlzdG9yeS5sZW5ndGgsXG4gICAgICAgIGJhbGFuY2VzOiB7IGNyeXB0bzFCYWxhbmNlLCBjcnlwdG8yQmFsYW5jZSwgc3RhYmxlY29pbkJhbGFuY2UgfSxcbiAgICAgICAgaXNBY3RpdmUsXG4gICAgICAgIHJ1bnRpbWU6IGN1cnJlbnRSdW50aW1lXG4gICAgICB9KTtcblxuICAgICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbklkLCB1cGRhdGVkU2Vzc2lvbik7XG5cbiAgICAgIC8vIElmIHNlc3Npb24gaXMgYWN0aXZlLCBlbnN1cmUgaXQncyB0cmFja2VkIGluIGFjdGl2ZSBzZXNzaW9uc1xuICAgICAgaWYgKGlzQWN0aXZlKSB7XG4gICAgICAgIHRoaXMuYWRkQWN0aXZlU2Vzc2lvbihzZXNzaW9uSWQpO1xuICAgICAgICBjb25zb2xlLmxvZyhg4pyFIFNlc3Npb24gbWFya2VkIGFzIGFjdGl2ZSBhbmQgdHJhY2tlZDogJHtzZXNzaW9uSWR9YCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLnJlbW92ZUFjdGl2ZVNlc3Npb24oc2Vzc2lvbklkKTtcbiAgICAgICAgY29uc29sZS5sb2coYOKPue+4jyBTZXNzaW9uIG1hcmtlZCBhcyBpbmFjdGl2ZSBhbmQgcmVtb3ZlZCBmcm9tIHRyYWNraW5nOiAke3Nlc3Npb25JZH1gKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2F2ZSB0byBiYWNrZW5kIG9ubHkgaWYgZXhwbGljaXRseSBhdXRoZW50aWNhdGVkXG4gICAgICBpZiAodGhpcy51c2VCYWNrZW5kICYmIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3BsdXRvQXV0aFRva2VuJyk7XG4gICAgICAgIGlmICh0b2tlbiAmJiB0b2tlbi5sZW5ndGggPiAxMCkgeyAvLyBCYXNpYyB0b2tlbiB2YWxpZGF0aW9uXG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIERvdWJsZS1jaGVjayBhdXRoZW50aWNhdGlvbiBiZWZvcmUgbWFraW5nIEFQSSBjYWxsXG4gICAgICAgICAgICBjb25zdCB7IHNlc3Npb25BcGkgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvYXBpJyk7XG4gICAgICAgICAgICBjb25zdCBzZXNzaW9uRGF0YSA9IHtcbiAgICAgICAgICAgICAgbmFtZTogdXBkYXRlZFNlc3Npb24ubmFtZSxcbiAgICAgICAgICAgICAgY29uZmlnOiBjb25maWcsIC8vIEJhY2tlbmQgZXhwZWN0cyAnY29uZmlnJywgbm90ICdjb25maWdfc25hcHNob3QnXG4gICAgICAgICAgICAgIHRhcmdldFByaWNlUm93czogdGFyZ2V0UHJpY2VSb3dzLCAvLyBCYWNrZW5kIGV4cGVjdHMgY2FtZWxDYXNlXG4gICAgICAgICAgICAgIGN1cnJlbnRNYXJrZXRQcmljZTogY3VycmVudE1hcmtldFByaWNlLFxuICAgICAgICAgICAgICBjcnlwdG8xQmFsYW5jZTogY3J5cHRvMUJhbGFuY2UsXG4gICAgICAgICAgICAgIGNyeXB0bzJCYWxhbmNlOiBjcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IHN0YWJsZWNvaW5CYWxhbmNlLFxuICAgICAgICAgICAgICBpc0FjdGl2ZTogaXNBY3RpdmUsXG4gICAgICAgICAgICAgIGFkZGl0aW9uYWxSdW50aW1lOiBjdXJyZW50UnVudGltZVxuICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgYXdhaXQgc2Vzc2lvbkFwaS51cGRhdGVTZXNzaW9uKHNlc3Npb25JZCwgc2Vzc2lvbkRhdGEpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBTZXNzaW9uIHNhdmVkIHRvIGJhY2tlbmQ6Jywgc2Vzc2lvbklkKTtcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBTdHJpbmcoZXJyb3IpO1xuICAgICAgICAgICAgY29uc29sZS53YXJuKCfinYwgQmFja2VuZCBzZXNzaW9uIHNhdmUgZmFpbGVkOicsIGVycm9yTWVzc2FnZSk7XG5cbiAgICAgICAgICAgIC8vIEhhbmRsZSBcIlNlc3Npb24gbm90IGZvdW5kXCIgZXJyb3IgYnkgY3JlYXRpbmcgYSBuZXcgc2Vzc2lvblxuICAgICAgICAgICAgaWYgKGVycm9yTWVzc2FnZS5pbmNsdWRlcygnU2Vzc2lvbiBub3QgZm91bmQnKSkge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBTZXNzaW9uIG5vdCBmb3VuZCBpbiBiYWNrZW5kLCBjcmVhdGluZyBuZXcgc2Vzc2lvbi4uLicpO1xuICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIC8vIENyZWF0ZSBhIG5ldyBzZXNzaW9uIGluIHRoZSBiYWNrZW5kIHdpdGggdGhlIHNhbWUgZGF0YVxuICAgICAgICAgICAgICAgIGNvbnN0IG5ld1Nlc3Npb25JZCA9IGF3YWl0IHRoaXMuY3JlYXRlTmV3U2Vzc2lvbih1cGRhdGVkU2Vzc2lvbi5uYW1lLCBjb25maWcsIHtcbiAgICAgICAgICAgICAgICAgIGNyeXB0bzE6IGNyeXB0bzFCYWxhbmNlLFxuICAgICAgICAgICAgICAgICAgY3J5cHRvMjogY3J5cHRvMkJhbGFuY2UsXG4gICAgICAgICAgICAgICAgICBzdGFibGVjb2luOiBzdGFibGVjb2luQmFsYW5jZVxuICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgLy8gVXBkYXRlIHRoZSBsb2NhbCBzZXNzaW9uIElEIGFuZCBzZXNzaW9uIGRhdGFcbiAgICAgICAgICAgICAgICB0aGlzLnNlc3Npb25zLnNldChuZXdTZXNzaW9uSWQsIHtcbiAgICAgICAgICAgICAgICAgIC4uLnVwZGF0ZWRTZXNzaW9uLFxuICAgICAgICAgICAgICAgICAgaWQ6IG5ld1Nlc3Npb25JZFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHRoaXMuc2Vzc2lvbnMuZGVsZXRlKHNlc3Npb25JZCk7IC8vIFJlbW92ZSBvbGQgc2Vzc2lvblxuICAgICAgICAgICAgICAgIHRoaXMuc2V0Q3VycmVudFNlc3Npb24obmV3U2Vzc2lvbklkKTsgLy8gVXBkYXRlIGN1cnJlbnQgc2Vzc2lvbiBJRFxuXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBDcmVhdGVkIG5ldyBzZXNzaW9uIHRvIHJlcGxhY2UgbWlzc2luZyBvbmU6JywgbmV3U2Vzc2lvbklkKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgfSBjYXRjaCAoY3JlYXRlRXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGNyZWF0ZSByZXBsYWNlbWVudCBzZXNzaW9uOicsIGNyZWF0ZUVycm9yKTtcbiAgICAgICAgICAgICAgICAvLyBGYWxsIHRocm91Z2ggdG8gZGlzYWJsZSBiYWNrZW5kIG1vZGVcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBEaXNhYmxlIGJhY2tlbmQgbW9kZSBvbiBhbnkgYXV0aGVudGljYXRpb24tcmVsYXRlZCBlcnJvclxuICAgICAgICAgICAgaWYgKGVycm9yTWVzc2FnZS5pbmNsdWRlcygnNDAxJykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCc0MjInKSB8fCBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJ0F1dGhlbnRpY2F0aW9uJykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdyZXF1aXJlZCcpKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SQIERpc2FibGluZyBiYWNrZW5kIG1vZGUgZHVlIHRvIGF1dGhlbnRpY2F0aW9uIGlzc3VlJyk7XG4gICAgICAgICAgICAgIHRoaXMudXNlQmFja2VuZCA9IGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gQ29udGludWUgd2l0aCBsb2NhbFN0b3JhZ2Ugc2F2ZSBhcyBmYWxsYmFja1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pqg77iPIEludmFsaWQgb3IgbWlzc2luZyBhdXRoIHRva2VuLCBza2lwcGluZyBiYWNrZW5kIHNlc3Npb24gc2F2ZScpO1xuICAgICAgICAgIHRoaXMudXNlQmFja2VuZCA9IGZhbHNlOyAvLyBEaXNhYmxlIGJhY2tlbmQgbW9kZSBpZiBubyB2YWxpZCB0b2tlblxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHRoaXMuc2F2ZVNlc3Npb25zVG9TdG9yYWdlKCk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNhdmUgc2Vzc2lvbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgcHVibGljIGxvYWRTZXNzaW9uKHNlc3Npb25JZDogc3RyaW5nKTogVHJhZGluZ1Nlc3Npb24gfCBudWxsIHtcbiAgICByZXR1cm4gdGhpcy5zZXNzaW9ucy5nZXQoc2Vzc2lvbklkKSB8fCBudWxsO1xuICB9XG5cbiAgcHVibGljIGFzeW5jIGRlbGV0ZVNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICAvLyBEZWxldGUgZnJvbSBiYWNrZW5kIGZpcnN0IGlmIGF2YWlsYWJsZSBhbmQgdXNlciBpcyBhdXRoZW50aWNhdGVkXG4gICAgaWYgKHRoaXMudXNlQmFja2VuZCAmJiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncGx1dG9BdXRoVG9rZW4nKTtcbiAgICAgIGlmICh0b2tlbikge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGF3YWl0IHNlc3Npb25BcGkuZGVsZXRlU2Vzc2lvbihzZXNzaW9uSWQpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgU2Vzc2lvbiBkZWxldGVkIGZyb20gYmFja2VuZDonLCBzZXNzaW9uSWQpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogU3RyaW5nKGVycm9yKTtcbiAgICAgICAgICBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCc0MDEnKSB8fCBlcnJvck1lc3NhZ2UuaW5jbHVkZXMoJzQyMicpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygnQXV0aGVudGljYXRpb24nKSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflJAgQXV0aGVudGljYXRpb24gaXNzdWUgZHVyaW5nIHNlc3Npb24gZGVsZXRpb24sIHByb2NlZWRpbmcgd2l0aCBsb2NhbCBkZWxldGlvbicpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRmFpbGVkIHRvIGRlbGV0ZSBzZXNzaW9uIGZyb20gYmFja2VuZDonLCBlcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICAgIC8vIENvbnRpbnVlIHdpdGggbG9jYWwgZGVsZXRpb24gYXMgZmFsbGJhY2tcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyBObyBhdXRoIHRva2VuLCBza2lwcGluZyBiYWNrZW5kIHNlc3Npb24gZGVsZXRpb24nKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBkZWxldGVkID0gdGhpcy5zZXNzaW9ucy5kZWxldGUoc2Vzc2lvbklkKTtcbiAgICBpZiAoZGVsZXRlZCkge1xuICAgICAgaWYgKHRoaXMuY3VycmVudFNlc3Npb25JZCA9PT0gc2Vzc2lvbklkKSB7XG4gICAgICAgIHRoaXMuY3VycmVudFNlc3Npb25JZCA9IG51bGw7XG4gICAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uS2V5ID0gdGhpcy5nZXRXaW5kb3dTcGVjaWZpY0tleShDVVJSRU5UX1NFU1NJT05fS0VZKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oY3VycmVudFNlc3Npb25LZXkpO1xuICAgICAgfVxuICAgICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcbiAgICB9XG4gICAgcmV0dXJuIGRlbGV0ZWQ7XG4gIH1cblxuICBwdWJsaWMgZ2V0QWxsU2Vzc2lvbnMoKTogU2Vzc2lvbk1ldGFkYXRhW10ge1xuICAgIHJldHVybiBBcnJheS5mcm9tKHRoaXMuc2Vzc2lvbnMudmFsdWVzKCkpLm1hcChzZXNzaW9uID0+ICh7XG4gICAgICBpZDogc2Vzc2lvbi5pZCxcbiAgICAgIG5hbWU6IHNlc3Npb24ubmFtZSxcbiAgICAgIHBhaXI6IGAke3Nlc3Npb24uY29uZmlnLmNyeXB0bzF9LyR7c2Vzc2lvbi5jb25maWcuY3J5cHRvMn1gLFxuICAgICAgY3JlYXRlZEF0OiBzZXNzaW9uLmNyZWF0ZWRBdCxcbiAgICAgIGxhc3RNb2RpZmllZDogc2Vzc2lvbi5sYXN0TW9kaWZpZWQsXG4gICAgICBpc0FjdGl2ZTogc2Vzc2lvbi5pc0FjdGl2ZSxcbiAgICAgIHJ1bnRpbWU6IHRoaXMuZ2V0Q3VycmVudFJ1bnRpbWUoc2Vzc2lvbi5pZCksIC8vIFVzZSBjdXJyZW50IHJ1bnRpbWUgaW5jbHVkaW5nIGFjdGl2ZSB0aW1lXG4gICAgICB0b3RhbFRyYWRlczogc2Vzc2lvbi5vcmRlckhpc3RvcnkubGVuZ3RoLFxuICAgICAgdG90YWxQcm9maXRMb3NzOiBzZXNzaW9uLm9yZGVySGlzdG9yeVxuICAgICAgICAuZmlsdGVyKHRyYWRlID0+IHRyYWRlLm9yZGVyVHlwZSA9PT0gJ1NFTEwnICYmIHRyYWRlLnJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzIgIT09IHVuZGVmaW5lZClcbiAgICAgICAgLnJlZHVjZSgoc3VtLCB0cmFkZSkgPT4gc3VtICsgKHRyYWRlLnJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzIgfHwgMCksIDApXG4gICAgfSkpO1xuICB9XG5cbiAgcHVibGljIGdldEN1cnJlbnRBY3RpdmVTZXNzaW9ucygpOiBTZXNzaW9uTWV0YWRhdGFbXSB7XG4gICAgLy8gR2V0IGFsbCBzZXNzaW9ucyB0aGF0IGFyZSBjdXJyZW50bHkgYWN0aXZlIGFjcm9zcyBhbGwgd2luZG93c1xuICAgIGNvbnN0IGFjdGl2ZVNlc3Npb25JZHMgPSBuZXcgU2V0KFxuICAgICAgQXJyYXkuZnJvbSh0aGlzLmFjdGl2ZVNlc3Npb25zQWNyb3NzV2luZG93cy52YWx1ZXMoKSlcbiAgICAgICAgLm1hcChlbnRyeSA9PiBlbnRyeS5zZXNzaW9uSWQpXG4gICAgICAgIC5maWx0ZXIoc2Vzc2lvbklkID0+IHNlc3Npb25JZCAmJiBzZXNzaW9uSWQgIT09ICd1bmRlZmluZWQnKSAvLyBGaWx0ZXIgb3V0IGludmFsaWQgc2Vzc2lvbiBJRHNcbiAgICApO1xuXG4gICAgLy8gQWxzbyBpbmNsdWRlIGN1cnJlbnQgc2Vzc2lvbiBpZiBpdCBleGlzdHMgKG1pZ2h0IG5vdCBiZSBpbiBhY3RpdmVTZXNzaW9uc0Fjcm9zc1dpbmRvd3MgeWV0KVxuICAgIGlmICh0aGlzLmN1cnJlbnRTZXNzaW9uSWQgJiYgdGhpcy5zZXNzaW9ucy5oYXModGhpcy5jdXJyZW50U2Vzc2lvbklkKSkge1xuICAgICAgYWN0aXZlU2Vzc2lvbklkcy5hZGQodGhpcy5jdXJyZW50U2Vzc2lvbklkKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygn8J+UjSBnZXRDdXJyZW50QWN0aXZlU2Vzc2lvbnMgZGVidWc6Jywge1xuICAgICAgYWN0aXZlU2Vzc2lvbnNBY3Jvc3NXaW5kb3dzOiBBcnJheS5mcm9tKHRoaXMuYWN0aXZlU2Vzc2lvbnNBY3Jvc3NXaW5kb3dzLmVudHJpZXMoKSksXG4gICAgICBhY3RpdmVTZXNzaW9uSWRzOiBBcnJheS5mcm9tKGFjdGl2ZVNlc3Npb25JZHMpLFxuICAgICAgYWxsU2Vzc2lvbnM6IEFycmF5LmZyb20odGhpcy5zZXNzaW9ucy5rZXlzKCkpLFxuICAgICAgY3VycmVudFNlc3Npb25JZDogdGhpcy5jdXJyZW50U2Vzc2lvbklkLFxuICAgICAgd2luZG93SWQ6IHRoaXMud2luZG93SWRcbiAgICB9KTtcblxuICAgIC8vIEdldCBzZXNzaW9ucyB0aGF0IGFyZSBlaXRoZXIgaW4gYWN0aXZlIHRyYWNraW5nIE9SIG1hcmtlZCBhcyBhY3RpdmUgaW4gdGhlaXIgZGF0YVxuICAgIGNvbnN0IGFsbEFjdGl2ZVNlc3Npb25zID0gQXJyYXkuZnJvbSh0aGlzLnNlc3Npb25zLnZhbHVlcygpKVxuICAgICAgLmZpbHRlcihzZXNzaW9uID0+IHtcbiAgICAgICAgY29uc3QgaXNJbkFjdGl2ZVRyYWNraW5nID0gc2Vzc2lvbi5pZCAmJiBhY3RpdmVTZXNzaW9uSWRzLmhhcyhzZXNzaW9uLmlkKTtcbiAgICAgICAgY29uc3QgaXNNYXJrZWRBY3RpdmUgPSBzZXNzaW9uLmlzQWN0aXZlO1xuICAgICAgICBjb25zdCBpc0N1cnJlbnRTZXNzaW9uID0gc2Vzc2lvbi5pZCA9PT0gdGhpcy5jdXJyZW50U2Vzc2lvbklkO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIFNlc3Npb24gJHtzZXNzaW9uLmlkfSAoJHtzZXNzaW9uLm5hbWV9KTpgLCB7XG4gICAgICAgICAgaXNJbkFjdGl2ZVRyYWNraW5nLFxuICAgICAgICAgIGlzTWFya2VkQWN0aXZlLFxuICAgICAgICAgIGlzQ3VycmVudFNlc3Npb24sXG4gICAgICAgICAgc2hvdWxkSW5jbHVkZTogaXNJbkFjdGl2ZVRyYWNraW5nIHx8IGlzTWFya2VkQWN0aXZlIHx8IGlzQ3VycmVudFNlc3Npb25cbiAgICAgICAgfSk7XG5cbiAgICAgICAgcmV0dXJuIGlzSW5BY3RpdmVUcmFja2luZyB8fCBpc01hcmtlZEFjdGl2ZSB8fCBpc0N1cnJlbnRTZXNzaW9uO1xuICAgICAgfSlcbiAgICAgIC5tYXAoc2Vzc2lvbiA9PiAoe1xuICAgICAgICBpZDogc2Vzc2lvbi5pZCxcbiAgICAgICAgbmFtZTogc2Vzc2lvbi5uYW1lLFxuICAgICAgICBwYWlyOiBgJHtzZXNzaW9uLmNvbmZpZy5jcnlwdG8xfS8ke3Nlc3Npb24uY29uZmlnLmNyeXB0bzJ9YCxcbiAgICAgICAgY3JlYXRlZEF0OiBzZXNzaW9uLmNyZWF0ZWRBdCxcbiAgICAgICAgbGFzdE1vZGlmaWVkOiBzZXNzaW9uLmxhc3RNb2RpZmllZCxcbiAgICAgICAgaXNBY3RpdmU6IHRydWUsIC8vIFRoZXNlIGFyZSBhbGwgY3VycmVudGx5IGFjdGl2ZVxuICAgICAgICBydW50aW1lOiB0aGlzLmdldEN1cnJlbnRSdW50aW1lKHNlc3Npb24uaWQpLFxuICAgICAgICB0b3RhbFRyYWRlczogc2Vzc2lvbi5vcmRlckhpc3RvcnkubGVuZ3RoLFxuICAgICAgICB0b3RhbFByb2ZpdExvc3M6IHNlc3Npb24ub3JkZXJIaXN0b3J5XG4gICAgICAgICAgLmZpbHRlcih0cmFkZSA9PiB0cmFkZS5vcmRlclR5cGUgPT09ICdTRUxMJyAmJiB0cmFkZS5yZWFsaXplZFByb2ZpdExvc3NDcnlwdG8yICE9PSB1bmRlZmluZWQpXG4gICAgICAgICAgLnJlZHVjZSgoc3VtLCB0cmFkZSkgPT4gc3VtICsgKHRyYWRlLnJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzIgfHwgMCksIDApXG4gICAgICB9KSlcbiAgICAgIC5maWx0ZXIoc2Vzc2lvbiA9PiBzZXNzaW9uLmlkKTsgLy8gRW5zdXJlIGFsbCByZXR1cm5lZCBzZXNzaW9ucyBoYXZlIHZhbGlkIElEc1xuXG4gICAgY29uc29sZS5sb2coJ/CflI0gUmV0dXJuaW5nIGFjdGl2ZSBzZXNzaW9uczonLCBhbGxBY3RpdmVTZXNzaW9ucy5sZW5ndGgsIGFsbEFjdGl2ZVNlc3Npb25zLm1hcChzID0+ICh7IGlkOiBzLmlkLCBuYW1lOiBzLm5hbWUgfSkpKTtcbiAgICByZXR1cm4gYWxsQWN0aXZlU2Vzc2lvbnM7XG4gIH1cblxuICBwdWJsaWMgc2V0Q3VycmVudFNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5zZXNzaW9ucy5oYXMoc2Vzc2lvbklkKSkge1xuICAgICAgdGhpcy5jdXJyZW50U2Vzc2lvbklkID0gc2Vzc2lvbklkO1xuXG4gICAgICAvLyBTdG9yZSBpbiBib3RoIGxvY2FsU3RvcmFnZSAod2luZG93LXNwZWNpZmljKSBhbmQgc2Vzc2lvblN0b3JhZ2UgKHBhZ2UgcmVmcmVzaCBwZXJzaXN0ZW5jZSlcbiAgICAgIGNvbnN0IGN1cnJlbnRTZXNzaW9uS2V5ID0gdGhpcy5nZXRXaW5kb3dTcGVjaWZpY0tleShDVVJSRU5UX1NFU1NJT05fS0VZKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGN1cnJlbnRTZXNzaW9uS2V5LCBzZXNzaW9uSWQpO1xuICAgICAgc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbShDVVJSRU5UX1NFU1NJT05fS0VZLCBzZXNzaW9uSWQpO1xuXG4gICAgICAvLyBNYXJrIHRoaXMgc2Vzc2lvbiBhcyBhY3RpdmUgd2hlbiBpdCBiZWNvbWVzIHRoZSBjdXJyZW50IHNlc3Npb25cbiAgICAgIC8vIEFsbG93IG11bHRpcGxlIHNlc3Npb25zIHRvIGJlIGFjdGl2ZSBzaW11bHRhbmVvdXNseSAoZm9yIGRpZmZlcmVudCB0YWJzKVxuICAgICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgICBpZiAoc2Vzc2lvbikge1xuICAgICAgICBzZXNzaW9uLmlzQWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgc2Vzc2lvbi5sYXN0TW9kaWZpZWQgPSBEYXRlLm5vdygpO1xuICAgICAgICB0aGlzLnNlc3Npb25zLnNldChzZXNzaW9uSWQsIHNlc3Npb24pO1xuICAgICAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuXG4gICAgICAgIC8vIFRyYWNrIHRoaXMgc2Vzc2lvbiBhcyBhY3RpdmUgYWNyb3NzIHdpbmRvd3NcbiAgICAgICAgdGhpcy5hZGRBY3RpdmVTZXNzaW9uKHNlc3Npb25JZCk7XG4gICAgICAgIGNvbnNvbGUubG9nKGDinIUgU2Vzc2lvbiAke3Nlc3Npb25JZH0gbWFya2VkIGFzIGFjdGl2ZSBmb3Igd2luZG93ICR7dGhpcy53aW5kb3dJZH1gKTtcblxuICAgICAgICAvLyBUcmlnZ2VyIHN0b3JhZ2UgZXZlbnQgdG8gbm90aWZ5IGFkbWluIHBhbmVsIGFib3V0IGN1cnJlbnQgc2Vzc2lvbiBjaGFuZ2VcbiAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IFN0b3JhZ2VFdmVudCgnc3RvcmFnZScsIHtcbiAgICAgICAgICBrZXk6ICdwbHV0b19jdXJyZW50X3Nlc3Npb24nLFxuICAgICAgICAgIG5ld1ZhbHVlOiBzZXNzaW9uSWQsXG4gICAgICAgICAgc3RvcmFnZUFyZWE6IGxvY2FsU3RvcmFnZVxuICAgICAgICB9KSk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcHVibGljIGdldEN1cnJlbnRTZXNzaW9uSWQoKTogc3RyaW5nIHwgbnVsbCB7XG4gICAgcmV0dXJuIHRoaXMuY3VycmVudFNlc3Npb25JZDtcbiAgfVxuXG4gIHB1YmxpYyBjbGVhckN1cnJlbnRTZXNzaW9uKCk6IHZvaWQge1xuICAgIC8vIFJlbW92ZSBzZXNzaW9uIGZyb20gYWN0aXZlIHRyYWNraW5nIGFuZCBtYXJrIGFzIGluYWN0aXZlIG9ubHkgaWYgbm8gb3RoZXIgd2luZG93cyBhcmUgdXNpbmcgaXRcbiAgICBpZiAodGhpcy5jdXJyZW50U2Vzc2lvbklkKSB7XG4gICAgICB0aGlzLnJlbW92ZUFjdGl2ZVNlc3Npb24odGhpcy5jdXJyZW50U2Vzc2lvbklkKTtcblxuICAgICAgLy8gQ2hlY2sgaWYgYW55IG90aGVyIHdpbmRvd3MgYXJlIHN0aWxsIHVzaW5nIHRoaXMgc2Vzc2lvblxuICAgICAgY29uc3QgaXNTdGlsbEFjdGl2ZUluT3RoZXJXaW5kb3dzID0gQXJyYXkuZnJvbSh0aGlzLmFjdGl2ZVNlc3Npb25zQWNyb3NzV2luZG93cy52YWx1ZXMoKSlcbiAgICAgICAgLnNvbWUoZW50cnkgPT4gZW50cnkuc2Vzc2lvbklkID09PSB0aGlzLmN1cnJlbnRTZXNzaW9uSWQpO1xuXG4gICAgICBpZiAoIWlzU3RpbGxBY3RpdmVJbk90aGVyV2luZG93cykge1xuICAgICAgICBjb25zdCBzZXNzaW9uID0gdGhpcy5zZXNzaW9ucy5nZXQodGhpcy5jdXJyZW50U2Vzc2lvbklkKTtcbiAgICAgICAgaWYgKHNlc3Npb24gJiYgc2Vzc2lvbi5pc0FjdGl2ZSkge1xuICAgICAgICAgIHNlc3Npb24uaXNBY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICBzZXNzaW9uLmxhc3RNb2RpZmllZCA9IERhdGUubm93KCk7XG4gICAgICAgICAgdGhpcy5zZXNzaW9ucy5zZXQodGhpcy5jdXJyZW50U2Vzc2lvbklkLCBzZXNzaW9uKTtcbiAgICAgICAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDij7nvuI8gU2Vzc2lvbiAke3RoaXMuY3VycmVudFNlc3Npb25JZH0gbWFya2VkIGFzIGluYWN0aXZlIChubyBvdGhlciB3aW5kb3dzIHVzaW5nIGl0KWApO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+UhCBTZXNzaW9uICR7dGhpcy5jdXJyZW50U2Vzc2lvbklkfSBzdGlsbCBhY3RpdmUgaW4gb3RoZXIgd2luZG93c2ApO1xuICAgICAgfVxuICAgIH1cblxuICAgIHRoaXMuY3VycmVudFNlc3Npb25JZCA9IG51bGw7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCBjdXJyZW50U2Vzc2lvbktleSA9IHRoaXMuZ2V0V2luZG93U3BlY2lmaWNLZXkoQ1VSUkVOVF9TRVNTSU9OX0tFWSk7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShjdXJyZW50U2Vzc2lvbktleSk7XG4gICAgICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKENVUlJFTlRfU0VTU0lPTl9LRVkpO1xuICAgIH1cbiAgICBjb25zb2xlLmxvZyhg8J+Xke+4jyBDbGVhcmVkIGN1cnJlbnQgc2Vzc2lvbiBmb3Igd2luZG93ICR7dGhpcy53aW5kb3dJZH1gKTtcbiAgfVxuXG4gIHB1YmxpYyBzdGFydFNlc3Npb25SdW50aW1lKHNlc3Npb25JZDogc3RyaW5nKTogdm9pZCB7XG4gICAgdGhpcy5zZXNzaW9uU3RhcnRUaW1lcy5zZXQoc2Vzc2lvbklkLCBEYXRlLm5vdygpKTtcbiAgfVxuXG4gIHB1YmxpYyBzdG9wU2Vzc2lvblJ1bnRpbWUoc2Vzc2lvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBjb25zdCBzdGFydFRpbWUgPSB0aGlzLnNlc3Npb25TdGFydFRpbWVzLmdldChzZXNzaW9uSWQpO1xuICAgIGlmIChzdGFydFRpbWUpIHtcbiAgICAgIGNvbnN0IHNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChzZXNzaW9uSWQpO1xuICAgICAgaWYgKHNlc3Npb24pIHtcbiAgICAgICAgY29uc3QgYWRkaXRpb25hbFJ1bnRpbWUgPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lO1xuICAgICAgICBzZXNzaW9uLnJ1bnRpbWUgPSAoc2Vzc2lvbi5ydW50aW1lIHx8IDApICsgYWRkaXRpb25hbFJ1bnRpbWU7XG4gICAgICAgIHNlc3Npb24ubGFzdE1vZGlmaWVkID0gRGF0ZS5ub3coKTtcbiAgICAgICAgLy8gS2VlcCBzZXNzaW9uIGFjdGl2ZSBldmVuIHdoZW4gcnVudGltZSBzdG9wcyAtIG9ubHkgZGVhY3RpdmF0ZSBvbiBtYW51YWwgc2F2ZSBvciBzZXNzaW9uIGNsZWFyXG4gICAgICAgIHRoaXMuc2Vzc2lvbnMuc2V0KHNlc3Npb25JZCwgc2Vzc2lvbik7XG4gICAgICAgIHRoaXMuc2F2ZVNlc3Npb25zVG9TdG9yYWdlKCk7XG4gICAgICB9XG4gICAgICB0aGlzLnNlc3Npb25TdGFydFRpbWVzLmRlbGV0ZShzZXNzaW9uSWQpO1xuICAgIH1cbiAgfVxuXG4gIHB1YmxpYyBkZWFjdGl2YXRlU2Vzc2lvbihzZXNzaW9uSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGNvbnN0IHNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChzZXNzaW9uSWQpO1xuICAgIGlmIChzZXNzaW9uICYmIHNlc3Npb24uaXNBY3RpdmUpIHtcbiAgICAgIHNlc3Npb24uaXNBY3RpdmUgPSBmYWxzZTtcbiAgICAgIHNlc3Npb24ubGFzdE1vZGlmaWVkID0gRGF0ZS5ub3coKTtcbiAgICAgIHRoaXMuc2Vzc2lvbnMuc2V0KHNlc3Npb25JZCwgc2Vzc2lvbik7XG4gICAgICB0aGlzLnNhdmVTZXNzaW9uc1RvU3RvcmFnZSgpO1xuICAgICAgY29uc29sZS5sb2coYOKPue+4jyBTZXNzaW9uICR7c2Vzc2lvbklkfSBkZWFjdGl2YXRlZGApO1xuICAgIH1cbiAgfVxuXG4gIHB1YmxpYyBnZXRDdXJyZW50UnVudGltZShzZXNzaW9uSWQ6IHN0cmluZyk6IG51bWJlciB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKCFzZXNzaW9uKSByZXR1cm4gMDtcblxuICAgIGNvbnN0IHN0YXJ0VGltZSA9IHRoaXMuc2Vzc2lvblN0YXJ0VGltZXMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKHN0YXJ0VGltZSkge1xuICAgICAgLy8gU2Vzc2lvbiBpcyBjdXJyZW50bHkgcnVubmluZywgYWRkIGN1cnJlbnQgcnVudGltZSB0byBzdG9yZWQgcnVudGltZVxuICAgICAgcmV0dXJuIChzZXNzaW9uLnJ1bnRpbWUgfHwgMCkgKyAoRGF0ZS5ub3coKSAtIHN0YXJ0VGltZSk7XG4gICAgfVxuXG4gICAgLy8gU2Vzc2lvbiBpcyBub3QgcnVubmluZywgcmV0dXJuIHN0b3JlZCBydW50aW1lXG4gICAgcmV0dXJuIHNlc3Npb24ucnVudGltZSB8fCAwO1xuICB9XG5cbiAgLy8gTWV0aG9kIHRvIHJlZnJlc2ggYmFja2VuZCBjb25uZWN0aW9uIGFmdGVyIGxvZ2luL2xvZ291dFxuICBwdWJsaWMgYXN5bmMgcmVmcmVzaEJhY2tlbmRDb25uZWN0aW9uKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5SEIFJlZnJlc2hpbmcgYmFja2VuZCBjb25uZWN0aW9uLi4uJyk7XG4gICAgYXdhaXQgdGhpcy5pbml0aWFsaXplQmFja2VuZENvbm5lY3Rpb24oKTtcbiAgfVxuXG4gIC8vIE1ldGhvZCB0byBkaXNhYmxlIGJhY2tlbmQgbW9kZSBkdWUgdG8gYXV0aGVudGljYXRpb24gaXNzdWVzXG4gIHB1YmxpYyBkaXNhYmxlQmFja2VuZE1vZGUoKTogdm9pZCB7XG4gICAgY29uc29sZS5sb2coJ/CflJAgRGlzYWJsaW5nIGJhY2tlbmQgbW9kZSBkdWUgdG8gYXV0aGVudGljYXRpb24gaXNzdWVzJyk7XG4gICAgdGhpcy51c2VCYWNrZW5kID0gZmFsc2U7XG4gICAgdGhpcy5pc0luaXRpYWxpemluZyA9IGZhbHNlO1xuICB9XG5cbiAgLy8gTWV0aG9kIHRvIGhhbmRsZSBsb2dvdXQgLSBzd2l0Y2ggdG8gbG9jYWxTdG9yYWdlIG1vZGVcbiAgcHVibGljIGhhbmRsZUxvZ291dCgpOiB2b2lkIHtcbiAgICBjb25zb2xlLmxvZygn8J+RiyBVc2VyIGxvZ2dlZCBvdXQsIHN3aXRjaGluZyB0byBsb2NhbFN0b3JhZ2UgbW9kZScpO1xuICAgIHRoaXMudXNlQmFja2VuZCA9IGZhbHNlO1xuICAgIHRoaXMuc2Vzc2lvbnMuY2xlYXIoKTtcbiAgICB0aGlzLmN1cnJlbnRTZXNzaW9uSWQgPSBudWxsO1xuICAgIHRoaXMubG9hZFNlc3Npb25zRnJvbVN0b3JhZ2UoKTtcbiAgfVxuXG4gIHB1YmxpYyBleHBvcnRTZXNzaW9uVG9KU09OKHNlc3Npb25JZDogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKCFzZXNzaW9uKSByZXR1cm4gbnVsbDtcblxuICAgIHJldHVybiBKU09OLnN0cmluZ2lmeShzZXNzaW9uLCBudWxsLCAyKTtcbiAgfVxuXG4gIHB1YmxpYyBpbXBvcnRTZXNzaW9uRnJvbUpTT04oanNvbkRhdGE6IHN0cmluZyk6IHN0cmluZyB8IG51bGwge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZXNzaW9uRGF0YSA9IEpTT04ucGFyc2UoanNvbkRhdGEpO1xuICAgICAgY29uc3Qgc2Vzc2lvbklkID0gdXVpZHY0KCk7XG4gICAgICBcbiAgICAgIGNvbnN0IGltcG9ydGVkU2Vzc2lvbjogVHJhZGluZ1Nlc3Npb24gPSB7XG4gICAgICAgIC4uLnNlc3Npb25EYXRhLFxuICAgICAgICBpZDogc2Vzc2lvbklkLFxuICAgICAgICBpc0FjdGl2ZTogZmFsc2UsXG4gICAgICAgIGxhc3RNb2RpZmllZDogRGF0ZS5ub3coKVxuICAgICAgfTtcblxuICAgICAgdGhpcy5zZXNzaW9ucy5zZXQoc2Vzc2lvbklkLCBpbXBvcnRlZFNlc3Npb24pO1xuICAgICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcbiAgICAgIHJldHVybiBzZXNzaW9uSWQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBpbXBvcnQgc2Vzc2lvbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH1cblxuICBwdWJsaWMgcmVuYW1lU2Vzc2lvbihzZXNzaW9uSWQ6IHN0cmluZywgbmV3TmFtZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKCFzZXNzaW9uKSByZXR1cm4gZmFsc2U7XG5cbiAgICBzZXNzaW9uLm5hbWUgPSBuZXdOYW1lO1xuICAgIHNlc3Npb24ubGFzdE1vZGlmaWVkID0gRGF0ZS5ub3coKTtcbiAgICB0aGlzLnNlc3Npb25zLnNldChzZXNzaW9uSWQsIHNlc3Npb24pO1xuICAgIHRoaXMuc2F2ZVNlc3Npb25zVG9TdG9yYWdlKCk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cblxuICBwdWJsaWMgYXN5bmMgdXBkYXRlU2Vzc2lvbkFsYXJtU2V0dGluZ3Moc2Vzc2lvbklkOiBzdHJpbmcsIGFsYXJtU2V0dGluZ3M6IEFwcFNldHRpbmdzKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuc2Vzc2lvbnMuZ2V0KHNlc3Npb25JZCk7XG4gICAgaWYgKCFzZXNzaW9uKSByZXR1cm4gZmFsc2U7XG5cbiAgICBzZXNzaW9uLmFsYXJtU2V0dGluZ3MgPSBhbGFybVNldHRpbmdzO1xuICAgIHNlc3Npb24ubGFzdE1vZGlmaWVkID0gRGF0ZS5ub3coKTtcbiAgICB0aGlzLnNlc3Npb25zLnNldChzZXNzaW9uSWQsIHNlc3Npb24pO1xuXG4gICAgLy8gU2F2ZSB0byBiYWNrZW5kIGlmIGF2YWlsYWJsZVxuICAgIGlmICh0aGlzLnVzZUJhY2tlbmQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHNlc3Npb25EYXRhID0ge1xuICAgICAgICAgIG5hbWU6IHNlc3Npb24ubmFtZSxcbiAgICAgICAgICBjb25maWc6IHNlc3Npb24uY29uZmlnLFxuICAgICAgICAgIHRhcmdldFByaWNlUm93czogc2Vzc2lvbi50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgICAgY3VycmVudE1hcmtldFByaWNlOiBzZXNzaW9uLmN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgICBjcnlwdG8xQmFsYW5jZTogc2Vzc2lvbi5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgICBjcnlwdG8yQmFsYW5jZTogc2Vzc2lvbi5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgICBzdGFibGVjb2luQmFsYW5jZTogc2Vzc2lvbi5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgICAgICBpc0FjdGl2ZTogc2Vzc2lvbi5pc0FjdGl2ZSxcbiAgICAgICAgICBhbGFybV9zZXR0aW5nczogYWxhcm1TZXR0aW5nc1xuICAgICAgICB9O1xuXG4gICAgICAgIGF3YWl0IHNlc3Npb25BcGkudXBkYXRlU2Vzc2lvbihzZXNzaW9uSWQsIHNlc3Npb25EYXRhKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBTZXNzaW9uIGFsYXJtIHNldHRpbmdzIHNhdmVkIHRvIGJhY2tlbmQ6Jywgc2Vzc2lvbklkKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWlsZWQgdG8gc2F2ZSBzZXNzaW9uIGFsYXJtIHNldHRpbmdzIHRvIGJhY2tlbmQ6JywgZXJyb3IpO1xuICAgICAgICAvLyBDb250aW51ZSB3aXRoIGxvY2FsU3RvcmFnZSBzYXZlIGFzIGZhbGxiYWNrXG4gICAgICB9XG4gICAgfVxuXG4gICAgdGhpcy5zYXZlU2Vzc2lvbnNUb1N0b3JhZ2UoKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuXG4gIHB1YmxpYyBnZXRTZXNzaW9uSGlzdG9yeShzZXNzaW9uSWQ6IHN0cmluZyk6IE9yZGVySGlzdG9yeUVudHJ5W10ge1xuICAgIGNvbnN0IHNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChzZXNzaW9uSWQpO1xuICAgIHJldHVybiBzZXNzaW9uID8gWy4uLnNlc3Npb24ub3JkZXJIaXN0b3J5XSA6IFtdO1xuICB9XG5cbiAgcHVibGljIGV4cG9ydFNlc3Npb25Ub0NTVihzZXNzaW9uSWQ6IHN0cmluZyk6IHN0cmluZyB8IG51bGwge1xuICAgIGNvbnN0IHNlc3Npb24gPSB0aGlzLnNlc3Npb25zLmdldChzZXNzaW9uSWQpO1xuICAgIGlmICghc2Vzc2lvbikgcmV0dXJuIG51bGw7XG5cbiAgICBjb25zdCBoZWFkZXJzID0gW1xuICAgICAgJ0RhdGUnLFxuICAgICAgJ1RpbWUnLFxuICAgICAgJ1BhaXInLFxuICAgICAgJ0NyeXB0bycsXG4gICAgICAnT3JkZXIgVHlwZScsXG4gICAgICAnQW1vdW50JyxcbiAgICAgICdBdmcgUHJpY2UnLFxuICAgICAgJ1ZhbHVlJyxcbiAgICAgICdQcmljZSAxJyxcbiAgICAgICdDcnlwdG8gMScsXG4gICAgICAnUHJpY2UgMicsXG4gICAgICAnQ3J5cHRvIDInLFxuICAgICAgJ1Byb2ZpdC9Mb3NzIChDcnlwdG8xKScsXG4gICAgICAnUHJvZml0L0xvc3MgKENyeXB0bzIpJ1xuICAgIF07XG5cbiAgICBjb25zdCBjc3ZDb250ZW50ID0gW1xuICAgICAgaGVhZGVycy5qb2luKCcsJyksXG4gICAgICAuLi5zZXNzaW9uLm9yZGVySGlzdG9yeS5tYXAoZW50cnkgPT4gW1xuICAgICAgICBuZXcgRGF0ZShlbnRyeS50aW1lc3RhbXApLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgbmV3IERhdGUoZW50cnkudGltZXN0YW1wKS50b1RpbWVTdHJpbmcoKS5zcGxpdCgnICcpWzBdLFxuICAgICAgICBlbnRyeS5wYWlyLFxuICAgICAgICBlbnRyeS5jcnlwdG8xU3ltYm9sLFxuICAgICAgICBlbnRyeS5vcmRlclR5cGUsXG4gICAgICAgIGVudHJ5LmFtb3VudENyeXB0bzE/LnRvRml4ZWQoc2Vzc2lvbi5jb25maWcubnVtRGlnaXRzKSB8fCAnJyxcbiAgICAgICAgZW50cnkuYXZnUHJpY2U/LnRvRml4ZWQoc2Vzc2lvbi5jb25maWcubnVtRGlnaXRzKSB8fCAnJyxcbiAgICAgICAgZW50cnkudmFsdWVDcnlwdG8yPy50b0ZpeGVkKHNlc3Npb24uY29uZmlnLm51bURpZ2l0cykgfHwgJycsXG4gICAgICAgIGVudHJ5LnByaWNlMT8udG9GaXhlZChzZXNzaW9uLmNvbmZpZy5udW1EaWdpdHMpIHx8ICcnLFxuICAgICAgICBlbnRyeS5jcnlwdG8xU3ltYm9sLFxuICAgICAgICBlbnRyeS5wcmljZTI/LnRvRml4ZWQoc2Vzc2lvbi5jb25maWcubnVtRGlnaXRzKSB8fCAnJyxcbiAgICAgICAgZW50cnkuY3J5cHRvMlN5bWJvbCxcbiAgICAgICAgZW50cnkucmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMT8udG9GaXhlZChzZXNzaW9uLmNvbmZpZy5udW1EaWdpdHMpIHx8ICcnLFxuICAgICAgICBlbnRyeS5yZWFsaXplZFByb2ZpdExvc3NDcnlwdG8yPy50b0ZpeGVkKHNlc3Npb24uY29uZmlnLm51bURpZ2l0cykgfHwgJydcbiAgICAgIF0uam9pbignLCcpKVxuICAgIF0uam9pbignXFxuJyk7XG5cbiAgICByZXR1cm4gY3N2Q29udGVudDtcbiAgfVxuXG4gIHB1YmxpYyBjbGVhckFsbFNlc3Npb25zKCk6IHZvaWQge1xuICAgIHRoaXMuc2Vzc2lvbnMuY2xlYXIoKTtcbiAgICB0aGlzLmN1cnJlbnRTZXNzaW9uSWQgPSBudWxsO1xuICAgIC8vIENsZWFyIHNoYXJlZCBzZXNzaW9ucyBzdG9yYWdlXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oU0VTU0lPTlNfU1RPUkFHRV9LRVkpO1xuICAgIC8vIENsZWFyIHdpbmRvdy1zcGVjaWZpYyBjdXJyZW50IHNlc3Npb25cbiAgICBjb25zdCBjdXJyZW50U2Vzc2lvbktleSA9IHRoaXMuZ2V0V2luZG93U3BlY2lmaWNLZXkoQ1VSUkVOVF9TRVNTSU9OX0tFWSk7XG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oY3VycmVudFNlc3Npb25LZXkpO1xuICB9XG5cbiAgLy8gQXV0by1zYXZlIGZ1bmN0aW9uYWxpdHlcbiAgcHVibGljIGVuYWJsZUF1dG9TYXZlKFxuICAgIHNlc3Npb25JZDogc3RyaW5nLFxuICAgIGdldFNlc3Npb25EYXRhOiAoKSA9PiB7XG4gICAgICBjb25maWc6IFRyYWRpbmdDb25maWc7XG4gICAgICB0YXJnZXRQcmljZVJvd3M6IFRhcmdldFByaWNlUm93W107XG4gICAgICBvcmRlckhpc3Rvcnk6IE9yZGVySGlzdG9yeUVudHJ5W107XG4gICAgICBjdXJyZW50TWFya2V0UHJpY2U6IG51bWJlcjtcbiAgICAgIGNyeXB0bzFCYWxhbmNlOiBudW1iZXI7XG4gICAgICBjcnlwdG8yQmFsYW5jZTogbnVtYmVyO1xuICAgICAgc3RhYmxlY29pbkJhbGFuY2U6IG51bWJlcjtcbiAgICAgIGlzQWN0aXZlOiBib29sZWFuO1xuICAgIH0sXG4gICAgaW50ZXJ2YWxNczogbnVtYmVyID0gMzAwMDAgLy8gMzAgc2Vjb25kc1xuICApOiAoKSA9PiB2b2lkIHtcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIGNvbnN0IGRhdGEgPSBnZXRTZXNzaW9uRGF0YSgpO1xuICAgICAgdGhpcy5zYXZlU2Vzc2lvbihcbiAgICAgICAgc2Vzc2lvbklkLFxuICAgICAgICBkYXRhLmNvbmZpZyxcbiAgICAgICAgZGF0YS50YXJnZXRQcmljZVJvd3MsXG4gICAgICAgIGRhdGEub3JkZXJIaXN0b3J5LFxuICAgICAgICBkYXRhLmN1cnJlbnRNYXJrZXRQcmljZSxcbiAgICAgICAgZGF0YS5jcnlwdG8xQmFsYW5jZSxcbiAgICAgICAgZGF0YS5jcnlwdG8yQmFsYW5jZSxcbiAgICAgICAgZGF0YS5zdGFibGVjb2luQmFsYW5jZSxcbiAgICAgICAgZGF0YS5pc0FjdGl2ZVxuICAgICAgKTtcbiAgICB9LCBpbnRlcnZhbE1zKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbInY0IiwidXVpZHY0Iiwic2Vzc2lvbkFwaSIsIlNFU1NJT05TX1NUT1JBR0VfS0VZIiwiQ1VSUkVOVF9TRVNTSU9OX0tFWSIsIkFDVElWRV9TRVNTSU9OU19LRVkiLCJnZW5lcmF0ZVdpbmRvd0lkIiwiTWF0aCIsIkRhdGUiLCJub3ciLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsImdldFdpbmRvd0lkIiwid2luZG93SWQiLCJzZXNzaW9uU3RvcmFnZSIsImdldEl0ZW0iLCJzZXRJdGVtIiwiY29uc29sZSIsImxvZyIsIlNlc3Npb25NYW5hZ2VyIiwiZ2V0SW5zdGFuY2UiLCJpbnN0YW5jZSIsImdlbmVyYXRlU2Vzc2lvbk5hbWUiLCJjb25maWciLCJjcnlwdG8xIiwiY3J5cHRvMiIsInRyYWRpbmdNb2RlIiwiYmFzZU5hbWUiLCJleGlzdGluZ1Nlc3Npb25zIiwiQXJyYXkiLCJmcm9tIiwic2Vzc2lvbnMiLCJ2YWx1ZXMiLCJzaW1pbGFyU2Vzc2lvbnMiLCJmaWx0ZXIiLCJzZXNzaW9uIiwibmFtZSIsInN0YXJ0c1dpdGgiLCJsZW5ndGgiLCJtYXhOdW1iZXIiLCJmb3JFYWNoIiwibWF0Y2giLCJSZWdFeHAiLCJyZXBsYWNlIiwibnVtYmVyIiwicGFyc2VJbnQiLCJtYXgiLCJpbml0aWFsaXplQmFja2VuZENvbm5lY3Rpb24iLCJpc0luaXRpYWxpemluZyIsImF1dGhUb2tlbiIsImxvY2FsU3RvcmFnZSIsInVzZUJhY2tlbmQiLCJsb2FkU2Vzc2lvbnNGcm9tU3RvcmFnZSIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwib2siLCJsb2FkU2Vzc2lvbnNGcm9tQmFja2VuZCIsIkVycm9yIiwiZXJyb3IiLCJjaGVja0JhY2tlbmRDb25uZWN0aW9uIiwiZ2V0V2luZG93U3BlY2lmaWNLZXkiLCJiYXNlS2V5IiwibG9hZEFjdGl2ZVNlc3Npb25zRnJvbVN0b3JhZ2UiLCJhY3RpdmVTZXNzaW9uc0RhdGEiLCJwYXJzZWRBY3RpdmVTZXNzaW9ucyIsIkpTT04iLCJwYXJzZSIsImFjdGl2ZVNlc3Npb25zQWNyb3NzV2luZG93cyIsIk1hcCIsIk9iamVjdCIsImVudHJpZXMiLCJzdGFsZVRocmVzaG9sZCIsImtleSIsImVudHJ5IiwidGltZXN0YW1wIiwiZGVsZXRlIiwic2F2ZUFjdGl2ZVNlc3Npb25zVG9TdG9yYWdlIiwiYWN0aXZlU2Vzc2lvbnNPYmplY3QiLCJmcm9tRW50cmllcyIsInN0cmluZ2lmeSIsIndpbmRvdyIsImRpc3BhdGNoRXZlbnQiLCJTdG9yYWdlRXZlbnQiLCJuZXdWYWx1ZSIsInN0b3JhZ2VBcmVhIiwiYWRkQWN0aXZlU2Vzc2lvbiIsInNlc3Npb25JZCIsIndhcm4iLCJzZXQiLCJyZW1vdmVBY3RpdmVTZXNzaW9uIiwic2V0dXBTdG9yYWdlTGlzdGVuZXIiLCJhZGRFdmVudExpc3RlbmVyIiwiZXZlbnQiLCJwYXJzZWRTZXNzaW9ucyIsInNpemUiLCJoYW5kbGVBcHBSZXN0YXJ0IiwibGFzdEFwcENsb3NlIiwiYXBwU3RhcnRUaW1lIiwiaXNBcHBSZXN0YXJ0IiwicnVubmluZ1Nlc3Npb25zIiwiaXNBY3RpdmUiLCJtYXAiLCJzIiwiaWQiLCJ0b0xvY2FsZVN0cmluZyIsIm1vbnRoIiwiZGF5IiwiaG91ciIsIm1pbnV0ZSIsImhvdXIxMiIsImF1dG9TYXZlZE5hbWUiLCJhdXRvU2F2ZWRJZCIsImNyZWF0ZU5ld1Nlc3Npb25TeW5jIiwic2F2ZVNlc3Npb24iLCJ0YXJnZXRQcmljZVJvd3MiLCJvcmRlckhpc3RvcnkiLCJjdXJyZW50TWFya2V0UHJpY2UiLCJjcnlwdG8xQmFsYW5jZSIsImNyeXB0bzJCYWxhbmNlIiwic3RhYmxlY29pbkJhbGFuY2UiLCJsYXN0TW9kaWZpZWQiLCJjdXJyZW50U2Vzc2lvbklkIiwiY3VycmVudFNlc3Npb25LZXkiLCJyZW1vdmVJdGVtIiwic2F2ZVNlc3Npb25zVG9TdG9yYWdlIiwiY2xlYW51cFN0YWxlUGVyc2lzdGVuY2VJbmZvIiwia2V5c1RvUmVtb3ZlIiwiaSIsInBlcnNpc3RlbmNlSW5mbyIsImxhc3RTYXZlZCIsInB1c2giLCJzZXNzaW9uc0RhdGEiLCJ3aW5kb3dTcGVjaWZpY0tleSIsImFjdGl2ZVNlc3Npb25zIiwiXyIsIm1vc3RSZWNlbnRTZXNzaW9uIiwicmVkdWNlIiwibGF0ZXN0IiwiY3VycmVudCIsImN1cnJlbnRTZXNzaW9uIiwiZ2V0IiwidG9rZW4iLCJnZXRBbGxTZXNzaW9ucyIsImNsZWFyIiwic2Vzc2lvbkRhdGEiLCJzZXNzaW9uX3V1aWQiLCJjb25maWdfc25hcHNob3QiLCJjcmVhdGVkQXQiLCJjcmVhdGVkX2F0IiwiZ2V0VGltZSIsImxhc3RfbW9kaWZpZWQiLCJpc19hY3RpdmUiLCJydW50aW1lIiwidGFyZ2V0X3ByaWNlX3Jvd3MiLCJvcmRlcl9oaXN0b3J5IiwiY3VycmVudF9tYXJrZXRfcHJpY2UiLCJjcnlwdG8xX2JhbGFuY2UiLCJjcnlwdG8yX2JhbGFuY2UiLCJzdGFibGVjb2luX2JhbGFuY2UiLCJhbGFybVNldHRpbmdzIiwiYWxhcm1fc2V0dGluZ3MiLCJ1bmRlZmluZWQiLCJhY3RpdmVTZXNzaW9uIiwiZmluZCIsImVycm9yTWVzc2FnZSIsIm1lc3NhZ2UiLCJTdHJpbmciLCJpbmNsdWRlcyIsInNlc3Npb25zT2JqZWN0IiwiY3JlYXRlTmV3U2Vzc2lvbldpdGhBdXRvTmFtZSIsImN1c3RvbU5hbWUiLCJjdXJyZW50QmFsYW5jZXMiLCJzZXNzaW9uTmFtZSIsImNyZWF0ZU5ld1Nlc3Npb24iLCJiYWxhbmNlcyIsInN0YWJsZWNvaW4iLCJuZXdTZXNzaW9uIiwiY3JlYXRlU2Vzc2lvbiIsIm92ZXJyaWRlUnVudGltZSIsImN1cnJlbnRSdW50aW1lIiwic3RhcnRUaW1lIiwic2Vzc2lvblN0YXJ0VGltZXMiLCJ1cGRhdGVkU2Vzc2lvbiIsImFkZGl0aW9uYWxSdW50aW1lIiwidXBkYXRlU2Vzc2lvbiIsIm5ld1Nlc3Npb25JZCIsInNldEN1cnJlbnRTZXNzaW9uIiwiY3JlYXRlRXJyb3IiLCJsb2FkU2Vzc2lvbiIsImRlbGV0ZVNlc3Npb24iLCJkZWxldGVkIiwicGFpciIsImdldEN1cnJlbnRSdW50aW1lIiwidG90YWxUcmFkZXMiLCJ0b3RhbFByb2ZpdExvc3MiLCJ0cmFkZSIsIm9yZGVyVHlwZSIsInJlYWxpemVkUHJvZml0TG9zc0NyeXB0bzIiLCJzdW0iLCJnZXRDdXJyZW50QWN0aXZlU2Vzc2lvbnMiLCJhY3RpdmVTZXNzaW9uSWRzIiwiU2V0IiwiaGFzIiwiYWRkIiwiYWxsU2Vzc2lvbnMiLCJrZXlzIiwiYWxsQWN0aXZlU2Vzc2lvbnMiLCJpc0luQWN0aXZlVHJhY2tpbmciLCJpc01hcmtlZEFjdGl2ZSIsImlzQ3VycmVudFNlc3Npb24iLCJzaG91bGRJbmNsdWRlIiwiZ2V0Q3VycmVudFNlc3Npb25JZCIsImNsZWFyQ3VycmVudFNlc3Npb24iLCJpc1N0aWxsQWN0aXZlSW5PdGhlcldpbmRvd3MiLCJzb21lIiwic3RhcnRTZXNzaW9uUnVudGltZSIsInN0b3BTZXNzaW9uUnVudGltZSIsImRlYWN0aXZhdGVTZXNzaW9uIiwicmVmcmVzaEJhY2tlbmRDb25uZWN0aW9uIiwiZGlzYWJsZUJhY2tlbmRNb2RlIiwiaGFuZGxlTG9nb3V0IiwiZXhwb3J0U2Vzc2lvblRvSlNPTiIsImltcG9ydFNlc3Npb25Gcm9tSlNPTiIsImpzb25EYXRhIiwiaW1wb3J0ZWRTZXNzaW9uIiwicmVuYW1lU2Vzc2lvbiIsIm5ld05hbWUiLCJ1cGRhdGVTZXNzaW9uQWxhcm1TZXR0aW5ncyIsImdldFNlc3Npb25IaXN0b3J5IiwiZXhwb3J0U2Vzc2lvblRvQ1NWIiwiY3N2Q29udGVudCIsImpvaW4iLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwidG9UaW1lU3RyaW5nIiwiY3J5cHRvMVN5bWJvbCIsImFtb3VudENyeXB0bzEiLCJ0b0ZpeGVkIiwibnVtRGlnaXRzIiwiYXZnUHJpY2UiLCJ2YWx1ZUNyeXB0bzIiLCJwcmljZTEiLCJwcmljZTIiLCJjcnlwdG8yU3ltYm9sIiwicmVhbGl6ZWRQcm9maXRMb3NzQ3J5cHRvMSIsImNsZWFyQWxsU2Vzc2lvbnMiLCJlbmFibGVBdXRvU2F2ZSIsImdldFNlc3Npb25EYXRhIiwiaW50ZXJ2YWxNcyIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJkYXRhIiwiY2xlYXJJbnRlcnZhbCIsInNldFRpbWVvdXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

}]);