"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_session-manager_ts"],{

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js":
/*!******************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/native.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  randomUUID\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvbmF0aXZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLGlFQUFlO0FBQ2Y7QUFDQSxDQUFDIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFx1dWlkXFxkaXN0XFxlc20tYnJvd3NlclxcbmF0aXZlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHJhbmRvbVVVSUQgPSB0eXBlb2YgY3J5cHRvICE9PSAndW5kZWZpbmVkJyAmJiBjcnlwdG8ucmFuZG9tVVVJRCAmJiBjcnlwdG8ucmFuZG9tVVVJRC5iaW5kKGNyeXB0byk7XG5leHBvcnQgZGVmYXVsdCB7XG4gIHJhbmRvbVVVSURcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js":
/*!*****************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/regex.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvcmVnZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWMsRUFBRSxVQUFVLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixFQUFFLFVBQVUsR0FBRyx5Q0FBeUMiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFxyZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLTVdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCkkL2k7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js":
/*!***************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/rng.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\n// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nfunction rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvcm5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFx1dWlkXFxkaXN0XFxlc20tYnJvd3Nlclxccm5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFVuaXF1ZSBJRCBjcmVhdGlvbiByZXF1aXJlcyBhIGhpZ2ggcXVhbGl0eSByYW5kb20gIyBnZW5lcmF0b3IuIEluIHRoZSBicm93c2VyIHdlIHRoZXJlZm9yZVxuLy8gcmVxdWlyZSB0aGUgY3J5cHRvIEFQSSBhbmQgZG8gbm90IHN1cHBvcnQgYnVpbHQtaW4gZmFsbGJhY2sgdG8gbG93ZXIgcXVhbGl0eSByYW5kb20gbnVtYmVyXG4vLyBnZW5lcmF0b3JzIChsaWtlIE1hdGgucmFuZG9tKCkpLlxubGV0IGdldFJhbmRvbVZhbHVlcztcbmNvbnN0IHJuZHM4ID0gbmV3IFVpbnQ4QXJyYXkoMTYpO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcm5nKCkge1xuICAvLyBsYXp5IGxvYWQgc28gdGhhdCBlbnZpcm9ubWVudHMgdGhhdCBuZWVkIHRvIHBvbHlmaWxsIGhhdmUgYSBjaGFuY2UgdG8gZG8gc29cbiAgaWYgKCFnZXRSYW5kb21WYWx1ZXMpIHtcbiAgICAvLyBnZXRSYW5kb21WYWx1ZXMgbmVlZHMgdG8gYmUgaW52b2tlZCBpbiBhIGNvbnRleHQgd2hlcmUgXCJ0aGlzXCIgaXMgYSBDcnlwdG8gaW1wbGVtZW50YXRpb24uXG4gICAgZ2V0UmFuZG9tVmFsdWVzID0gdHlwZW9mIGNyeXB0byAhPT0gJ3VuZGVmaW5lZCcgJiYgY3J5cHRvLmdldFJhbmRvbVZhbHVlcyAmJiBjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzLmJpbmQoY3J5cHRvKTtcblxuICAgIGlmICghZ2V0UmFuZG9tVmFsdWVzKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ2NyeXB0by5nZXRSYW5kb21WYWx1ZXMoKSBub3Qgc3VwcG9ydGVkLiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3V1aWRqcy91dWlkI2dldHJhbmRvbXZhbHVlcy1ub3Qtc3VwcG9ydGVkJyk7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGdldFJhbmRvbVZhbHVlcyhybmRzOCk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js":
/*!*********************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/stringify.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js\");\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nfunction unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js":
/*!**************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/v4.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js\");\n\n\n\n\nfunction v4(options, buf, offset) {\n  if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n    return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNOO0FBQ3NCOztBQUVqRDtBQUNBLE1BQU0sa0RBQU07QUFDWixXQUFXLGtEQUFNO0FBQ2pCOztBQUVBO0FBQ0EsaURBQWlELCtDQUFHLEtBQUs7O0FBRXpEO0FBQ0EsbUNBQW1DOztBQUVuQztBQUNBOztBQUVBLG9CQUFvQixRQUFRO0FBQzVCO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxTQUFTLDhEQUFlO0FBQ3hCOztBQUVBLGlFQUFlLEVBQUUiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFx2NC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbmF0aXZlIGZyb20gJy4vbmF0aXZlLmpzJztcbmltcG9ydCBybmcgZnJvbSAnLi9ybmcuanMnO1xuaW1wb3J0IHsgdW5zYWZlU3RyaW5naWZ5IH0gZnJvbSAnLi9zdHJpbmdpZnkuanMnO1xuXG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICBpZiAobmF0aXZlLnJhbmRvbVVVSUQgJiYgIWJ1ZiAmJiAhb3B0aW9ucykge1xuICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICB9XG5cbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gIGNvbnN0IHJuZHMgPSBvcHRpb25zLnJhbmRvbSB8fCAob3B0aW9ucy5ybmcgfHwgcm5nKSgpOyAvLyBQZXIgNC40LCBzZXQgYml0cyBmb3IgdmVyc2lvbiBhbmQgYGNsb2NrX3NlcV9oaV9hbmRfcmVzZXJ2ZWRgXG5cbiAgcm5kc1s2XSA9IHJuZHNbNl0gJiAweDBmIHwgMHg0MDtcbiAgcm5kc1s4XSA9IHJuZHNbOF0gJiAweDNmIHwgMHg4MDsgLy8gQ29weSBieXRlcyB0byBidWZmZXIsIGlmIHByb3ZpZGVkXG5cbiAgaWYgKGJ1Zikge1xuICAgIG9mZnNldCA9IG9mZnNldCB8fCAwO1xuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCAxNjsgKytpKSB7XG4gICAgICBidWZbb2Zmc2V0ICsgaV0gPSBybmRzW2ldO1xuICAgIH1cblxuICAgIHJldHVybiBidWY7XG4gIH1cblxuICByZXR1cm4gdW5zYWZlU3RyaW5naWZ5KHJuZHMpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2NDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js":
/*!********************************************************!*\
  !*** ./node_modules/uuid/dist/esm-browser/validate.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js\");\n\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLWJyb3dzZXIvdmFsaWRhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7O0FBRS9CO0FBQ0EscUNBQXFDLGlEQUFLO0FBQzFDOztBQUVBLGlFQUFlLFFBQVEiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1icm93c2VyXFx2YWxpZGF0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUkVHRVggZnJvbSAnLi9yZWdleC5qcyc7XG5cbmZ1bmN0aW9uIHZhbGlkYXRlKHV1aWQpIHtcbiAgcmV0dXJuIHR5cGVvZiB1dWlkID09PSAnc3RyaW5nJyAmJiBSRUdFWC50ZXN0KHV1aWQpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2YWxpZGF0ZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\nconst ACTIVE_SESSIONS_KEY = 'pluto_active_sessions'; // Track active sessions across all windows\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 11));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n        console.log(\"\\uD83C\\uDD95 Created new window ID: \".concat(windowId));\n    } else {\n        console.log(\"\\uD83D\\uDD04 Using existing window ID: \".concat(windowId));\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackendConnection() {\n        // Prevent multiple initialization attempts\n        if (this.isInitializing) {\n            console.log('⚠️ Backend initialization already in progress, skipping');\n            return;\n        }\n        this.isInitializing = true;\n        try {\n            // Check if user is authenticated\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                console.log('⚠️ No auth token found, using localStorage mode until login');\n                this.useBackend = false;\n                this.loadSessionsFromStorage();\n                return;\n            }\n            // Test backend connection (health endpoint doesn't need auth)\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                console.log('✅ Backend connection established, testing auth and loading sessions');\n                this.useBackend = true;\n                // Load sessions from backend when connection is established\n                await this.loadSessionsFromBackend();\n            } else {\n                throw new Error('Backend health check failed');\n            }\n        } catch (error) {\n            console.log('⚠️ Backend not available, using localStorage mode:', error);\n            this.useBackend = false;\n            this.loadSessionsFromStorage();\n        } finally{\n            this.isInitializing = false;\n        }\n    }\n    async checkBackendConnection() {\n        try {\n            const authToken = localStorage.getItem('plutoAuthToken');\n            if (!authToken) {\n                return false;\n            }\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(authToken)\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.log('⚠️ Backend connection check failed:', error);\n            return false;\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    loadActiveSessionsFromStorage() {\n        try {\n            if (false) {}\n            const activeSessionsData = localStorage.getItem(ACTIVE_SESSIONS_KEY);\n            if (activeSessionsData) {\n                const parsedActiveSessions = JSON.parse(activeSessionsData);\n                this.activeSessionsAcrossWindows = new Map(Object.entries(parsedActiveSessions));\n                // Clean up stale entries (older than 5 minutes)\n                const now = Date.now();\n                const staleThreshold = 5 * 60 * 1000; // 5 minutes\n                for (const [key, entry] of this.activeSessionsAcrossWindows.entries()){\n                    if (now - entry.timestamp > staleThreshold) {\n                        this.activeSessionsAcrossWindows.delete(key);\n                    }\n                }\n                this.saveActiveSessionsToStorage();\n            }\n        } catch (error) {\n            console.error('Failed to load active sessions from storage:', error);\n        }\n    }\n    saveActiveSessionsToStorage() {\n        try {\n            if (false) {}\n            const activeSessionsObject = Object.fromEntries(this.activeSessionsAcrossWindows);\n            localStorage.setItem(ACTIVE_SESSIONS_KEY, JSON.stringify(activeSessionsObject));\n            // Trigger storage event to notify other windows/tabs (including admin panel)\n            window.dispatchEvent(new StorageEvent('storage', {\n                key: ACTIVE_SESSIONS_KEY,\n                newValue: JSON.stringify(activeSessionsObject),\n                storageArea: localStorage\n            }));\n        } catch (error) {\n            console.error('Failed to save active sessions to storage:', error);\n        }\n    }\n    addActiveSession(sessionId) {\n        // Validate session ID before adding\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.warn('⚠️ Attempted to add invalid session ID to active sessions:', sessionId);\n            return;\n        }\n        const key = \"\".concat(this.windowId, \"_\").concat(sessionId);\n        this.activeSessionsAcrossWindows.set(key, {\n            sessionId,\n            windowId: this.windowId,\n            timestamp: Date.now()\n        });\n        this.saveActiveSessionsToStorage();\n    }\n    removeActiveSession(sessionId) {\n        const key = \"\".concat(this.windowId, \"_\").concat(sessionId);\n        this.activeSessionsAcrossWindows.delete(key);\n        this.saveActiveSessionsToStorage();\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Try to load current session from multiple sources for better persistence\n            let currentSessionId = null;\n            // 1. First try window-specific storage (for new tabs)\n            const windowSpecificKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            currentSessionId = localStorage.getItem(windowSpecificKey);\n            // 2. If not found, try sessionStorage (survives page refresh)\n            if (!currentSessionId) {\n                currentSessionId = sessionStorage.getItem(CURRENT_SESSION_KEY);\n            }\n            // 3. If still not found, look for any active session (fallback)\n            if (!currentSessionId && sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                const activeSessions = Object.entries(parsedSessions).filter((param)=>{\n                    let [_, session] = param;\n                    return session.isActive && session.lastModified && Date.now() - session.lastModified < 30 * 60 * 1000 // Active within last 30 minutes\n                    ;\n                });\n                if (activeSessions.length > 0) {\n                    // Get the most recently active session\n                    const mostRecentSession = activeSessions.reduce((latest, current)=>current[1].lastModified > latest[1].lastModified ? current : latest);\n                    currentSessionId = mostRecentSession[0];\n                    console.log(\"\\uD83D\\uDD04 Restored most recent active session: \".concat(currentSessionId));\n                }\n            }\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            // Store current session in sessionStorage for page refresh persistence\n            if (currentSessionId) {\n                sessionStorage.setItem(CURRENT_SESSION_KEY, currentSessionId);\n                // Ensure the current session is marked as active if it exists\n                const currentSession = this.sessions.get(currentSessionId);\n                if (currentSession && currentSession.isActive) {\n                    this.addActiveSession(currentSessionId);\n                    console.log(\"✅ Restored active session tracking for: \".concat(currentSessionId));\n                }\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId, \", current: \").concat(currentSessionId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        try {\n            // Check if user is authenticated before making API calls\n            const token =  true ? localStorage.getItem('plutoAuthToken') : 0;\n            if (!token || token.length < 10) {\n                console.log('⚠️ Invalid or missing auth token, skipping backend session loading');\n                this.useBackend = false; // Disable backend mode\n                this.loadSessionsFromStorage();\n                return;\n            }\n            console.log('🔄 Loading sessions from backend...');\n            const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n            const response = await sessionApi.getAllSessions(true);\n            console.log('✅ Backend response received:', response);\n            if (response && response.sessions) {\n                // Convert backend sessions to our internal format\n                this.sessions.clear();\n                response.sessions.forEach((session)=>{\n                    const sessionData = {\n                        id: session.session_uuid,\n                        name: session.name,\n                        config: JSON.parse(session.config_snapshot || '{}'),\n                        createdAt: new Date(session.created_at).getTime(),\n                        lastModified: new Date(session.last_modified).getTime(),\n                        isActive: session.is_active,\n                        runtime: session.runtime || 0,\n                        targetPriceRows: session.target_price_rows ? JSON.parse(session.target_price_rows) : [],\n                        orderHistory: session.order_history ? JSON.parse(session.order_history) : [],\n                        currentMarketPrice: session.current_market_price || 100000,\n                        crypto1Balance: session.crypto1_balance || 10000,\n                        crypto2Balance: session.crypto2_balance || 10000,\n                        stablecoinBalance: session.stablecoin_balance || 10000,\n                        alarmSettings: session.alarm_settings ? JSON.parse(session.alarm_settings) : undefined\n                    };\n                    this.sessions.set(session.session_uuid, sessionData);\n                });\n                // Find active session and restore it properly\n                const activeSession = response.sessions.find((s)=>s.is_active);\n                if (activeSession) {\n                    this.currentSessionId = activeSession.session_uuid;\n                    // Store in both localStorage and sessionStorage for persistence\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.setItem(currentSessionKey, activeSession.session_uuid);\n                    sessionStorage.setItem(CURRENT_SESSION_KEY, activeSession.session_uuid);\n                    console.log(\"✅ Restored active session from backend: \".concat(activeSession.session_uuid));\n                }\n                // Also save to localStorage as backup\n                this.saveSessionsToStorage();\n                console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // Handle authentication errors gracefully\n            if (errorMessage.includes('Authentication') || errorMessage.includes('401') || errorMessage.includes('422')) {\n                console.log('🔐 Authentication issue detected, disabling backend mode');\n                this.useBackend = false; // Disable backend mode to prevent future API calls\n            } else if (errorMessage.includes('Cannot connect to server')) {\n                console.log('🌐 Backend server not available, using local storage only');\n            } else {\n                // Only log detailed errors for unexpected issues\n                console.warn('⚠️ Backend session loading failed, falling back to local storage:', errorMessage);\n            }\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n            // Trigger storage event to notify other windows/tabs (including admin panel)\n            window.dispatchEvent(new StorageEvent('storage', {\n                key: SESSIONS_STORAGE_KEY,\n                newValue: JSON.stringify(sessionsObject),\n                storageArea: localStorage\n            }));\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName, currentBalances) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config, currentBalances);\n    }\n    async createNewSession(name, config, currentBalances) {\n        // Use provided balances or default values\n        const balances = currentBalances || {\n            crypto1: 10,\n            crypto2: 100000,\n            stablecoin: 0\n        };\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name,\n                    config: config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession(sessionData);\n                // Add the session to our local cache\n                const newSession = {\n                    id: response.session.session_uuid,\n                    name: response.session.name,\n                    config,\n                    createdAt: new Date(response.session.created_at).getTime(),\n                    lastModified: new Date(response.session.last_modified).getTime(),\n                    isActive: response.session.is_active,\n                    runtime: response.session.runtime || 0,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 100000,\n                    crypto1Balance: balances.crypto1,\n                    crypto2Balance: balances.crypto2,\n                    stablecoinBalance: balances.stablecoin\n                };\n                this.sessions.set(response.session.session_uuid, newSession);\n                console.log('✅ Session created on backend:', response.session.session_uuid);\n                return response.session.session_uuid;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n                this.useBackend = false;\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: balances.crypto1,\n            crypto2Balance: balances.crypto2,\n            stablecoinBalance: balances.stablecoin,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        return sessionId;\n    }\n    async saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime || 0;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = (session.runtime || 0) + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            console.log(\"\\uD83D\\uDCBE Saving session with complete data:\", {\n                sessionId,\n                targetPriceRows: targetPriceRows.length,\n                orderHistory: orderHistory.length,\n                balances: {\n                    crypto1Balance,\n                    crypto2Balance,\n                    stablecoinBalance\n                },\n                isActive,\n                runtime: currentRuntime\n            });\n            this.sessions.set(sessionId, updatedSession);\n            // If session is active, ensure it's tracked in active sessions\n            if (isActive) {\n                this.addActiveSession(sessionId);\n                console.log(\"✅ Session marked as active and tracked: \".concat(sessionId));\n            } else {\n                this.removeActiveSession(sessionId);\n                console.log(\"⏹️ Session marked as inactive and removed from tracking: \".concat(sessionId));\n            }\n            // Save to backend only if explicitly authenticated\n            if (this.useBackend && \"object\" !== 'undefined') {\n                const token = localStorage.getItem('plutoAuthToken');\n                if (token && token.length > 10) {\n                    try {\n                        // Double-check authentication before making API call\n                        const { sessionApi } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\"));\n                        const sessionData = {\n                            name: updatedSession.name,\n                            config: config,\n                            targetPriceRows: targetPriceRows,\n                            currentMarketPrice: currentMarketPrice,\n                            crypto1Balance: crypto1Balance,\n                            crypto2Balance: crypto2Balance,\n                            stablecoinBalance: stablecoinBalance,\n                            isActive: isActive,\n                            additionalRuntime: currentRuntime\n                        };\n                        await sessionApi.updateSession(sessionId, sessionData);\n                        console.log('✅ Session saved to backend:', sessionId);\n                    } catch (error) {\n                        const errorMessage = error instanceof Error ? error.message : String(error);\n                        console.warn('❌ Backend session save failed:', errorMessage);\n                        // Handle \"Session not found\" error by creating a new session\n                        if (errorMessage.includes('Session not found')) {\n                            console.log('🔄 Session not found in backend, creating new session...');\n                            try {\n                                // Create a new session in the backend with the same data\n                                const newSessionId = await this.createNewSession(updatedSession.name, config, {\n                                    crypto1: crypto1Balance,\n                                    crypto2: crypto2Balance,\n                                    stablecoin: stablecoinBalance\n                                });\n                                // Update the local session ID and session data\n                                this.sessions.set(newSessionId, {\n                                    ...updatedSession,\n                                    id: newSessionId\n                                });\n                                this.sessions.delete(sessionId); // Remove old session\n                                this.setCurrentSession(newSessionId); // Update current session ID\n                                console.log('✅ Created new session to replace missing one:', newSessionId);\n                                return true;\n                            } catch (createError) {\n                                console.error('❌ Failed to create replacement session:', createError);\n                            // Fall through to disable backend mode\n                            }\n                        }\n                        // Disable backend mode on any authentication-related error\n                        if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication') || errorMessage.includes('required')) {\n                            console.log('🔐 Disabling backend mode due to authentication issue');\n                            this.useBackend = false;\n                        }\n                    // Continue with localStorage save as fallback\n                    }\n                } else {\n                    console.log('⚠️ Invalid or missing auth token, skipping backend session save');\n                    this.useBackend = false; // Disable backend mode if no valid token\n                }\n            }\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    async deleteSession(sessionId) {\n        // Delete from backend first if available and user is authenticated\n        if (this.useBackend && \"object\" !== 'undefined') {\n            const token = localStorage.getItem('plutoAuthToken');\n            if (token) {\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.deleteSession(sessionId);\n                    console.log('✅ Session deleted from backend:', sessionId);\n                } catch (error) {\n                    const errorMessage = error instanceof Error ? error.message : String(error);\n                    if (errorMessage.includes('401') || errorMessage.includes('422') || errorMessage.includes('Authentication')) {\n                        console.log('🔐 Authentication issue during session deletion, proceeding with local deletion');\n                    } else {\n                        console.error('❌ Failed to delete session from backend:', error);\n                    }\n                // Continue with local deletion as fallback\n                }\n            } else {\n                console.log('⚠️ No auth token, skipping backend session deletion');\n            }\n        }\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    getCurrentActiveSessions() {\n        // Get all sessions that are currently active across all windows\n        const activeSessionIds = new Set(Array.from(this.activeSessionsAcrossWindows.values()).map((entry)=>entry.sessionId).filter((sessionId)=>sessionId && sessionId !== 'undefined') // Filter out invalid session IDs\n        );\n        // Also include current session if it exists (might not be in activeSessionsAcrossWindows yet)\n        if (this.currentSessionId && this.sessions.has(this.currentSessionId)) {\n            activeSessionIds.add(this.currentSessionId);\n        }\n        console.log('🔍 getCurrentActiveSessions debug:', {\n            activeSessionsAcrossWindows: Array.from(this.activeSessionsAcrossWindows.entries()),\n            activeSessionIds: Array.from(activeSessionIds),\n            allSessions: Array.from(this.sessions.keys()),\n            currentSessionId: this.currentSessionId,\n            windowId: this.windowId\n        });\n        // Get sessions that are either in active tracking OR marked as active in their data\n        const allActiveSessions = Array.from(this.sessions.values()).filter((session)=>{\n            const isInActiveTracking = session.id && activeSessionIds.has(session.id);\n            const isMarkedActive = session.isActive;\n            const isCurrentSession = session.id === this.currentSessionId;\n            console.log(\"\\uD83D\\uDD0D Session \".concat(session.id, \" (\").concat(session.name, \"):\"), {\n                isInActiveTracking,\n                isMarkedActive,\n                isCurrentSession,\n                shouldInclude: isInActiveTracking || isMarkedActive || isCurrentSession\n            });\n            return isInActiveTracking || isMarkedActive || isCurrentSession;\n        }).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: true,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            })).filter((session)=>session.id); // Ensure all returned sessions have valid IDs\n        console.log('🔍 Returning active sessions:', allActiveSessions.length, allActiveSessions.map((s)=>({\n                id: s.id,\n                name: s.name\n            })));\n        return allActiveSessions;\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            // Store in both localStorage (window-specific) and sessionStorage (page refresh persistence)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            sessionStorage.setItem(CURRENT_SESSION_KEY, sessionId);\n            // Mark this session as active when it becomes the current session\n            // Allow multiple sessions to be active simultaneously (for different tabs)\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                // Track this session as active across windows\n                this.addActiveSession(sessionId);\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n                // Trigger storage event to notify admin panel about current session change\n                window.dispatchEvent(new StorageEvent('storage', {\n                    key: 'pluto_current_session',\n                    newValue: sessionId,\n                    storageArea: localStorage\n                }));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Remove session from active tracking and mark as inactive only if no other windows are using it\n        if (this.currentSessionId) {\n            this.removeActiveSession(this.currentSessionId);\n            // Check if any other windows are still using this session\n            const isStillActiveInOtherWindows = Array.from(this.activeSessionsAcrossWindows.values()).some((entry)=>entry.sessionId === this.currentSessionId);\n            if (!isStillActiveInOtherWindows) {\n                const session = this.sessions.get(this.currentSessionId);\n                if (session && session.isActive) {\n                    session.isActive = false;\n                    session.lastModified = Date.now();\n                    this.sessions.set(this.currentSessionId, session);\n                    this.saveSessionsToStorage();\n                    console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive (no other windows using it)\"));\n                }\n            } else {\n                console.log(\"\\uD83D\\uDD04 Session \".concat(this.currentSessionId, \" still active in other windows\"));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            sessionStorage.removeItem(CURRENT_SESSION_KEY);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime = (session.runtime || 0) + additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return (session.runtime || 0) + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime || 0;\n    }\n    // Method to refresh backend connection after login/logout\n    async refreshBackendConnection() {\n        console.log('🔄 Refreshing backend connection...');\n        await this.initializeBackendConnection();\n    }\n    // Method to disable backend mode due to authentication issues\n    disableBackendMode() {\n        console.log('🔐 Disabling backend mode due to authentication issues');\n        this.useBackend = false;\n        this.isInitializing = false;\n    }\n    // Method to handle logout - switch to localStorage mode\n    handleLogout() {\n        console.log('👋 User logged out, switching to localStorage mode');\n        this.useBackend = false;\n        this.sessions.clear();\n        this.currentSessionId = null;\n        this.loadSessionsFromStorage();\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    async updateSessionAlarmSettings(sessionId, alarmSettings) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.alarmSettings = alarmSettings;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        // Save to backend if available\n        if (this.useBackend) {\n            try {\n                const sessionData = {\n                    name: session.name,\n                    config: session.config,\n                    targetPriceRows: session.targetPriceRows,\n                    currentMarketPrice: session.currentMarketPrice,\n                    crypto1Balance: session.crypto1Balance,\n                    crypto2Balance: session.crypto2Balance,\n                    stablecoinBalance: session.stablecoinBalance,\n                    isActive: session.isActive,\n                    alarm_settings: alarmSettings\n                };\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, sessionData);\n                console.log('✅ Session alarm settings saved to backend:', sessionId);\n            } catch (error) {\n                console.error('❌ Failed to save session alarm settings to backend:', error);\n            // Continue with localStorage save as fallback\n            }\n        }\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.isInitializing = false // Prevent multiple initialization attempts\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.activeSessionsAcrossWindows = new Map();\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Enable backend by default - we want persistent sessions\n        this.useBackend = true;\n        // Load sessions and setup cross-window sync\n        this.loadSessionsFromStorage();\n        this.loadActiveSessionsFromStorage();\n        this.setupStorageListener();\n        // Check backend connection and load sessions from backend (with delay to ensure auth is ready)\n        setTimeout(()=>{\n            this.initializeBackendConnection();\n        }, 1000); // 1 second delay to allow auth to be properly initialized\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window \".concat(this.windowId));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

}]);