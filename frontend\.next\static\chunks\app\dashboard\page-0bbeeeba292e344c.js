(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105,737],{659:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(157).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},1462:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(157).A)("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},2085:(e,r,t)=>{"use strict";t.d(r,{F:()=>n});var a=t(2596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=a.$,n=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:o}=r,d=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],a=null==o?void 0:o[e];if(null===r)return null;let l=s(r)||s(a);return n[e][l]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return l(e,d,null==r?void 0:null===(a=r.compoundVariants)||void 0===a?void 0:a.reduce((e,r)=>{let{class:t,className:a,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...c}[r]):({...o,...c})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},3033:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var a=t(5155);t(2115);var s=t(7313),l=t(5695),n=t(1462),o=t(3638);let d=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,a.jsx)(n.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,a.jsx)(o.A,{})}];function c(){let e=(0,l.useRouter)(),r=(0,l.usePathname)(),t="orders";return"/dashboard/history"===r&&(t="history"),(0,a.jsx)(s.tU,{value:t,onValueChange:r=>{let t=d.find(e=>e.value===r);t&&e.push(t.href)},className:"w-full mb-6",children:(0,a.jsx)(s.j7,{className:"grid w-full grid-cols-2 bg-card border-2 border-border",children:d.map(e=>(0,a.jsx)(s.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},3638:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},5791:(e,r,t)=>{Promise.resolve().then(t.bind(t,9366))},6126:(e,r,t)=>{"use strict";t.d(r,{E:()=>o});var a=t(5155);t(2115);var s=t(2085),l=t(9434);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),r),...s})}},6424:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,F:()=>o});var a=t(5155),s=t(2115),l=t(7655),n=t(9434);let o=s.forwardRef((e,r)=>{let{className:t,children:s,...o}=e;return(0,a.jsxs)(l.bL,{ref:r,className:(0,n.cn)("relative overflow-hidden",t),...o,children:[(0,a.jsx)(l.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,a.jsx)(d,{}),(0,a.jsx)(l.OK,{})]})});o.displayName=l.bL.displayName;let d=s.forwardRef((e,r)=>{let{className:t,orientation:s="vertical",...o}=e;return(0,a.jsx)(l.VM,{ref:r,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...o,children:(0,a.jsx)(l.lr,{className:"relative flex-1 rounded-full bg-border"})})});d.displayName=l.VM.displayName},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>i,ZB:()=>d,Zp:()=>n,aR:()=>o});var a=t(5155),s=t(2115),l=t(9434);let n=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",t),...s})});n.displayName="Card";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-4 md:p-6",t),...s})});o.displayName="CardHeader";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,l.cn)("text-xl font-semibold leading-none tracking-tight",t),...s})});d.displayName="CardTitle";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})});c.displayName="CardDescription";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-4 md:p-6 pt-0",t),...s})});i.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-4 md:p-6 pt-0",t),...s})}).displayName="CardFooter"},7313:(e,r,t)=>{"use strict";t.d(r,{Xi:()=>c,av:()=>i,j7:()=>d,tU:()=>o});var a=t(5155),s=t(2115),l=t(64),n=t(9434);let o=l.bL,d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(l.B8,{ref:r,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...s})});d.displayName=l.B8.displayName;let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(l.l9,{ref:r,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...s})});c.displayName=l.l9.displayName;let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(l.UC,{ref:r,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...s})});i.displayName=l.UC.displayName},9366:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var a=t(5155),s=t(2115),l=t(7213),n=t(9434);let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:(0,n.cn)("w-full caption-bottom text-sm",t),...s})})});o.displayName="Table";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("thead",{ref:r,className:(0,n.cn)("[&_tr]:border-b",t),...s})});d.displayName="TableHeader";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tbody",{ref:r,className:(0,n.cn)("[&_tr:last-child]:border-0",t),...s})});c.displayName="TableBody",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tfoot",{ref:r,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...s})}).displayName="TableFooter";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("tr",{ref:r,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...s})});i.displayName="TableRow";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("th",{ref:r,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...s})});u.displayName="TableHead";let p=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("td",{ref:r,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...s})});p.displayName="TableCell",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("caption",{ref:r,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",t),...s})}).displayName="TableCaption";var m=t(6424),f=t(6126);function x(){let{getDisplayOrders:e,config:r,currentMarketPrice:t}=(0,l.U)(),s=e(),x=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==e||isNaN(e))return"-";let a=e.toFixed(r.numDigits);return t&&e>0?"+".concat(a):a},y=e=>null==e||isNaN(e)?"-":"".concat(e.toFixed(2),"%"),b=[{key:"#",label:"#"},{key:"status",label:"Status"},{key:"orderLevel",label:"Level"},{key:"valueLevel",label:"Value"},{key:"crypto2Var",label:"".concat(r.crypto2||"Crypto 2"," Var.")},{key:"crypto1Var",label:"".concat(r.crypto1||"Crypto 1"," Var.")},{key:"targetPrice",label:"Target Price"},{key:"percentFromActualPrice",label:"% from Actual"},{key:"incomeCrypto1",label:"Income ".concat(r.crypto1||"Crypto 1")},{key:"incomeCrypto2",label:"Income ".concat(r.crypto2||"Crypto 2")},{key:"originalCostCrypto2",label:"Original Cost ".concat(r.crypto2||"Crypto 2")}];return(0,a.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,a.jsxs)(m.F,{className:"w-full whitespace-nowrap",children:[(0,a.jsxs)(o,{className:"min-w-full",children:[(0,a.jsx)(d,{children:(0,a.jsx)(i,{className:"bg-card hover:bg-card",children:b.map(e=>(0,a.jsx)(u,{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm",children:e.label},e.key))})}),(0,a.jsx)(c,{children:0===s.length?(0,a.jsx)(i,{children:(0,a.jsx)(p,{colSpan:b.length,className:"h-24 text-center text-muted-foreground",children:'No target prices set. Use "Set Target Prices" in the sidebar.'})}):s.map(e=>(0,a.jsxs)(i,{className:"hover:bg-card/80",children:[(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:e.counter}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:(0,a.jsx)(f.E,{variant:"Full"===e.status?"default":"secondary",className:(0,n.cn)("Full"===e.status?"bg-green-600 text-white":"bg-yellow-500 text-black","font-bold"),children:e.status})}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:e.orderLevel}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:x(e.valueLevel)}),(0,a.jsx)(p,{className:(0,n.cn)("px-3 py-2 text-xs",e.crypto2Var&&e.crypto2Var<0?"text-destructive":"text-green-400"),children:x(e.crypto2Var,!0)}),(0,a.jsx)(p,{className:(0,n.cn)("px-3 py-2 text-xs",e.crypto1Var&&e.crypto1Var<0?"text-destructive":"text-green-400"),children:x(e.crypto1Var,!0)}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs font-semibold text-primary",children:x(e.targetPrice)}),(0,a.jsx)(p,{className:(0,n.cn)("px-3 py-2 text-xs",e.percentFromActualPrice<0?"text-destructive":"text-green-400"),children:y(e.percentFromActualPrice)}),(0,a.jsx)(p,{className:(0,n.cn)("px-3 py-2 text-xs",e.incomeCrypto1&&e.incomeCrypto1<0?"text-destructive":"text-green-400"),children:x(e.incomeCrypto1)}),(0,a.jsx)(p,{className:(0,n.cn)("px-3 py-2 text-xs",e.incomeCrypto2&&e.incomeCrypto2<0?"text-destructive":"text-green-400"),children:x(e.incomeCrypto2)}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:x(e.originalCostCrypto2)})]},e.id))})]}),(0,a.jsx)(m.$,{orientation:"horizontal"})]})})}var y=t(3033),b=t(6695),h=t(659);function v(){let e=(0,l.U)();if(!e)return(0,a.jsx)("div",{className:"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md",children:(0,a.jsx)("div",{className:"flex items-center justify-center gap-3",children:(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Loading market data..."})})});let{config:r,currentMarketPrice:t}=e,s=(null==r?void 0:r.crypto1)&&(null==r?void 0:r.crypto2),n=s?"".concat(r.crypto1,"/").concat(r.crypto2):"Crypto 1/Crypto 2",o=s&&t&&t>0?(e=>{if(null==e||isNaN(e)||e<=0)return"0.00";try{return e.toFixed((null==r?void 0:r.numDigits)||2)}catch(r){return console.warn("Error formatting price:",r,"Price:",e),"0.00"}})(t):"0.00";return(0,a.jsx)("div",{className:"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-green-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Current Market Price"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-lg font-semibold text-foreground",children:[n,":"]}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-primary",children:["$",o]})]})]})})}var g=t(4553),N=t(7481);function j(){let{config:e,saveCurrentSession:r,targetPriceRows:t,orderHistory:n}=(0,l.U)(),{toast:o}=(0,N.dj)(),[d,c]=(0,s.useState)(""),i=g.SessionManager.getInstance();(0,s.useEffect)(()=>{(()=>{let r=i.getCurrentSessionId();if(r){let e=i.loadSession(r);if(e){c(e.name);return}}e.crypto1&&e.crypto2?c("".concat(e.crypto1,"/").concat(e.crypto2," ").concat(e.tradingMode||"SimpleSpot")):c("Crypto 1/Crypto 2 = 0")})()},[e.crypto1,e.crypto2,e.tradingMode,i,t.length,n.length]);let u=d||(e.crypto1&&e.crypto2?"".concat(e.crypto1,"/").concat(e.crypto2," ").concat(e.tradingMode||"SimpleSpot"):"Crypto 1/Crypto 2 = 0");return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(y.A,{}),(0,a.jsxs)(b.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(b.aR,{children:[(0,a.jsxs)(b.ZB,{className:"text-2xl font-bold text-primary",children:["Active Orders (",u,")"]}),(0,a.jsx)(b.BT,{children:"Current state of your target price levels. Prices update in real-time."})]}),(0,a.jsxs)(b.Wu,{children:[(0,a.jsx)(v,{}),(0,a.jsx)(x,{})]})]})]})}},9367:(e,r,t)=>{"use strict";function a(e,[r,t]){return Math.min(t,Math.max(r,e))}t.d(r,{q:()=>a})},9737:(e,r,t)=>{"use strict";let a;t.d(r,{A:()=>o});let s={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},l=new Uint8Array(16),n=[];for(let e=0;e<256;++e)n.push((e+256).toString(16).slice(1));let o=function(e,r,t){if(s.randomUUID&&!r&&!e)return s.randomUUID();let o=(e=e||{}).random||(e.rng||function(){if(!a&&!(a="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return a(l)})();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,r){t=t||0;for(let e=0;e<16;++e)r[t+e]=o[e];return r}return function(e,r=0){return n[e[r+0]]+n[e[r+1]]+n[e[r+2]]+n[e[r+3]]+"-"+n[e[r+4]]+n[e[r+5]]+"-"+n[e[r+6]]+n[e[r+7]]+"-"+n[e[r+8]]+n[e[r+9]]+"-"+n[e[r+10]]+n[e[r+11]]+n[e[r+12]]+n[e[r+13]]+n[e[r+14]]+n[e[r+15]]}(o)}}},e=>{var r=r=>e(e.s=r);e.O(0,[1,64,655,553,318,441,684,358],()=>r(5791)),_N_E=e.O()}]);