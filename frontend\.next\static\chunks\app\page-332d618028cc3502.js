(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{157:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:l="",children:u,iconNode:g,...d}=e;return(0,o.createElement)("svg",{ref:t,...s,width:n,height:n,stroke:r,strokeWidth:c?24*Number(i)/Number(n):i,className:a("lucide",l),...d},[...g.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),c=(e,t)=>{let r=(0,o.forwardRef)((r,s)=>{let{className:c,...l}=r;return(0,o.createElement)(i,{ref:s,iconNode:t,className:a("lucide-".concat(n(e)),c),...l})});return r.displayName="".concat(e),r}},172:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(157).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},283:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,AuthProvider:()=>l});var o=r(5155),n=r(2115),a=r(5695),s=r(172),i=r(5731);let c=(0,n.createContext)(void 0),l=e=>{let{children:t}=e,[l,u]=(0,n.useState)(!1),[g,d]=(0,n.useState)(!0),h=(0,a.useRouter)(),m=(0,a.usePathname)();(0,n.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");"true"===e&&t&&u(!0),d(!1)},[]),(0,n.useEffect)(()=>{g||l||"/login"===m?!g&&l&&"/login"===m&&h.push("/dashboard"):h.push("/login")},[l,g,m,h]);let y=async(e,t)=>{d(!0);try{if(await i.ZQ.login(e,t)){u(!0);try{let{SessionManager:e}=await Promise.all([r.e(553),r.e(737)]).then(r.bind(r,4553));await e.getInstance().refreshBackendConnection()}catch(e){console.error("Failed to refresh session manager:",e)}return h.push("/dashboard"),!0}return u(!1),!1}catch(e){return console.error("Login failed:",e),u(!1),!1}finally{d(!1)}},f=async()=>{try{await i.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{try{let{SessionManager:e}=await Promise.all([r.e(553),r.e(737)]).then(r.bind(r,4553));e.getInstance().handleLogout()}catch(e){console.error("Failed to handle session manager logout:",e)}u(!1),h.push("/login")}};return!g||(null==m?void 0:m.startsWith("/_next/static/"))?l||"/login"===m||(null==m?void 0:m.startsWith("/_next/static/"))?(0,o.jsx)(c.Provider,{value:{isAuthenticated:l,login:y,logout:f,isLoading:g},children:t}):(0,o.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,o.jsx)(s.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,o.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]}):(0,o.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,o.jsx)(s.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,o.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]})},u=()=>{let e=(0,n.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var o=r(5155),n=r(2115),a=r(5695),s=r(283),i=r(172);function c(){let e=(0,a.useRouter)(),{isAuthenticated:t,isLoading:r}=(0,s.A)();return(0,n.useEffect)(()=>{r||(t?e.replace("/dashboard"):e.replace("/login"))},[t,r,e]),(0,o.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background",children:[(0,o.jsx)(i.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,o.jsx)("p",{className:"ml-4 text-xl",children:"Initializing Pluto..."})]})}},5695:(e,t,r)=>{"use strict";var o=r(8999);r.o(o,"usePathname")&&r.d(t,{usePathname:function(){return o.usePathname}}),r.o(o,"useRouter")&&r.d(t,{useRouter:function(){return o.useRouter}})},5731:(e,t,r)=>{"use strict";r.d(t,{ZQ:()=>a,oc:()=>s,sessionApi:()=>i});let o="http://localhost:5000";async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(o).concat(e),n=localStorage.getItem("plutoAuthToken"),a=r.includes("/sessions")||r.includes("/trading")||r.includes("/admin");if(a&&!n)throw console.warn("\uD83D\uDD10 Blocked API call to ".concat(r," - no authentication token available")),Error("Authentication required - no token available");console.log("\uD83D\uDD10 Auth token check:",{hasToken:!!n,tokenLength:n?n.length:0,tokenPreview:n?"".concat(n.substring(0,20),"..."):"No token",isAuthenticatedEndpoint:a});let s={"Content-Type":"application/json",...n?{Authorization:"Bearer ".concat(n)}:{},...t.headers};try{let e;let o=new AbortController,n=setTimeout(()=>o.abort(),1e4),a=await fetch(r,{...t,headers:s,signal:o.signal}).finally(()=>clearTimeout(n));if(401===a.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),window.location.href="/login",Error("Authentication expired. Please login again.");let i="",c=a.headers.get("content-type");if(c&&c.includes("application/json")){i=await a.text();try{e=JSON.parse(i)}catch(t){e={message:i}}}else{i=await a.text();try{e=JSON.parse(i)}catch(t){e={message:i}}}if(!a.ok){let r={status:a.status,statusText:a.statusText,url:a.url,method:t.method||"GET",responseData:e,responseText:i.substring(0,500)};throw 401!==a.status&&422!==a.status&&e&&(e.error||e.message||Object.keys(e).length>0)?console.error("\uD83D\uDEA8 API Error Details:",r):console.warn("⚠️ API ".concat(a.status," Error:"),a.url),Error(e.error||e.message||"API error: ".concat(a.status))}return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",e),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw console.error("Request timeout:",e),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",e),e}}console.log("API Base URL:",o);let a={login:async(e,t)=>{try{let r=await c(async()=>await n("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(r&&r.access_token)return localStorage.setItem("plutoAuthToken",r.access_token),localStorage.setItem("plutoAuth","true"),r.user&&localStorage.setItem("plutoUser",JSON.stringify(r.user)),!0;return!1}catch(e){return console.error("Login API error:",e),!1}},register:async(e,t,r)=>c(async()=>await n("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:r})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},s={getConfig:async e=>n(e?"/trading/config/".concat(e):"/trading/config"),saveConfig:async e=>n("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>n("/trading/config/".concat(e),{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>n("/trading/bot/start/".concat(e),{method:"POST"}),stopBot:async e=>n("/trading/bot/stop/".concat(e),{method:"POST"}),getBotStatus:async e=>n("/trading/bot/status/".concat(e)),getTradeHistory:async e=>n("/trading/history".concat(e?"?configId=".concat(e):"")),getBalances:async()=>n("/trading/balances"),getMarketPrice:async e=>n("/trading/market-data/".concat(e)),getTradingPairs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return n("/trading/exchange/trading-pairs?exchange=".concat(e))},getCryptocurrencies:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return n("/trading/exchange/cryptocurrencies?exchange=".concat(e))}},i={getAllSessions:async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return n("/sessions/?include_inactive=".concat(e))},createSession:async e=>n("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>n("/sessions/".concat(e)),updateSession:async(e,t)=>n("/sessions/".concat(e),{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>n("/sessions/".concat(e),{method:"DELETE"}),activateSession:async e=>n("/sessions/".concat(e,"/activate"),{method:"POST"}),getSessionHistory:async e=>n("/sessions/".concat(e,"/history")),getActiveSession:async()=>n("/sessions/active")},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=0,o=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&r<t){let e=500*Math.pow(2,r);return console.log("Retrying after ".concat(e,"ms (attempt ").concat(r+1,"/").concat(t,")...")),r++,await new Promise(t=>setTimeout(t,e)),o()}throw e}};return o()}},9225:(e,t,r)=>{Promise.resolve().then(r.bind(r,3792))}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(9225)),_N_E=e.O()}]);