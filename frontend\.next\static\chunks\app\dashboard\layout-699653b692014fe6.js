(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,AuthProvider:()=>o});var a=r(5155),s=r(2115),l=r(5695),n=r(172),i=r(5731);let c=(0,s.createContext)(void 0),o=e=>{let{children:t}=e,[o,d]=(0,s.useState)(!1),[p,m]=(0,s.useState)(!0),h=(0,l.useRouter)(),u=(0,l.usePathname)();(0,s.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");"true"===e&&t&&d(!0),m(!1)},[]),(0,s.useEffect)(()=>{p||o||"/login"===u?!p&&o&&"/login"===u&&h.push("/dashboard"):h.push("/login")},[o,p,u,h]);let x=async(e,t)=>{m(!0);try{if(await i.ZQ.login(e,t)){d(!0);try{let{SessionManager:e}=await Promise.all([r.e(553),r.e(737)]).then(r.bind(r,4553));await e.getInstance().refreshBackendConnection()}catch(e){console.error("Failed to refresh session manager:",e)}return h.push("/dashboard"),!0}return d(!1),!1}catch(e){return console.error("Login failed:",e),d(!1),!1}finally{m(!1)}},g=async()=>{try{await i.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{try{let{SessionManager:e}=await Promise.all([r.e(553),r.e(737)]).then(r.bind(r,4553));e.getInstance().handleLogout()}catch(e){console.error("Failed to handle session manager logout:",e)}d(!1),h.push("/login")}};return!p||(null==u?void 0:u.startsWith("/_next/static/"))?o||"/login"===u||(null==u?void 0:u.startsWith("/_next/static/"))?(0,a.jsx)(c.Provider,{value:{isAuthenticated:o,login:x,logout:g,isLoading:p},children:t}):(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,a.jsx)(n.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,a.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,a.jsx)(n.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,a.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]})},d=()=>{let e=(0,s.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2346:(e,t,r)=>{"use strict";r.d(t,{w:()=>i});var a=r(5155),s=r(2115),l=r(7489),n=r(9434);let i=s.forwardRef((e,t)=>{let{className:r,orientation:s="horizontal",decorative:i=!0,...c}=e;return(0,a.jsx)(l.b,{ref:t,decorative:i,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",r),...c})});i.displayName=l.b.displayName},2972:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>H});var a=r(5155),s=r(2115),l=r(6874),n=r.n(l),i=r(5695),c=r(9435),o=r(285),d=r(283),p=r(8186),m=r(7082),h=r(9532),u=r(3349),x=r(6126),g=r(6695),b=r(8639),f=r(2087),j=r(7648);function v(e){let{className:t=""}=e,[r,l]=(0,s.useState)(navigator.onLine),[n,i]=(0,s.useState)(new Date);return(0,s.useEffect)(()=>{let e=()=>l(!0),t=()=>l(!1);window.addEventListener("online",e),window.addEventListener("offline",t);let r=setInterval(()=>{i(new Date)},1e3);return()=>{window.removeEventListener("online",e),window.removeEventListener("offline",t),clearInterval(r)}},[]),(0,a.jsxs)("div",{className:"flex items-center gap-2 ".concat(t),children:[(0,a.jsxs)(x.E,{variant:r?"default":"destructive",className:"flex items-center gap-1 ".concat(r?"bg-green-600 hover:bg-green-600/90 text-white":""),children:[r?(0,a.jsx)(b.A,{className:"h-3 w-3"}):(0,a.jsx)(f.A,{className:"h-3 w-3"}),r?"Online":"Offline"]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,a.jsx)(j.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:n.toLocaleTimeString()})]})]})}function y(){let{logout:e}=(0,d.A)();(0,i.useRouter)();let t=(0,i.usePathname)(),r=[{href:"/dashboard",label:"Home",icon:(0,a.jsx)(p.A,{})},{href:"/admin",label:"Admin Panel",icon:(0,a.jsx)(m.A,{})}];return(0,a.jsxs)("header",{className:"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(c.A,{useFullName:!1}),(0,a.jsx)(v,{})]}),(0,a.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[r.map(e=>(0,a.jsx)(o.$,{variant:t===e.href?"default":"ghost",size:"sm",asChild:!0,className:"".concat(t===e.href?"btn-neo":"hover:bg-accent/50"),children:(0,a.jsxs)(n(),{href:e.href,className:"flex items-center gap-2",children:[e.icon,(0,a.jsx)("span",{className:"hidden sm:inline",children:e.label})]})},e.label)),(0,a.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:()=>{window.open("/dashboard?newSession=true","_blank")},className:"hover:bg-accent/50 flex items-center gap-2",title:"Open a new independent trading session in a new tab",children:[(0,a.jsx)(h.A,{}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"New Session"})]}),(0,a.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:()=>{e()},className:"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2",children:[(0,a.jsx)(u.A,{}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})}var N=r(7213),S=r(2523),w=r(5057),C=r(6981),A=r(518),T=r(9434);let E=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(C.bL,{ref:t,className:(0,T.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",r),...s,children:(0,a.jsx)(C.C1,{className:(0,T.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(A.A,{className:"h-4 w-4"})})})});E.displayName=C.bL.displayName;var P=r(9409),O=r(6424),F=r(2346),k=r(4165);let D=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,T.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});D.displayName="Textarea";var L=r(7313),B=r(7481);function I(e){var t;let r,{isOpen:l,onClose:n,onSetTargetPrices:i}=e,[c,d]=(0,s.useState)("manual"),[p,m]=(0,s.useState)(""),{toast:h}=(0,B.dj)();try{r=(0,N.U)()}catch(e){console.warn("Trading context not available:",e),r=null}let[u,x]=(0,s.useState)("8"),[g,b]=(0,s.useState)("5"),[f,j]=(0,s.useState)("even"),v=(null==r?void 0:r.currentMarketPrice)||1e5,y=(null==r?void 0:null===(t=r.config)||void 0===t?void 0:t.slippagePercent)||.2,S=()=>{let e=parseInt(u),t=parseFloat(g);if(!e||e<2||e>20||!t||t<=0)return[];let r=[],a=v*(1-t/100),s=v*(1+t/100);if("even"===f)for(let t=0;t<e;t++){let l=a+t/(e-1)*(s-a);r.push(Math.round(l))}else if("fibonacci"===f){let t=[0,.236,.382,.5,.618,.764,.854,.927,1];for(let l=0;l<e;l++){let n=a+(s-a)*(t[Math.min(l,t.length-1)]||l/(e-1));r.push(Math.round(n))}}else if("exponential"===f)for(let t=0;t<e;t++){let l=a+(s-a)*Math.pow(t/(e-1),1.5);r.push(Math.round(l))}let l=3*y/100*v,n=r.sort((e,t)=>e-t),i=[];for(let e=0;e<n.length;e++){let t=n[e];if(i.length>0){let e=i[i.length-1];t-e<l&&(t=e+l)}i.push(Math.round(t))}return i},C=()=>{let e=p.split("\n").filter(e=>""!==e.trim()).map(e=>parseFloat(e.trim())).filter(e=>!isNaN(e)&&e>0).sort((e,t)=>e-t);if(e.length<2)return{hasOverlap:!1,message:""};let t=y/100*v;for(let r=0;r<e.length-1;r++)if(e[r]+t>=e[r+1]-t){let a=2*t,s=e[r+1]-e[r];return{hasOverlap:!0,message:"Overlap detected between ".concat(e[r]," and ").concat(e[r+1],". Minimum gap needed: ").concat(a.toFixed(0),", actual gap: ").concat(s.toFixed(0))}}return{hasOverlap:!1,message:"No slippage zone overlaps detected ✓"}},A=C();return(0,a.jsx)(k.lG,{open:l,onOpenChange:n,children:(0,a.jsxs)(k.Cf,{className:"sm:max-w-2xl bg-card border-2 border-border",children:[(0,a.jsxs)(k.c7,{children:[(0,a.jsx)(k.L3,{className:"text-primary",children:"Set Target Prices"}),(0,a.jsx)(k.rr,{children:"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."})]}),(0,a.jsxs)(L.tU,{value:c,onValueChange:d,className:"w-full",children:[(0,a.jsxs)(L.j7,{className:"grid w-full grid-cols-2",children:[(0,a.jsx)(L.Xi,{value:"manual",children:"Manual Entry"}),(0,a.jsx)(L.Xi,{value:"automatic",children:"Automatic Generation"})]}),(0,a.jsx)(L.av,{value:"manual",className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(w.J,{htmlFor:"target-prices-input",className:"text-left",children:"Target Prices"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored."}),(0,a.jsx)(D,{id:"target-prices-input",value:p,onChange:e=>m(e.target.value),placeholder:"50000 50500 49800",className:"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"}),A.message&&(0,a.jsx)("p",{className:"text-sm ".concat(A.hasOverlap?"text-red-500":"text-green-500"),children:A.message})]})}),(0,a.jsxs)(L.av,{value:"automatic",className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{htmlFor:"targetCount",children:"Number of Targets"}),(0,a.jsxs)(P.l6,{value:u,onValueChange:x,children:[(0,a.jsx)(P.bq,{children:(0,a.jsx)(P.yv,{})}),(0,a.jsx)(P.gC,{children:[4,6,8,10,12,15,20].map(e=>(0,a.jsxs)(P.eb,{value:e.toString(),children:[e," targets"]},e))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{htmlFor:"priceRange",children:"Price Range (%)"}),(0,a.jsxs)(P.l6,{value:g,onValueChange:b,children:[(0,a.jsx)(P.bq,{children:(0,a.jsx)(P.yv,{})}),(0,a.jsx)(P.gC,{children:[2,2.5,3,3.5,4,4.5,5,6,7,8,10].map(e=>(0,a.jsxs)(P.eb,{value:e.toString(),children:["\xb1",e,"%"]},e))})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{htmlFor:"distribution",children:"Distribution Pattern"}),(0,a.jsxs)(P.l6,{value:f,onValueChange:j,children:[(0,a.jsx)(P.bq,{children:(0,a.jsx)(P.yv,{})}),(0,a.jsxs)(P.gC,{children:[(0,a.jsx)(P.eb,{value:"even",children:"Even Distribution"}),(0,a.jsx)(P.eb,{value:"fibonacci",children:"Fibonacci (More near current price)"}),(0,a.jsx)(P.eb,{value:"exponential",children:"Exponential (Wider spread)"})]})]})]}),(0,a.jsx)("div",{className:"bg-muted p-3 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Current Market Price:"})," $",v.toLocaleString(),(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"Slippage:"})," \xb1",y,"% ($",(v*y/100).toFixed(0),")",(0,a.jsx)("br",{}),(0,a.jsx)("strong",{children:"Range:"})," $",(v*(1-parseFloat(g)/100)).toLocaleString()," - $",(v*(1+parseFloat(g)/100)).toLocaleString()]})}),(0,a.jsxs)(o.$,{onClick:()=>{m(S().join("\n"))},className:"w-full btn-neo",children:["Generate ",u," Target Prices"]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(w.J,{children:"Generated Prices (Preview)"}),(0,a.jsx)(D,{value:p,onChange:e=>m(e.target.value),className:"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono",placeholder:"Click 'Generate' to create automatic target prices..."}),A.message&&(0,a.jsx)("p",{className:"text-sm ".concat(A.hasOverlap?"text-red-500":"text-green-500"),children:A.message})]})]})]}),(0,a.jsxs)(k.Es,{children:[(0,a.jsx)(k.HM,{asChild:!0,children:(0,a.jsx)(o.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,a.jsx)(o.$,{type:"button",onClick:()=>{let e=p.split("\n").map(e=>e.trim()).filter(e=>""!==e),t=e.map(e=>parseFloat(e)).filter(e=>!isNaN(e)&&e>0);if(0===t.length&&e.length>0){h({title:"Invalid Input",description:"No valid prices found. Please enter numbers, one per line.",variant:"destructive"});return}let r=C();if(r.hasOverlap){h({title:"Slippage Zone Overlap",description:r.message,variant:"destructive"});return}i(t),h({title:"Target Prices Updated",description:"".concat(t.length," target prices have been set.")}),m(""),n()},disabled:A.hasOverlap,className:"btn-neo",children:"Save Prices"})]})]})})}var U=r(9348),_=r(5318),M=r(1133);function R(e){let{label:t,value:r,allowedCryptos:l,onValidCrypto:n,placeholder:i="Enter crypto symbol",description:c,className:d}=e,[p,m]=(0,s.useState)(""),[h,u]=(0,s.useState)("idle"),[x,g]=(0,s.useState)(""),[b,f]=(0,s.useState)(!1);(0,s.useEffect)(()=>{r&&r!==p?(m(r),u("valid"),f(!0)):r||f(!1)},[r]);let j=()=>{let e=p.toUpperCase().trim();if(!e){u("invalid"),g("Please enter a crypto symbol");return}if(!l||!Array.isArray(l)){u("invalid"),g("No allowed cryptocurrencies configured");return}l.includes(e)?(u("valid"),g(""),f(!0),n(e)):(u("invalid"),g("".concat(e," is not available. Allowed: ").concat(l.join(", "))))};return(0,a.jsxs)("div",{className:(0,T.cn)("space-y-2",d),children:[(0,a.jsx)(w.J,{htmlFor:"crypto-input-".concat(t),children:t}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(S.p,{id:"crypto-input-".concat(t),value:p||r||"",onChange:e=>{m(e.target.value),u("idle"),g("")},onKeyPress:e=>{"Enter"===e.key&&j()},placeholder:i,className:(0,T.cn)("pr-8",(()=>{switch(h){case"valid":return"border-green-500";case"invalid":return"border-red-500";default:return""}})())}),"idle"!==h&&(0,a.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(()=>{switch(h){case"valid":return(0,a.jsx)(A.A,{className:"h-4 w-4 text-green-500"});case"invalid":return(0,a.jsx)(_.A,{className:"h-4 w-4 text-red-500"});default:return null}})()})]}),(0,a.jsx)(o.$,{onClick:j,variant:"outline",className:"btn-neo",disabled:!p.trim(),children:"Check"})]}),r&&b&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsxs)("span",{className:"text-green-600 font-medium",children:["Selected: ",r]})]}),"invalid"===h&&x&&(0,a.jsxs)("div",{className:"flex items-start space-x-2 text-sm text-red-600",children:[(0,a.jsx)(M.A,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:x})]}),c&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:c}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[l&&Array.isArray(l)?l.length:0," cryptocurrencies available"]})]})}var G=r(8803),$=r(172),J=r(955),V=r(8531);let W=["USDT","USDC","BTC"],Z=["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],z=["USDC","DAI","TUSD","FDUSD","USDT","EUR"],Y=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];function q(){let e;try{e=(0,N.U)()}catch(e){return console.error("Trading context not available:",e),(0,a.jsx)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)("p",{className:"text-red-500",children:"Trading context not available. Please refresh the page."})})})}let{config:t,dispatch:r,botSystemStatus:l,appSettings:n,targetPriceRows:i,setTargetPrices:c}=e,d="Running"===l,p="WarmingUp"===l,{toast:m}=(0,B.dj)(),[h,u]=(0,s.useState)(!1),x=e=>{let t;let{name:a,value:s,type:l,checked:n}=e.target;if("checkbox"===l)t=n;else if("number"===l){if(""===s||null==s)t=0;else{let e=parseFloat(s);t=isNaN(e)?0:e}}else t=s;r({type:"SET_CONFIG",payload:{[a]:t}})},b=(e,a)=>{if(r({type:"SET_CONFIG",payload:{[e]:a}}),"crypto1"===e){let e=U.vA[a]||W||["USDT","USDC","BTC"];t.crypto2&&Array.isArray(e)&&e.includes(t.crypto2)||r({type:"SET_CONFIG",payload:{crypto2:e[0]||"USDT"}})}},f=(e,t)=>{let a=parseFloat(t);isNaN(a)&&(a=0),a<0&&(a=0),a>100&&(a=100),"incomeSplitCrypto1Percent"===e?r({type:"SET_CONFIG",payload:{incomeSplitCrypto1Percent:a,incomeSplitCrypto2Percent:100-a}}):r({type:"SET_CONFIG",payload:{incomeSplitCrypto2Percent:a,incomeSplitCrypto1Percent:100-a}})},j=U.hg||[];return"SimpleSpot"===t.tradingMode?U.vA[t.crypto1]:(U.hg||[]).filter(e=>e!==t.crypto1),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_CRYPTOS length:",null===U.hg||void 0===U.hg?void 0:U.hg.length),console.log("\uD83D\uDD0D DEBUG: crypto1Options length:",j.length),console.log("\uD83D\uDD0D DEBUG: First 20 cryptos:",null===U.hg||void 0===U.hg?void 0:U.hg.slice(0,20)),console.log("\uD83D\uDD0D DEBUG: LOCAL_ALLOWED_CRYPTO1:",Z),console.log("\uD83D\uDD0D DEBUG: LOCAL_ALLOWED_CRYPTO2:",z),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_STABLECOINS:",U.Ql),(0,a.jsxs)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsx)("h2",{className:"text-2xl font-bold text-sidebar-primary",children:"Trading Configuration"})}),(0,a.jsx)(O.F,{className:"flex-1 pr-2",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Mode"})}),(0,a.jsxs)(g.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(E,{id:"enableStablecoinSwap",checked:"StablecoinSwap"===t.tradingMode,onCheckedChange:e=>{let a;let s=e?"StablecoinSwap":"SimpleSpot";a="StablecoinSwap"===s?Z.filter(e=>e!==t.crypto1)[0]:z[0],r({type:"SET_CONFIG",payload:{tradingMode:s,crypto2:a}})}}),(0,a.jsx)(w.J,{htmlFor:"enableStablecoinSwap",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Enable Stablecoin Swap Mode"})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Current mode: ","SimpleSpot"===t.tradingMode?"Simple Spot":"Stablecoin Swap"]}),"StablecoinSwap"===t.tradingMode&&(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin"}),(0,a.jsxs)(P.l6,{name:"preferredStablecoin",value:t.preferredStablecoin,onValueChange:e=>b("preferredStablecoin",e),children:[(0,a.jsx)(P.bq,{id:"preferredStablecoin",children:(0,a.jsx)(P.yv,{placeholder:"Select stablecoin"})}),(0,a.jsx)(P.gC,{className:"max-h-[300px] overflow-y-auto",children:Y.map(e=>(0,a.jsx)(P.eb,{value:e,children:e},e))})]})]})]})]}),(0,a.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Pair"})}),(0,a.jsxs)(g.Wu,{className:"space-y-4",children:[(0,a.jsx)(R,{label:"Crypto 1 (Base)",value:t.crypto1,allowedCryptos:Z,onValidCrypto:e=>{if(r({type:"SET_CONFIG",payload:{crypto1:e}}),"StablecoinSwap"===t.tradingMode){let a=Z.filter(t=>t!==e);a.includes(t.crypto2)&&t.crypto2!==e||r({type:"SET_CONFIG",payload:{crypto2:a[0]}})}else z.includes(t.crypto2)||r({type:"SET_CONFIG",payload:{crypto2:z[0]}})},placeholder:"e.g., BTC, ETH, SOL",description:"Enter the base cryptocurrency symbol"}),(0,a.jsx)(R,{label:"StablecoinSwap"===t.tradingMode?"Crypto 2":"Crypto 2 (Stablecoin)",value:t.crypto2,allowedCryptos:"StablecoinSwap"===t.tradingMode?Z.filter(e=>e!==t.crypto1):z,onValidCrypto:e=>{("StablecoinSwap"!==t.tradingMode||e!==t.crypto1)&&r({type:"SET_CONFIG",payload:{crypto2:e}})},placeholder:"StablecoinSwap"===t.tradingMode?"e.g., BTC, ETH, SOL":"e.g., USDT, USDC, DAI",description:"StablecoinSwap"===t.tradingMode?"Enter the second cryptocurrency symbol":"Enter the quote/stablecoin symbol"})]})]}),(0,a.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Parameters"})}),(0,a.jsxs)(g.Wu,{className:"space-y-3",children:[[{name:"baseBid",label:"Base Bid (Crypto 2)",type:"number",step:"0.01"},{name:"multiplier",label:"Multiplier",type:"number",step:"0.001"},{name:"numDigits",label:"Display Digits",type:"number",step:"1"},{name:"slippagePercent",label:"Slippage %",type:"number",step:"0.01"}].map(e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{htmlFor:e.name,children:e.label}),(0,a.jsx)(S.p,{id:e.name,name:e.name,type:e.type,value:t[e.name]||"",onChange:x,step:e.step,min:"0"})]},e.name)),(0,a.jsxs)("div",{children:[(0,a.jsx)(w.J,{children:"Couple Income % Split (must sum to 100)"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(w.J,{htmlFor:"incomeSplitCrypto1Percent",className:"text-xs",children:[t.crypto1||"Crypto 1","%"]}),(0,a.jsx)(S.p,{id:"incomeSplitCrypto1Percent",type:"number",value:t.incomeSplitCrypto1Percent||"",onChange:e=>f("incomeSplitCrypto1Percent",e.target.value),min:"0",max:"100"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(w.J,{htmlFor:"incomeSplitCrypto2Percent",className:"text-xs",children:[t.crypto2||"Crypto 2","%"]}),(0,a.jsx)(S.p,{id:"incomeSplitCrypto2Percent",type:"number",value:t.incomeSplitCrypto2Percent||"",onChange:e=>f("incomeSplitCrypto2Percent",e.target.value),min:"0",max:"100"})]})]})]})]})]})]})}),(0,a.jsxs)("div",{className:"flex-shrink-0 mt-4",children:[(0,a.jsx)(F.w,{className:"mb-4 bg-sidebar-border"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:(0,T.cn)("text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",d?"bg-green-600 text-primary-foreground":"bg-muted text-muted-foreground"),children:[d?(0,a.jsx)(G.A,{className:"h-4 w-4"}):p?(0,a.jsx)($.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(J.A,{className:"h-4 w-4"}),"Bot Status: ",d?"Running":p?"Warming Up":"Stopped"]}),(0,a.jsx)(o.$,{onClick:()=>u(!0),className:"w-full btn-outline-neo",children:"Set Target Prices"}),(0,a.jsxs)(o.$,{onClick:()=>{if(d)r({type:"SYSTEM_STOP_BOT"});else{if(!t.crypto1||!t.crypto2){m({title:"Cannot Start Bot",description:"Please select both Crypto 1 and Crypto 2 before starting the bot.",variant:"destructive",duration:3e3});return}if(0===i.length){m({title:"Cannot Start Bot",description:"Please set target prices before starting the bot.",variant:"destructive",duration:3e3});return}r({type:"SYSTEM_START_BOT_INITIATE"})}},className:(0,T.cn)("w-full btn-neo",d||p?"bg-destructive hover:bg-destructive/90":"bg-green-600 hover:bg-green-600/90"),disabled:p,children:[d?(0,a.jsx)(G.A,{className:"h-4 w-4"}):p?(0,a.jsx)($.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(J.A,{className:"h-4 w-4"}),d?"Stop Bot":p?"Warming Up...":"Start Bot"]}),(0,a.jsxs)(o.$,{onClick:()=>{r({type:"SYSTEM_RESET_BOT"}),m({title:"Bot Reset",description:"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.",duration:4e3})},variant:"outline",className:"w-full btn-outline-neo",disabled:p,children:[(0,a.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Reset Bot"]})]})]}),(0,a.jsx)(I,{isOpen:h,onClose:()=>u(!1),onSetTargetPrices:c})]})}function H(e){let{children:t}=e,{isAuthenticated:r,isLoading:s}=(0,d.A)(),l=(0,i.useRouter)();return s?(0,a.jsx)("div",{className:"flex items-center justify-center h-screen bg-background",children:(0,a.jsx)($.A,{className:"h-12 w-12 animate-spin text-primary"})}):r?(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-background text-foreground",children:[(0,a.jsx)(y,{}),(0,a.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,a.jsx)(q,{}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8",children:t})]})]}):(l.push("/login"),null)}},7926:(e,t,r)=>{Promise.resolve().then(r.bind(r,2972))},9435:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(5155);r(2115);var s=r(9502);let l=e=>{let{className:t,useFullName:r=!0}=e;return(0,a.jsxs)("div",{className:"flex items-center text-2xl font-bold text-primary ".concat(t),children:[(0,a.jsx)(s.A,{className:"mr-2 h-7 w-7"}),(0,a.jsxs)("span",{children:["Pluto",r?" Trading Bot":""]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[1,64,527,26,655,631,870,553,318,141,441,684,358],()=>t(7926)),_N_E=e.O()}]);