"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // Try multiple API endpoints for better coverage\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API - primary source for real-time prices\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Real-time price from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toLocaleString()));\n                    return price;\n                }\n            } else {\n                console.warn(\"Binance API response not OK: \".concat(response.status, \" \").concat(response.statusText));\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Try alternative Binance 24hr ticker for more data\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/24hr?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.lastPrice);\n                if (price > 0) {\n                    console.log(\"✅ Real-time price from Binance (24hr): \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toLocaleString()));\n                    return price;\n                }\n            }\n        } catch (binanceError2) {\n            console.warn('Binance 24hr API also failed...', binanceError2);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Try to calculate from real-time USD prices\n        try {\n            const crypto1USDPrice = await getRealTimeUSDPrice(config.crypto1);\n            const crypto2USDPrice = await getRealTimeUSDPrice(config.crypto2);\n            const calculatedPrice = crypto1USDPrice / crypto2USDPrice;\n            console.log(\"⚠️ Using calculated price from real-time USD values: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(calculatedPrice.toLocaleString()));\n            return calculatedPrice;\n        } catch (usdError) {\n            console.warn('Real-time USD price calculation failed, using static fallback...', usdError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using static mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice.toLocaleString()));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Real-time price cache\nlet priceCache = {};\nlet lastPriceUpdate = 0;\nconst PRICE_CACHE_DURATION = 60000; // 1 minute cache\n// Helper function to get real-time USD price from API with fallback (async version)\nconst getRealTimeUSDPrice = async (crypto)=>{\n    const now = Date.now();\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if still valid\n    if (priceCache[cacheKey] && now - lastPriceUpdate < PRICE_CACHE_DURATION) {\n        return priceCache[cacheKey];\n    }\n    try {\n        // Try to fetch real-time price from CoinGecko API\n        const coinGeckoId = getCoinGeckoId(crypto);\n        if (coinGeckoId) {\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(coinGeckoId, \"&vs_currencies=usd\"));\n            if (response.ok) {\n                var _data_coinGeckoId;\n                const data = await response.json();\n                const price = (_data_coinGeckoId = data[coinGeckoId]) === null || _data_coinGeckoId === void 0 ? void 0 : _data_coinGeckoId.usd;\n                if (price && price > 0) {\n                    priceCache[cacheKey] = price;\n                    lastPriceUpdate = now;\n                    console.log(\"\\uD83D\\uDCCA Real-time price fetched for \".concat(crypto, \": $\").concat(price));\n                    return price;\n                }\n            }\n        }\n    } catch (error) {\n        console.warn(\"⚠️ Failed to fetch real-time price for \".concat(crypto, \", using fallback:\"), error);\n    }\n    // Fallback to static prices if API fails\n    return getUSDPrice(crypto);\n};\n// Synchronous helper function to get USD price (uses cached values or static fallback)\nconst getUSDPrice = (crypto)=>{\n    const cacheKey = crypto.toUpperCase();\n    // Return cached price if available\n    if (priceCache[cacheKey]) {\n        return priceCache[cacheKey];\n    }\n    // Fallback to static prices\n    return getStaticUSDPrice(crypto);\n};\n// Static fallback prices (used when API is unavailable)\nconst getStaticUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies - Updated to current market prices\n        'BTC': 106000,\n        'ETH': 2500,\n        'SOL': 180,\n        'ADA': 0.85,\n        'DOGE': 0.32,\n        'LINK': 22,\n        'MATIC': 0.42,\n        'DOT': 6.5,\n        'AVAX': 38,\n        'SHIB': 0.000022,\n        'XRP': 2.1,\n        'LTC': 95,\n        'BCH': 420,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            // Trigger immediate auto-save when a trade happens\n            setTimeout(()=>{\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (currentSessionId) {\n                    const currentSession = sessionManager.loadSession(currentSessionId);\n                    if (currentSession) {\n                        const newOrderHistory = [\n                            action.payload,\n                            ...state.orderHistory\n                        ];\n                        sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, newOrderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running').then(()=>{\n                            console.log('💾 Immediate auto-save triggered by trade');\n                        }).catch((error)=>{\n                            console.error('❌ Failed immediate auto-save after trade:', error);\n                        });\n                    }\n                }\n            }, 100); // Small delay to ensure state is updated\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'SET_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1,\n                crypto2Balance: action.payload.crypto2,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Validate that both crypto1 and crypto2 are selected\n            if (!state.config.crypto1 || !state.config.crypto2) {\n                console.warn('⚠️ Cannot start bot: Both crypto1 and crypto2 must be selected');\n                return state; // Don't change state if validation fails\n            }\n            // Validate that target prices are set\n            if (!state.targetPriceRows || state.targetPriceRows.length === 0) {\n                console.warn('⚠️ Cannot start bot: Target prices must be set');\n                return state; // Don't change state if validation fails\n            }\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current balances instead of resetting to initial values\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Note: URL parameter will be cleared in useEffect to avoid render-time state updates\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        console.log('🔍 Session restoration check:', {\n            currentSessionId,\n            hasSessionId: !!currentSessionId,\n            allSessions: sessionManager.getAllSessions().length\n        });\n        // If no current session exists, try to find the most recent active session\n        // BUT only if this is not an app restart (to prevent auto-restoring stopped sessions)\n        if (!currentSessionId) {\n            const lastAppClose = localStorage.getItem('pluto_last_app_close');\n            const appStartTime = Date.now();\n            const isAppRestart = !lastAppClose || appStartTime - parseInt(lastAppClose) > 5 * 60 * 1000;\n            if (!isAppRestart) {\n                // This is likely a page refresh, try to restore active session\n                const allSessions = sessionManager.getAllSessions();\n                const recentActiveSession = allSessions.filter((s)=>s.isActive).sort((a, b)=>b.lastModified - a.lastModified)[0];\n                if (recentActiveSession) {\n                    console.log('🔄 Page refresh detected - restoring recent active session:', recentActiveSession.id);\n                    sessionManager.setCurrentSession(recentActiveSession.id);\n                // Continue to load this session below\n                } else {\n                    console.log('🆕 No active sessions found - starting with fresh state');\n                    return initialTradingState;\n                }\n            } else {\n                console.log('🆕 App restart detected - starting with fresh state (running sessions already cleaned up)');\n                return initialTradingState;\n            }\n        }\n        // If we have a session, try to load the session data first (priority over localStorage)\n        const finalSessionId = sessionManager.getCurrentSessionId(); // Get the potentially updated session ID\n        if (!finalSessionId) {\n            console.log('🆕 No session ID available after restoration attempt - starting fresh');\n            return initialTradingState;\n        }\n        const currentSession = sessionManager.loadSession(finalSessionId);\n        if (currentSession) {\n            console.log('🔄 Loading session data:', {\n                sessionId: finalSessionId,\n                isActive: currentSession.isActive,\n                targetPriceRows: currentSession.targetPriceRows.length,\n                orderHistory: currentSession.orderHistory.length,\n                config: currentSession.config,\n                balances: {\n                    crypto1: currentSession.crypto1Balance,\n                    crypto2: currentSession.crypto2Balance,\n                    stablecoin: currentSession.stablecoinBalance\n                }\n            });\n            // Merge session config with initial config to ensure no empty values\n            const mergedConfig = {\n                ...initialBaseConfig,\n                ...currentSession.config,\n                // Ensure numeric values are not 0 or undefined\n                baseBid: currentSession.config.baseBid || initialBaseConfig.baseBid,\n                multiplier: currentSession.config.multiplier || initialBaseConfig.multiplier,\n                numDigits: currentSession.config.numDigits || initialBaseConfig.numDigits,\n                slippagePercent: currentSession.config.slippagePercent || initialBaseConfig.slippagePercent,\n                incomeSplitCrypto1Percent: currentSession.config.incomeSplitCrypto1Percent || initialBaseConfig.incomeSplitCrypto1Percent,\n                incomeSplitCrypto2Percent: currentSession.config.incomeSplitCrypto2Percent || initialBaseConfig.incomeSplitCrypto2Percent\n            };\n            // Determine bot status based on session state and app restart detection\n            const lastAppClose = localStorage.getItem('pluto_last_app_close');\n            const appStartTime = Date.now();\n            const isAppRestart = !lastAppClose || appStartTime - parseInt(lastAppClose) > 5 * 60 * 1000;\n            // Only restore as running if:\n            // 1. Session was active\n            // 2. Has target prices\n            // 3. This is NOT an app restart (page refresh is OK)\n            const shouldBotBeRunning = currentSession.isActive && currentSession.targetPriceRows && currentSession.targetPriceRows.length > 0 && !isAppRestart;\n            console.log('🔄 Session restoration details:', {\n                sessionId: finalSessionId,\n                isActive: currentSession.isActive,\n                hasTargetPrices: currentSession.targetPriceRows.length > 0,\n                isAppRestart,\n                shouldBotBeRunning,\n                targetPriceCount: currentSession.targetPriceRows.length,\n                orderHistoryCount: currentSession.orderHistory.length,\n                restorationReason: isAppRestart ? 'App restart - bot will be stopped' : 'Page refresh - bot state preserved',\n                targetPriceRows: currentSession.targetPriceRows.map((row)=>({\n                        id: row.id,\n                        counter: row.counter,\n                        targetPrice: row.targetPrice,\n                        status: row.status\n                    }))\n            });\n            return {\n                ...initialTradingState,\n                config: mergedConfig,\n                targetPriceRows: currentSession.targetPriceRows,\n                orderHistory: currentSession.orderHistory,\n                currentMarketPrice: currentSession.currentMarketPrice,\n                crypto1Balance: currentSession.crypto1Balance,\n                crypto2Balance: currentSession.crypto2Balance,\n                stablecoinBalance: currentSession.stablecoinBalance,\n                botSystemStatus: shouldBotBeRunning ? 'Running' : 'Stopped'\n            };\n        } else {\n            // Session ID exists but session data not found - clear the invalid session ID\n            console.warn('⚠️ Session ID exists but session data not found, clearing invalid session ID:', finalSessionId);\n            sessionManager.clearCurrentSession();\n        }\n        // Fallback to localStorage if session loading fails\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Clear URL parameter after component mounts to avoid render-time state updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                const urlParams = new URLSearchParams(window.location.search);\n                const isNewSession = urlParams.get('newSession') === 'true';\n                if (isNewSession) {\n                    // Clear the URL parameter to avoid confusion\n                    const newUrl = window.location.pathname;\n                    window.history.replaceState({}, '', newUrl);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                let price;\n                // For StablecoinSwap mode, use real-time prices for accurate market calculation\n                if (state.config.tradingMode === \"StablecoinSwap\") {\n                    // Try to get real-time prices first\n                    try {\n                        const crypto1USDPrice = await getRealTimeUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = await getRealTimeUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price Calculation (Real-time):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6), \"\\n            - Calculation: \").concat(crypto1USDPrice, \" \\xf7 \").concat(crypto2USDPrice, \" = \").concat(price.toFixed(6)));\n                    } catch (error) {\n                        // Fallback to cached/static prices\n                        const crypto1USDPrice = getUSDPrice(state.config.crypto1);\n                        const crypto2USDPrice = getUSDPrice(state.config.crypto2);\n                        price = crypto1USDPrice / crypto2USDPrice;\n                        console.log(\"\\uD83D\\uDCCA StablecoinSwap Market Price Calculation (Fallback):\\n            - \".concat(state.config.crypto1, \" USD Price: $\").concat(crypto1USDPrice.toLocaleString(), \"\\n            - \").concat(state.config.crypto2, \" USD Price: $\").concat(crypto2USDPrice.toLocaleString(), \"\\n            - Market Price (\").concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \"): \").concat(price.toFixed(6)));\n                    }\n                } else {\n                    // For SimpleSpot mode, use the existing API-based approach\n                    price = await getMarketPriceFromAPI(state.config);\n                }\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                sendAPIErrorNotification('Price API', \"Failed to fetch market price: \".concat(error)).catch(console.error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Real-time price fetching effect - fetches actual market prices every 2 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real-time price updates from Binance API every 2 seconds\n            const priceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.priceUpdateInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        try {\n                            // Fetch real market price from Binance API\n                            await fetchMarketPrice();\n                        } catch (error) {\n                            console.warn('Failed to update market price:', error);\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceUpdateInterval\"], 2000); // Update every 2 seconds as requested\n            // Set up real-time price cache update for USD prices (every 2 minutes)\n            const usdPriceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.usdPriceUpdateInterval\": async ()=>{\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline && state.config.tradingMode === \"StablecoinSwap\") {\n                        try {\n                            // Update cache for current trading pair\n                            await getRealTimeUSDPrice(state.config.crypto1);\n                            await getRealTimeUSDPrice(state.config.crypto2);\n                            console.log('📊 Real-time USD price cache updated');\n                        } catch (error) {\n                            console.warn('⚠️ Failed to update USD price cache:', error);\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.usdPriceUpdateInterval\"], 120000); // Update every 2 minutes\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceUpdateInterval);\n                    clearInterval(usdPriceUpdateInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch,\n        state.config.crypto1,\n        state.config.crypto2,\n        state.config.tradingMode\n    ]);\n    // Audio initialization and user interaction detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                // Add user interaction listener to enable audio\n                const enableAudio = {\n                    \"TradingProvider.useEffect.enableAudio\": ()=>{\n                        if (audioRef.current) {\n                            // Try to play a silent audio to enable audio context\n                            audioRef.current.volume = 0;\n                            audioRef.current.play().then({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.volume = 1;\n                                    }\n                                    console.log('🔊 Audio context enabled after user interaction');\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]).catch({\n                                \"TradingProvider.useEffect.enableAudio\": ()=>{\n                                // Still blocked, but we tried\n                                }\n                            }[\"TradingProvider.useEffect.enableAudio\"]);\n                        }\n                        // Remove the listener after first interaction\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                }[\"TradingProvider.useEffect.enableAudio\"];\n                // Listen for first user interaction\n                document.addEventListener('click', enableAudio);\n                document.addEventListener('keydown', enableAudio);\n                return ({\n                    \"TradingProvider.useEffect\": ()=>{\n                        document.removeEventListener('click', enableAudio);\n                        document.removeEventListener('keydown', enableAudio);\n                    }\n                })[\"TradingProvider.useEffect\"];\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            // Get current session's alarm settings, fallback to global app settings\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            let alarmSettings = state.appSettings; // Default fallback\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                if (currentSession && currentSession.alarmSettings) {\n                    alarmSettings = currentSession.alarmSettings;\n                }\n            }\n            if (alarmSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && alarmSettings.alertOnOrderExecution) {\n                    soundPath = alarmSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && alarmSettings.alertOnError) {\n                    soundPath = alarmSettings.soundError;\n                }\n                if (soundPath) {\n                    try {\n                        // Stop any currently playing audio first\n                        audioRef.current.pause();\n                        audioRef.current.currentTime = 0;\n                        // Set the source and load the audio\n                        audioRef.current.src = soundPath;\n                        audioRef.current.load(); // Explicitly load the audio\n                        // Add error handler for audio loading\n                        audioRef.current.onerror = ({\n                            \"TradingProvider.useCallback[playSound]\": (e)=>{\n                                console.warn(\"⚠️ Audio loading failed for \".concat(soundPath, \":\"), e);\n                                // Try fallback sound\n                                if (soundPath !== '/sounds/chime2.wav' && audioRef.current) {\n                                    audioRef.current.src = '/sounds/chime2.wav';\n                                    audioRef.current.load();\n                                }\n                            }\n                        })[\"TradingProvider.useCallback[playSound]\"];\n                        // Play the sound and limit duration to 2 seconds\n                        const playPromise = audioRef.current.play();\n                        if (playPromise !== undefined) {\n                            playPromise.then({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    // Set a timeout to pause the audio after 2 seconds\n                                    setTimeout({\n                                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                                            if (audioRef.current && !audioRef.current.paused) {\n                                                audioRef.current.pause();\n                                                audioRef.current.currentTime = 0; // Reset for next play\n                                            }\n                                        }\n                                    }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                                \"TradingProvider.useCallback[playSound]\": (err)=>{\n                                    // Handle different types of audio errors gracefully\n                                    const errorMessage = err.message || String(err);\n                                    if (errorMessage.includes('user didn\\'t interact') || errorMessage.includes('autoplay')) {\n                                        console.log('🔇 Audio autoplay blocked by browser - user interaction required');\n                                    // Could show a notification to user about enabling audio\n                                    } else if (errorMessage.includes('interrupted') || errorMessage.includes('supported source')) {\n                                    // These are expected errors, don't log them\n                                    } else {\n                                        console.warn(\"⚠️ Audio playback failed:\", errorMessage);\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"]);\n                        }\n                    } catch (error) {\n                        console.warn(\"⚠️ Audio setup failed for \".concat(soundPath, \":\"), error);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                    // Don't retry error notifications to avoid infinite loops\n                    if (!isError) {\n                        await sendTelegramErrorNotification('Telegram API Error', \"Failed to send notification: \".concat(response.statusText));\n                    }\n                } else {\n                    console.log('✅ Telegram notification sent successfully');\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n                // Network disconnection detected\n                if (!isError) {\n                    await sendTelegramErrorNotification('Network Disconnection', \"Failed to connect to Telegram API: \".concat(error));\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"⚠️ <b>ERROR ALERT</b>\\n\\n\" + \"\\uD83D\\uDD34 <b>Type:</b> \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCDD <b>Message:</b> \".concat(errorMessage, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message, true); // Mark as error to prevent recursion\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendLowBalanceNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendLowBalanceNotification]\": async (crypto, currentBalance, requiredAmount)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDCB0 <b>LOW BALANCE ALERT</b>\\n\\n\" + \"\\uD83E\\uDE99 <b>Currency:</b> \".concat(crypto, \"\\n\") + \"\\uD83D\\uDCCA <b>Current Balance:</b> \".concat(currentBalance.toFixed(6), \" \").concat(crypto, \"\\n\") + \"⚡ <b>Required Amount:</b> \".concat(requiredAmount.toFixed(6), \" \").concat(crypto, \"\\n\") + \"\\uD83D\\uDCC9 <b>Shortage:</b> \".concat((requiredAmount - currentBalance).toFixed(6), \" \").concat(crypto, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendLowBalanceNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    const sendAPIErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendAPIErrorNotification]\": async (apiName, errorDetails)=>{\n            const timestamp = new Date().toLocaleString();\n            const message = \"\\uD83D\\uDD0C <b>API ERROR ALERT</b>\\n\\n\" + \"\\uD83C\\uDF10 <b>API:</b> \".concat(apiName, \"\\n\") + \"❌ <b>Error:</b> \".concat(errorDetails, \"\\n\") + \"⏰ <b>Time:</b> \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 <b>Bot:</b> \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2, \" \").concat(state.config.tradingMode);\n            await sendTelegramNotification(message);\n        }\n    }[\"TradingProvider.useCallback[sendAPIErrorNotification]\"], [\n        state.config,\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    // Manual save session function - defined before setTargetPrices to avoid initialization error\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": async ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, only create one if bot is running or has meaningful data\n                    if (state.config.crypto1 && state.config.crypto2 && (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0)) {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        const sessionId = await sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances);\n                        sessionManager.setCurrentSession(sessionId);\n                        await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        return true;\n                    }\n                    console.log('⚠️ No session to save - bot not running and no meaningful data');\n                    return false;\n                }\n                // Save existing session\n                return await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n            // Immediately save session when target prices are set\n            setTimeout({\n                \"TradingProvider.useCallback[setTargetPrices]\": async ()=>{\n                    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    if (currentSessionId) {\n                        await sessionManager.saveSession(currentSessionId, state.config, newRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        console.log('✅ Session saved immediately after setting target prices');\n                    }\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices]\"], 100); // Small delay to ensure state is updated\n            // Automatically create/save session when target prices are set to ensure persistence\n            setTimeout({\n                \"TradingProvider.useCallback[setTargetPrices]\": async ()=>{\n                    try {\n                        await saveCurrentSession();\n                        console.log('✅ Session auto-saved after setting target prices');\n                    } catch (error) {\n                        console.error('❌ Failed to auto-save session after setting target prices:', error);\n                    }\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices]\"], 100); // Small delay to ensure state is updated\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch,\n        saveCurrentSession\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(costCrypto2.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, costCrypto2).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Use current fluctuating market price for more realistic P/L\n                                const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                                const preferredStablecoin = config.preferredStablecoin || 'USDT';\n                                const stablecoinUSDPrice = getUSDPrice(preferredStablecoin);\n                                const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Use current fluctuating market price (this is the key for P/L calculation)\n                                const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                                // Apply current market price fluctuation to Crypto1 price\n                                const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                                const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                console.log(\"\\uD83D\\uDCB0 StablecoinSwap BUY - Setting originalCostCrypto2:\", {\n                                    counter: activeRow.counter,\n                                    amountCrypto2ToUse: amountCrypto2ToUse,\n                                    crypto1Bought: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    newLevel: newLevel,\n                                    crypto1StablecoinPrice: crypto1StablecoinPrice,\n                                    fluctuatedCrypto1Price: fluctuatedCrypto1Price,\n                                    currentMarketPrice: state.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                // Calculate P/L for the crypto2 sell based on price fluctuation\n                                const baseCrypto2Price = getUSDPrice(config.crypto2) / getUSDPrice(preferredStablecoin);\n                                const estimatedProfitCrypto2 = amountCrypto2ToUse * (crypto2StablecoinPrice - baseCrypto2Price);\n                                const estimatedProfitCrypto1 = estimatedProfitCrypto2 / crypto1StablecoinPrice;\n                                console.log(\"\\uD83D\\uDCCA StablecoinSwap Step A SELL P/L:\", {\n                                    crypto2: config.crypto2,\n                                    amountSold: amountCrypto2ToUse,\n                                    currentPrice: crypto2StablecoinPrice,\n                                    basePrice: baseCrypto2Price,\n                                    estimatedProfitCrypto2: estimatedProfitCrypto2,\n                                    estimatedProfitCrypto1: estimatedProfitCrypto1\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: preferredStablecoin,\n                                        realizedProfitLossCrypto2: estimatedProfitCrypto2,\n                                        realizedProfitLossCrypto1: estimatedProfitCrypto1\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: preferredStablecoin\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram notification\n                                console.log(\"❌ Insufficient \".concat(config.crypto2, \" balance for stablecoin swap: \").concat(currentCrypto2Balance.toFixed(6), \" < \").concat(amountCrypto2ToUse.toFixed(6)));\n                                sendLowBalanceNotification(config.crypto2, currentCrypto2Balance, amountCrypto2ToUse).catch(console.error);\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        console.log(\"\\uD83D\\uDD0D StablecoinSwap SELL Check:\", {\n                            currentCounter: currentCounter,\n                            inferiorRowFound: !!inferiorRow,\n                            inferiorRowStatus: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.status,\n                            inferiorRowCrypto1Held: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.crypto1AmountHeld,\n                            inferiorRowOriginalCost: inferiorRow === null || inferiorRow === void 0 ? void 0 : inferiorRow.originalCostCrypto2,\n                            canExecuteSell: !!(inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2)\n                        });\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            // Use current fluctuating market price for realistic P/L\n                            const crypto1USDPrice = getUSDPrice(config.crypto1 || 'BTC');\n                            const crypto2USDPrice = getUSDPrice(config.crypto2 || 'USDT');\n                            const preferredStablecoin = config.preferredStablecoin || 'USDT';\n                            const stablecoinUSDPrice = getUSDPrice(preferredStablecoin);\n                            // Apply current market price fluctuation to Crypto1 price\n                            const fluctuatedCrypto1Price = crypto1USDPrice * (state.currentMarketPrice / (crypto1USDPrice / crypto2USDPrice));\n                            const crypto1StablecoinPrice = fluctuatedCrypto1Price / stablecoinUSDPrice;\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = crypto2USDPrice / stablecoinUSDPrice;\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            // We compare the value of crypto2 we got back vs what we originally spent\n                            const originalCost = inferiorRow.originalCostCrypto2 || 0;\n                            const realizedProfitInCrypto2 = crypto2Reacquired - originalCost;\n                            // Calculate Crypto1 profit/loss - this should be the profit in terms of Crypto1\n                            // For StablecoinSwap, we need to calculate how much Crypto1 profit this represents\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 / crypto1StablecoinPrice : 0;\n                            // IMPORTANT: If P/L is exactly 0, it might indicate a price calculation issue\n                            if (realizedProfitInCrypto2 === 0) {\n                                console.warn(\"⚠️ P/L is exactly 0 - this might indicate an issue:\\n                - Are prices changing between BUY and SELL?\\n                - Is the market price fluctuation working?\\n                - Current market price: \".concat(state.currentMarketPrice));\n                            }\n                            console.log(\"\\uD83D\\uDCB0 StablecoinSwap P/L Calculation DETAILED:\\n              - Inferior Row Counter: \".concat(inferiorRow.counter, \"\\n              - Original Cost (Crypto2): \").concat(originalCost, \"\\n              - Crypto2 Reacquired: \").concat(crypto2Reacquired, \"\\n              - Realized P/L (Crypto2): \").concat(realizedProfitInCrypto2, \"\\n              - Crypto1 Stablecoin Price: \").concat(crypto1StablecoinPrice, \"\\n              - Realized P/L (Crypto1): \").concat(realizedProfitCrypto1, \"\\n              - Is Profitable: \").concat(realizedProfitInCrypto2 > 0 ? 'YES' : 'NO', \"\\n              - Calculation: \").concat(crypto2Reacquired, \" - \").concat(originalCost, \" = \").concat(realizedProfitInCrypto2));\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            // Step A: SELL Crypto1 for Stablecoin (with profit/loss data for analytics)\n                            console.log(\"\\uD83D\\uDCCA Adding StablecoinSwap SELL order to history with P/L:\", {\n                                realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                pair: \"\".concat(config.crypto1, \"/\").concat(preferredStablecoin),\n                                orderType: \"SELL\",\n                                amountCrypto1: amountCrypto1ToSell,\n                                avgPrice: crypto1StablecoinPrice,\n                                valueCrypto2: stablecoinFromC1Sell,\n                                price1: crypto1StablecoinPrice,\n                                crypto1Symbol: config.crypto1,\n                                crypto2Symbol: preferredStablecoin\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: preferredStablecoin,\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            // Step B: BUY Crypto2 with Stablecoin (no profit/loss as it's just conversion)\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: preferredStablecoin\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to validate bot status - stop bot if no target prices\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'Running' && state.targetPriceRows.length === 0) {\n                console.log('⚠️ Bot is running but no target prices set - stopping bot');\n                dispatch({\n                    type: 'SYSTEM_STOP_BOT'\n                });\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.targetPriceRows.length,\n        dispatch\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to ensure session is created and saved when bot starts running\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'Running') {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                // If no session exists, create one\n                if (!currentSessionId && state.config.crypto1 && state.config.crypto2) {\n                    console.log('🔄 Creating session for running bot...');\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": async (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            // Save the session with current state and mark as active\n                            await sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // Bot is running - this marks session as active\n                            );\n                            console.log('✅ Session created and saved for running bot:', sessionId);\n                            // Trigger storage event to notify admin panel\n                            window.dispatchEvent(new StorageEvent('storage', {\n                                key: 'pluto_active_sessions',\n                                newValue: Date.now().toString(),\n                                storageArea: localStorage\n                            }));\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to create session for running bot:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                } else if (currentSessionId) {\n                    // Session exists, mark it as active and save current state\n                    console.log('💾 Marking session as active and saving state for running bot...');\n                    // Ensure session is marked as active in the session manager\n                    sessionManager.setCurrentSession(currentSessionId);\n                    // Save session and ensure it's marked as active\n                    sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // Bot is running - this marks session as active\n                    ).then({\n                        \"TradingProvider.useEffect\": ()=>{\n                            console.log('✅ Session saved and marked as active for running bot:', currentSessionId);\n                            // Trigger storage event to notify admin panel\n                            window.dispatchEvent(new StorageEvent('storage', {\n                                key: 'pluto_active_sessions',\n                                newValue: Date.now().toString(),\n                                storageArea: localStorage\n                            }));\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to save session for running bot:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config,\n        state.targetPriceRows,\n        state.orderHistory,\n        state.currentMarketPrice,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot actually starts running (not during warmup)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot actually starts running\n            if (state.botSystemStatus === 'Running' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session when bot started:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot(currentSessionId);\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": async (savedSessionId)=>{\n                                await sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Only create new session for the new crypto pair if bot was actually running\n                    if (state.config.crypto1 && state.config.crypto2 && state.botSystemStatus === 'Running') {\n                        const currentBalances = {\n                            crypto1: state.crypto1Balance,\n                            crypto2: state.crypto2Balance,\n                            stablecoin: state.stablecoinBalance\n                        };\n                        sessionManager.createNewSessionWithAutoName(state.config, undefined, currentBalances).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2, 'with balances:', currentBalances);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    } else {\n                        // If bot wasn't running, just clear the current session without creating a new one\n                        sessionManager.clearCurrentSession();\n                        console.log('🔄 Crypto pair changed but bot wasn\\'t running - cleared current session');\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Send Telegram error notification for internet connection loss\n                            sendTelegramErrorNotification('Internet Connection Lost', 'Trading bot has been automatically stopped due to internet connection loss. Session has been saved and will resume when connection is restored.').catch(console.error);\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": async (backupSessionId)=>{\n                                            await sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": async ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            // Verify the session exists before trying to save\n                            const currentSession = sessionManager.loadSession(currentSessionId);\n                            if (currentSession) {\n                                try {\n                                    await sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                                } catch (saveError) {\n                                    console.warn('⚠️ Session save failed, will retry on next auto-save:', saveError);\n                                // Don't clear the session immediately, let the session manager handle it\n                                }\n                            } else {\n                                console.warn('⚠️ Current session ID exists but session not found, clearing session ID');\n                                sessionManager.clearCurrentSession();\n                            }\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.warn('Auto-save failed, will retry on next cycle:', error);\n                    // Don't clear sessions on auto-save failures to avoid data loss\n                    // The session manager will handle session recovery automatically\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            // Auto-save more frequently when bot is running, less frequently when stopped\n            const autoSaveInterval = state.botSystemStatus === 'Running' ? 15000 : 60000; // 15s when running, 60s when stopped\n            autoSaveManager.enable(saveFunction, autoSaveInterval);\n            console.log(\"⏰ Auto-save enabled with \".concat(autoSaveInterval / 1000, \"s interval (bot status: \").concat(state.botSystemStatus, \")\"));\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    console.log('🚪 Browser closing/refreshing - saving session state');\n                    // Mark app close time for restart detection\n                    localStorage.setItem('pluto_last_app_close', Date.now().toString());\n                    // Get current session info\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    const currentSession = currentSessionId ? sessionManager.loadSession(currentSessionId) : null;\n                    // Save current session state immediately\n                    saveFunction();\n                    // If bot is running, auto-save to past sessions and stop the bot\n                    if (state.botSystemStatus === 'Running' && currentSession) {\n                        console.log('🛑 Bot is running - auto-saving to past sessions before close');\n                        try {\n                            // Create auto-saved session in past sessions\n                            const timestamp = new Date().toLocaleString('en-US', {\n                                month: 'short',\n                                day: 'numeric',\n                                hour: '2-digit',\n                                minute: '2-digit',\n                                hour12: false\n                            });\n                            const autoSavedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                            // Create new session for past sessions (synchronously for beforeunload)\n                            const autoSavedId = sessionManager.createNewSessionSync(autoSavedName, currentSession.config);\n                            // Save the session with current progress as inactive (use Promise.resolve for sync operation)\n                            if (typeof autoSavedId === 'string') {\n                                sessionManager.saveSession(autoSavedId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false // Mark as inactive (past session)\n                                );\n                            }\n                            // Mark current session as inactive too\n                            if (currentSessionId) {\n                                sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false // Mark as inactive\n                                );\n                            }\n                            console.log('✅ Auto-saved running session to past sessions:', autoSavedName);\n                        } catch (error) {\n                            console.error('❌ Failed to auto-save running session:', error);\n                        }\n                        // Show warning message\n                        const message = 'Trading bot is currently running and will be stopped. Progress has been auto-saved.';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes and handle session active state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n            // Handle session active state based on bot status\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const isActive = state.botSystemStatus === 'Running';\n                console.log(\"\\uD83D\\uDD04 Bot status changed to \".concat(state.botSystemStatus, \", updating session active state:\"), {\n                    sessionId: currentSessionId,\n                    isActive\n                });\n                // Save session with updated active state\n                sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, isActive).then({\n                    \"TradingProvider.useEffect\": ()=>{\n                        // Trigger storage event to notify admin panel\n                        window.dispatchEvent(new StorageEvent('storage', {\n                            key: 'pluto_active_sessions',\n                            newValue: Date.now().toString(),\n                            storageArea: localStorage\n                        }));\n                    }\n                }[\"TradingProvider.useEffect\"]).catch({\n                    \"TradingProvider.useEffect\": (error)=>{\n                        console.error('❌ Failed to update session active state:', error);\n                    }\n                }[\"TradingProvider.useEffect\"]);\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config,\n        state.targetPriceRows,\n        state.orderHistory,\n        state.currentMarketPrice,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 2329,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"UoZzKekANiXdjxhLtue4DUxFfp0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});