"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[106],{207:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},620:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},968:(e,t,a)=>{a.d(t,{b:()=>d});var r=a(2115),n=a(3655),l=a(5155),o=r.forwardRef((e,t)=>(0,l.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var d=o},2773:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},3129:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},3503:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},3576:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]])},4607:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},4884:(e,t,a)=>{a.d(t,{bL:()=>A,zi:()=>M});var r=a(2115),n=a(5185),l=a(6101),o=a(6081),d=a(5845),i=a(5503),s=a(1275),c=a(3655),u=a(5155),p="Switch",[h,f]=(0,o.A)(p),[y,v]=h(p),k=r.forwardRef((e,t)=>{let{__scopeSwitch:a,name:o,checked:i,defaultChecked:s,required:p,disabled:h,value:f="on",onCheckedChange:v,form:k,...g}=e,[m,A]=r.useState(null),M=(0,l.s)(t,e=>A(e)),w=r.useRef(!1),j=!m||k||!!m.closest("form"),[D=!1,C]=(0,d.i)({prop:i,defaultProp:s,onChange:v});return(0,u.jsxs)(y,{scope:a,checked:D,disabled:h,children:[(0,u.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":D,"aria-required":p,"data-state":b(D),"data-disabled":h?"":void 0,disabled:h,value:f,...g,ref:M,onClick:(0,n.m)(e.onClick,e=>{C(e=>!e),j&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),j&&(0,u.jsx)(x,{control:m,bubbles:!w.current,name:o,value:f,checked:D,required:p,disabled:h,form:k,style:{transform:"translateX(-100%)"}})]})});k.displayName=p;var g="SwitchThumb",m=r.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,n=v(g,a);return(0,u.jsx)(c.sG.span,{"data-state":b(n.checked),"data-disabled":n.disabled?"":void 0,...r,ref:t})});m.displayName=g;var x=e=>{let{control:t,checked:a,bubbles:n=!0,...l}=e,o=r.useRef(null),d=(0,i.Z)(a),c=(0,s.X)(t);return r.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==a&&t){let r=new Event("click",{bubbles:n});t.call(e,a),e.dispatchEvent(r)}},[d,a,n]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...l,tabIndex:-1,ref:o,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function b(e){return e?"checked":"unchecked"}var A=k,M=m},5300:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},5318:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5452:(e,t,a)=>{a.d(t,{UC:()=>et,VY:()=>er,ZL:()=>$,bL:()=>Y,bm:()=>en,hE:()=>ea,hJ:()=>ee,l9:()=>Q});var r=a(2115),n=a(5185),l=a(6101),o=a(6081),d=a(1285),i=a(5845),s=a(9178),c=a(7900),u=a(4378),p=a(8905),h=a(3655),f=a(2293),y=a(3795),v=a(8168),k=a(9708),g=a(5155),m="Dialog",[x,b]=(0,o.A)(m),[A,M]=x(m),w=e=>{let{__scopeDialog:t,children:a,open:n,defaultOpen:l,onOpenChange:o,modal:s=!0}=e,c=r.useRef(null),u=r.useRef(null),[p=!1,h]=(0,i.i)({prop:n,defaultProp:l,onChange:o});return(0,g.jsx)(A,{scope:t,triggerRef:c,contentRef:u,contentId:(0,d.B)(),titleId:(0,d.B)(),descriptionId:(0,d.B)(),open:p,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),modal:s,children:a})};w.displayName=m;var j="DialogTrigger",D=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,o=M(j,a),d=(0,l.s)(t,o.triggerRef);return(0,g.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Z(o.open),...r,ref:d,onClick:(0,n.m)(e.onClick,o.onOpenToggle)})});D.displayName=j;var C="DialogPortal",[R,E]=x(C,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:a,children:n,container:l}=e,o=M(C,t);return(0,g.jsx)(R,{scope:t,forceMount:a,children:r.Children.map(n,e=>(0,g.jsx)(p.C,{present:a||o.open,children:(0,g.jsx)(u.Z,{asChild:!0,container:l,children:e})}))})};I.displayName=C;var H="DialogOverlay",N=r.forwardRef((e,t)=>{let a=E(H,e.__scopeDialog),{forceMount:r=a.forceMount,...n}=e,l=M(H,e.__scopeDialog);return l.modal?(0,g.jsx)(p.C,{present:r||l.open,children:(0,g.jsx)(O,{...n,ref:t})}):null});N.displayName=H;var O=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=M(H,a);return(0,g.jsx)(y.A,{as:k.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,g.jsx)(h.sG.div,{"data-state":Z(n.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_="DialogContent",F=r.forwardRef((e,t)=>{let a=E(_,e.__scopeDialog),{forceMount:r=a.forceMount,...n}=e,l=M(_,e.__scopeDialog);return(0,g.jsx)(p.C,{present:r||l.open,children:l.modal?(0,g.jsx)(P,{...n,ref:t}):(0,g.jsx)(q,{...n,ref:t})})});F.displayName=_;var P=r.forwardRef((e,t)=>{let a=M(_,e.__scopeDialog),o=r.useRef(null),d=(0,l.s)(t,a.contentRef,o);return r.useEffect(()=>{let e=o.current;if(e)return(0,v.Eq)(e)},[]),(0,g.jsx)(z,{...e,ref:d,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=a.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=r.forwardRef((e,t)=>{let a=M(_,e.__scopeDialog),n=r.useRef(!1),l=r.useRef(!1);return(0,g.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,o;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(n.current||null===(o=a.triggerRef.current)||void 0===o||o.focus(),t.preventDefault()),n.current=!1,l.current=!1},onInteractOutside:t=>{var r,o;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let d=t.target;(null===(o=a.triggerRef.current)||void 0===o?void 0:o.contains(d))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),z=r.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:d,...i}=e,u=M(_,a),p=r.useRef(null),h=(0,l.s)(t,p);return(0,f.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:d,children:(0,g.jsx)(s.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...i,ref:h,onDismiss:()=>u.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(U,{titleId:u.titleId}),(0,g.jsx)(J,{contentRef:p,descriptionId:u.descriptionId})]})]})}),V="DialogTitle",L=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=M(V,a);return(0,g.jsx)(h.sG.h2,{id:n.titleId,...r,ref:t})});L.displayName=V;var T="DialogDescription",G=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=M(T,a);return(0,g.jsx)(h.sG.p,{id:n.descriptionId,...r,ref:t})});G.displayName=T;var S="DialogClose",B=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,l=M(S,a);return(0,g.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:(0,n.m)(e.onClick,()=>l.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}B.displayName=S;var W="DialogTitleWarning",[X,K]=(0,o.q)(W,{contentName:_,titleName:V,docsSlug:"dialog"}),U=e=>{let{titleId:t}=e,a=K(W),n="`".concat(a.contentName,"` requires a `").concat(a.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(a.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(a.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(n)},[n,t]),null},J=e=>{let{contentRef:t,descriptionId:a}=e,n=K("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");a&&r&&!document.getElementById(a)&&console.warn(l)},[l,t,a]),null},Y=w,Q=D,$=I,ee=N,et=F,ea=L,er=G,en=B},7223:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},7554:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},7607:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8186:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},8271:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8718:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9119:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(157).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])}}]);