"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,RefreshCw,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/SessionAlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/SessionAlarmConfigModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSessionForAlarm, setSelectedSessionForAlarm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            console.log('🔄 Admin Panel - Initial load:', {\n                sessionId,\n                totalSessions: sessionManager.getAllSessions().length,\n                activeSessions: sessionManager.getCurrentActiveSessions().length\n            });\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    var _event_key, _event_key1, _event_key2;\n                    console.log('🔄 Admin Panel - Storage change detected:', {\n                        key: event.key,\n                        hasNewValue: !!event.newValue\n                    });\n                    if (event.key === 'pluto_trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                    // Listen for active session changes\n                    if ((_event_key = event.key) === null || _event_key === void 0 ? void 0 : _event_key.includes('active_sessions')) {\n                        loadSessions();\n                        console.log('🔄 Active sessions updated from another window');\n                    }\n                    // Listen for current session changes (when bot starts/stops)\n                    if ((_event_key1 = event.key) === null || _event_key1 === void 0 ? void 0 : _event_key1.includes('current_session')) {\n                        const newSessionId = sessionManager.getCurrentSessionId();\n                        setCurrentSessionId(newSessionId);\n                        loadSessions();\n                        console.log('🔄 Current session changed:', newSessionId);\n                    }\n                    // Listen for any session-related storage changes\n                    if ((_event_key2 = event.key) === null || _event_key2 === void 0 ? void 0 : _event_key2.includes('pluto_')) {\n                        loadSessions();\n                        console.log('🔄 Pluto storage updated, refreshing sessions');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            // Set up periodic refresh to ensure admin panel stays in sync\n            const refreshInterval = setInterval({\n                \"SessionManager.useEffect.refreshInterval\": ()=>{\n                    loadSessions();\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    setCurrentSessionId(currentSessionId);\n                }\n            }[\"SessionManager.useEffect.refreshInterval\"], 5000); // Refresh every 5 seconds\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                    clearInterval(refreshInterval);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display and refresh sessions for active sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    // Update current session runtime\n                    if (currentSessionId) {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                    }\n                    // Refresh sessions to update active status and runtime displays\n                    loadSessions();\n                }\n            }[\"SessionManager.useEffect.interval\"], 2000); // Update every 2 seconds for more responsive admin panel\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        console.log('🔄 Admin Panel - Loading sessions:', {\n            totalSessions: allSessions.length,\n            currentSessionId: sessionManager.getCurrentSessionId(),\n            sessions: allSessions.map((s)=>({\n                    id: s.id,\n                    name: s.name,\n                    isActive: s.isActive\n                }))\n        });\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = await sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        try {\n            var _session_targetPriceRows, _session_orderHistory, _session_targetPriceRows1, _session_orderHistory1;\n            console.log('🔄 Attempting to load session:', sessionId);\n            const session = sessionManager.loadSession(sessionId);\n            if (!session) {\n                console.error('❌ Session not found:', sessionId);\n                console.log('Available sessions:', sessionManager.getAllSessions().map((s)=>({\n                        id: s.id,\n                        name: s.name\n                    })));\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to load session - session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            console.log('🔄 Loading session with full data:', {\n                sessionId,\n                name: session.name,\n                config: session.config,\n                targetPriceRows: ((_session_targetPriceRows = session.targetPriceRows) === null || _session_targetPriceRows === void 0 ? void 0 : _session_targetPriceRows.length) || 0,\n                orderHistory: ((_session_orderHistory = session.orderHistory) === null || _session_orderHistory === void 0 ? void 0 : _session_orderHistory.length) || 0,\n                balances: {\n                    crypto1: session.crypto1Balance,\n                    crypto2: session.crypto2Balance,\n                    stablecoin: session.stablecoinBalance\n                },\n                isActive: session.isActive,\n                runtime: session.runtime\n            });\n            // Ensure we have valid data before loading\n            if (!session.config || !session.config.crypto1 || !session.config.crypto2) {\n                console.error('❌ Invalid session config:', session.config);\n                toast({\n                    title: \"Error\",\n                    description: \"Session has invalid configuration\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Load session data into context step by step\n            console.log('📝 Setting config:', session.config);\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: session.config\n            });\n            console.log('📝 Setting target price rows:', ((_session_targetPriceRows1 = session.targetPriceRows) === null || _session_targetPriceRows1 === void 0 ? void 0 : _session_targetPriceRows1.length) || 0);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: session.targetPriceRows || []\n            });\n            console.log('📝 Clearing and loading order history:', ((_session_orderHistory1 = session.orderHistory) === null || _session_orderHistory1 === void 0 ? void 0 : _session_orderHistory1.length) || 0);\n            dispatch({\n                type: 'CLEAR_ORDER_HISTORY'\n            });\n            if (session.orderHistory && session.orderHistory.length > 0) {\n                session.orderHistory.forEach((entry)=>{\n                    dispatch({\n                        type: 'ADD_ORDER_HISTORY_ENTRY',\n                        payload: entry\n                    });\n                });\n            }\n            console.log('📝 Setting market price:', session.currentMarketPrice);\n            dispatch({\n                type: 'SET_MARKET_PRICE',\n                payload: session.currentMarketPrice || 100000\n            });\n            console.log('📝 Setting balances:', {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance,\n                stablecoin: session.stablecoinBalance\n            });\n            dispatch({\n                type: 'SET_BALANCES',\n                payload: {\n                    crypto1: session.crypto1Balance || 10000,\n                    crypto2: session.crypto2Balance || 10000,\n                    stablecoin: session.stablecoinBalance || 10000\n                }\n            });\n            // Set session as current BEFORE determining bot status\n            sessionManager.setCurrentSession(sessionId);\n            setCurrentSessionId(sessionId);\n            // Determine bot status - NEVER restore as running after manual load\n            // All loaded sessions should start as stopped for safety\n            console.log('📝 Setting bot status to Stopped (loaded sessions always start stopped)');\n            dispatch({\n                type: 'SYSTEM_STOP_BOT'\n            });\n            // Mark session as inactive since we're loading it manually\n            sessionManager.saveSession(sessionId, session.config, session.targetPriceRows || [], session.orderHistory || [], session.currentMarketPrice || 100000, session.crypto1Balance || 10000, session.crypto2Balance || 10000, session.stablecoinBalance || 10000, false // Mark as inactive when loaded\n            );\n            loadSessions();\n            toast({\n                title: \"Session Loaded\",\n                description: 'Session \"'.concat(session.name, '\" has been loaded successfully')\n            });\n            // Navigate to dashboard to see the loaded session\n            console.log('🔄 Navigating to dashboard...');\n            setTimeout(()=>{\n                window.location.href = '/dashboard';\n            }, 1000);\n        } catch (error) {\n            console.error('❌ Error loading session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session: \".concat(error instanceof Error ? error.message : 'Unknown error'),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteSession = async (sessionId)=>{\n        const success = await sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleOpenAlarmConfig = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (session) {\n            setSelectedSessionForAlarm({\n                id: sessionId,\n                name: session.name,\n                alarmSettings: session.alarmSettings\n            });\n            setAlarmModalOpen(true);\n        }\n    };\n    const handleSaveAlarmSettings = async (sessionId, alarmSettings)=>{\n        const success = await sessionManager.updateSessionAlarmSettings(sessionId, alarmSettings);\n        if (success) {\n            loadSessions();\n            toast({\n                title: \"Alarm Settings Saved\",\n                description: \"Session alarm settings have been updated\"\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save alarm settings\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        // Simplified approach - just get sessions that are marked as active\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // Get all sessions and filter for active ones\n        const allSessions = sessionManager.getAllSessions();\n        const activeSessions = allSessions.filter((session)=>session.isActive);\n        // Convert to the format expected by the UI\n        const result = activeSessions.map((session)=>{\n            var _session_config, _session_config1, _session_orderHistory;\n            return {\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(((_session_config = session.config) === null || _session_config === void 0 ? void 0 : _session_config.crypto1) || 'Unknown', \"/\").concat(((_session_config1 = session.config) === null || _session_config1 === void 0 ? void 0 : _session_config1.crypto2) || 'Unknown'),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: sessionManager.getCurrentRuntime(session.id),\n                totalTrades: ((_session_orderHistory = session.orderHistory) === null || _session_orderHistory === void 0 ? void 0 : _session_orderHistory.length) || 0,\n                totalProfitLoss: (session.orderHistory || []).filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            };\n        });\n        console.log('🔍 Admin Panel - Active Sessions (Simplified):', {\n            currentSessionId,\n            totalSessions: allSessions.length,\n            activeSessions: activeSessions.length,\n            result: result.map((s)=>({\n                    id: s.id,\n                    name: s.name,\n                    isActive: s.isActive\n                }))\n        });\n        return result;\n    };\n    const getInactiveSessions = ()=>{\n        return sessions.filter((s)=>!s.isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Current Sessions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        console.log('🔄 Manual refresh triggered');\n                                        loadSessions();\n                                    },\n                                    title: \"Refresh Sessions\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Alarm\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        onClick: ()=>handleOpenAlarmConfig(session.id),\n                                                        title: \"Configure Alarms\",\n                                                        className: \"btn-outline-neo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: handleSaveCurrentSession,\n                                                        size: \"sm\",\n                                                        className: \"btn-neo\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Save\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 616,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, session.id, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Alarm\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 681,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleOpenAlarmConfig(session.id),\n                                                            title: \"Configure Alarms\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_RefreshCw_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 730,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, session.id, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, this),\n            selectedSessionForAlarm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SessionAlarmConfigModal__WEBPACK_IMPORTED_MODULE_10__.SessionAlarmConfigModal, {\n                isOpen: alarmModalOpen,\n                onClose: ()=>{\n                    setAlarmModalOpen(false);\n                    setSelectedSessionForAlarm(null);\n                },\n                sessionId: selectedSessionForAlarm.id,\n                sessionName: selectedSessionForAlarm.name,\n                currentAlarmSettings: selectedSessionForAlarm.alarmSettings,\n                onSave: handleSaveAlarmSettings\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 745,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 514,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"qvvjKP1M0ciSaXHnASZ/CLpomBw=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});