"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[527,737],{2085:(e,t,n)=>{n.d(t,{F:()=>s});var r=n(2596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:i}=t,u=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let o=l(t)||l(r);return s[e][o]}),a=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,u,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...a}[t]):({...i,...a})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2564:(e,t,n)=>{n.d(t,{s:()=>s});var r=n(2115),l=n(3655),o=n(5155),s=r.forwardRef((e,t)=>(0,o.jsx)(l.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));s.displayName="VisuallyHidden"},4378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),l=n(7650),o=n(3655),s=n(2712),i=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:a,...d}=e,[c,v]=r.useState(!1);(0,s.N)(()=>v(!0),[]);let f=a||c&&(null===(u=globalThis)||void 0===u?void 0:null===(n=u.document)||void 0===n?void 0:n.body);return f?l.createPortal((0,i.jsx)(o.sG.div,{...d,ref:t}),f):null});u.displayName="Portal"},9178:(e,t,n)=>{n.d(t,{lg:()=>b,qW:()=>v,bL:()=>m});var r,l=n(2115),o=n(5185),s=n(3655),i=n(6101),u=n(9033),a=n(5155),d="dismissableLayer.update",c=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),v=l.forwardRef((e,t)=>{var n,v;let{disableOutsidePointerEvents:f=!1,onEscapeKeyDown:m,onPointerDownOutside:b,onFocusOutside:E,onInteractOutside:h,onDismiss:w,...g}=e,D=l.useContext(c),[C,L]=l.useState(null),P=null!==(v=null==C?void 0:C.ownerDocument)&&void 0!==v?v:null===(n=globalThis)||void 0===n?void 0:n.document,[,O]=l.useState({}),k=(0,i.s)(t,e=>L(e)),R=Array.from(D.layers),[U]=[...D.layersWithOutsidePointerEventsDisabled].slice(-1),N=R.indexOf(U),x=C?R.indexOf(C):-1,S=D.layersWithOutsidePointerEventsDisabled.size>0,W=x>=N,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),o=l.useRef(!1),s=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){y("dismissableLayer.pointerDownOutside",r,l,{discrete:!0})},l={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...D.branches].some(e=>e.contains(t));!W||n||(null==b||b(e),null==h||h(e),e.defaultPrevented||null==w||w())},P),T=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&y("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...D.branches].some(e=>e.contains(t))||(null==E||E(e),null==h||h(e),e.defaultPrevented||null==w||w())},P);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{x===D.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},P),l.useEffect(()=>{if(C)return f&&(0===D.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),D.layersWithOutsidePointerEventsDisabled.add(C)),D.layers.add(C),p(),()=>{f&&1===D.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[C,P,f,D]),l.useEffect(()=>()=>{C&&(D.layers.delete(C),D.layersWithOutsidePointerEventsDisabled.delete(C),p())},[C,D]),l.useEffect(()=>{let e=()=>O({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,a.jsx)(s.sG.div,{...g,ref:k,style:{pointerEvents:S?W?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,T.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});v.displayName="DismissableLayer";var f=l.forwardRef((e,t)=>{let n=l.useContext(c),r=l.useRef(null),o=(0,i.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(s.sG.div,{...e,ref:o})});function p(){let e=new CustomEvent(d);document.dispatchEvent(e)}function y(e,t,n,r){let{discrete:l}=r,o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),l?(0,s.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayerBranch";var m=v,b=f},9737:(e,t,n)=>{let r;n.d(t,{A:()=>i});let l={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},o=new Uint8Array(16),s=[];for(let e=0;e<256;++e)s.push((e+256).toString(16).slice(1));let i=function(e,t,n){if(l.randomUUID&&!t&&!e)return l.randomUUID();let i=(e=e||{}).random||(e.rng||function(){if(!r&&!(r="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return r(o)})();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,t){n=n||0;for(let e=0;e<16;++e)t[n+e]=i[e];return t}return function(e,t=0){return s[e[t+0]]+s[e[t+1]]+s[e[t+2]]+s[e[t+3]]+"-"+s[e[t+4]]+s[e[t+5]]+"-"+s[e[t+6]]+s[e[t+7]]+"-"+s[e[t+8]]+s[e[t+9]]+"-"+s[e[t+10]]+s[e[t+11]]+s[e[t+12]]+s[e[t+13]]+s[e[t+14]]+s[e[t+15]]}(i)}}}]);