"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[553],{4553:(e,s,t)=>{t.d(s,{SessionManager:()=>d});var o=t(9737),i=t(5731);let n="pluto_trading_sessions",a="pluto_current_session",r="pluto_active_sessions",c=()=>"window_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11)),l=()=>{let e=sessionStorage.getItem("pluto_window_id");return e?console.log("\uD83D\uDD04 Using existing window ID: ".concat(e)):(e=c(),sessionStorage.setItem("pluto_window_id",e),console.log("\uD83C\uDD95 Created new window ID: ".concat(e))),e};class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}generateSessionName(e){let s=e.crypto1||"Crypto1",t=e.crypto2||"Crypto2",o=e.tradingMode||"SimpleSpot",i="".concat(s,"/").concat(t," ").concat(o),n=Array.from(this.sessions.values()).filter(e=>e.name.startsWith(i));if(0===n.length)return i;let a=0;return n.forEach(e=>{let s=e.name.match(new RegExp("^".concat(i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")," Session (\\d+)$")));if(s){let e=parseInt(s[1],10);e>a&&(a=e)}else e.name===i&&(a=Math.max(a,1))}),"".concat(i," Session ").concat(a+1)}async initializeBackendConnection(){if(this.isInitializing){console.log("⚠️ Backend initialization already in progress, skipping");return}this.isInitializing=!0;try{if(!localStorage.getItem("plutoAuthToken")){console.log("⚠️ No auth token found, using localStorage mode until login"),this.useBackend=!1,this.loadSessionsFromStorage();return}if((await fetch("http://localhost:5000/health/",{method:"GET",headers:{"Content-Type":"application/json"}})).ok)console.log("✅ Backend connection established, testing auth and loading sessions"),this.useBackend=!0,await this.loadSessionsFromBackend();else throw Error("Backend health check failed")}catch(e){console.log("⚠️ Backend not available, using localStorage mode:",e),this.useBackend=!1,this.loadSessionsFromStorage()}finally{this.isInitializing=!1}}async checkBackendConnection(){try{let e=localStorage.getItem("plutoAuthToken");if(!e)return!1;return(await fetch("http://localhost:5000/health/",{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}})).ok}catch(e){return console.log("⚠️ Backend connection check failed:",e),!1}}getWindowSpecificKey(e){return"".concat(e,"_").concat(this.windowId)}loadActiveSessionsFromStorage(){try{let e=localStorage.getItem(r);if(e){let s=JSON.parse(e);this.activeSessionsAcrossWindows=new Map(Object.entries(s));let t=Date.now();for(let[e,s]of this.activeSessionsAcrossWindows.entries())t-s.timestamp>3e5&&this.activeSessionsAcrossWindows.delete(e);this.saveActiveSessionsToStorage()}}catch(e){console.error("Failed to load active sessions from storage:",e)}}saveActiveSessionsToStorage(){try{let e=Object.fromEntries(this.activeSessionsAcrossWindows);localStorage.setItem(r,JSON.stringify(e)),window.dispatchEvent(new StorageEvent("storage",{key:r,newValue:JSON.stringify(e),storageArea:localStorage}))}catch(e){console.error("Failed to save active sessions to storage:",e)}}addActiveSession(e){if(!e||"undefined"===e||"null"===e){console.warn("⚠️ Attempted to add invalid session ID to active sessions:",e);return}let s="".concat(this.windowId,"_").concat(e);this.activeSessionsAcrossWindows.set(s,{sessionId:e,windowId:this.windowId,timestamp:Date.now()}),this.saveActiveSessionsToStorage()}removeActiveSession(e){let s="".concat(this.windowId,"_").concat(e);this.activeSessionsAcrossWindows.delete(s),this.saveActiveSessionsToStorage()}setupStorageListener(){window.addEventListener("storage",e=>{if(e.key===n&&e.newValue)try{let s=JSON.parse(e.newValue);this.sessions=new Map(Object.entries(s)),console.log("\uD83D\uDD04 Sessions synced from another window (".concat(this.sessions.size," sessions)"))}catch(e){console.error("Failed to sync sessions from storage event:",e)}})}handleAppRestart(){try{console.log("\uD83D\uDD04 Checking for app restart and cleaning up running sessions...");let e=localStorage.getItem("pluto_last_app_close"),s=Date.now();if(!e||s-parseInt(e)>3e5){console.log("\uD83D\uDD04 App restart detected - cleaning up running sessions");let e=Array.from(this.sessions.values()).filter(e=>e.isActive);if(e.length>0){console.log("\uD83D\uDED1 Found ".concat(e.length," running sessions to clean up:"),e.map(e=>({id:e.id,name:e.name}))),e.forEach(e=>{try{let s=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),t="".concat(e.name," (AutoSaved ").concat(s,")"),o=this.createNewSession(t,e.config);this.saveSession(o,e.config,e.targetPriceRows,e.orderHistory,e.currentMarketPrice,e.crypto1Balance,e.crypto2Balance,e.stablecoinBalance,!1),e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(e.id,e),console.log('✅ Auto-saved running session "'.concat(e.name,'" to past sessions as "').concat(t,'"'))}catch(s){console.error('❌ Failed to auto-save running session "'.concat(e.name,'":'),s),e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(e.id,e)}}),this.currentSessionId=null;let s=this.getWindowSpecificKey(a);localStorage.removeItem(s),sessionStorage.removeItem(a),this.saveSessionsToStorage(),console.log("✅ App restart cleanup completed - all running sessions moved to past sessions")}else console.log("✅ No running sessions found - no cleanup needed")}else console.log("✅ Normal app continuation - no cleanup needed");localStorage.setItem("pluto_app_start",s.toString())}catch(e){console.error("❌ Error during app restart cleanup:",e)}}cleanupStalePersistenceInfo(){try{console.log("\uD83E\uDDF9 Cleaning up stale persistence info...");let e=Date.now(),s=[];for(let t=0;t<localStorage.length;t++){let o=localStorage.key(t);if(o&&o.startsWith("pluto_session_persistence_"))try{let t=JSON.parse(localStorage.getItem(o)||"{}");t.lastSaved&&e-t.lastSaved>864e5&&s.push(o)}catch(e){s.push(o)}}s.forEach(e=>{localStorage.removeItem(e),console.log("\uD83D\uDDD1️ Removed stale persistence info: ".concat(e))}),s.length>0?console.log("✅ Cleaned up ".concat(s.length," stale persistence entries")):console.log("✅ No stale persistence info found")}catch(e){console.error("❌ Error cleaning up persistence info:",e)}}loadSessionsFromStorage(){try{let e=localStorage.getItem(n),s=null,t=this.getWindowSpecificKey(a);if((s=localStorage.getItem(t))||(s=sessionStorage.getItem(a)),!s&&e){let t=JSON.parse(e),o=Object.entries(t).filter(e=>{let[s,t]=e;return t.isActive&&t.lastModified&&Date.now()-t.lastModified<18e5});o.length>0&&(s=o.reduce((e,s)=>s[1].lastModified>e[1].lastModified?s:e)[0],console.log("\uD83D\uDD04 Restored most recent active session: ".concat(s)))}if(e){let s=JSON.parse(e);this.sessions=new Map(Object.entries(s))}if(this.currentSessionId=s,s){sessionStorage.setItem(a,s);let e=this.sessions.get(s);e&&e.isActive&&(this.addActiveSession(s),console.log("✅ Restored active session tracking for: ".concat(s)))}console.log("\uD83D\uDCC2 Loaded ".concat(this.sessions.size," shared sessions for window ").concat(this.windowId,", current: ").concat(s))}catch(e){console.error("Failed to load sessions from storage:",e)}}async loadSessionsFromBackend(){try{let e=localStorage.getItem("plutoAuthToken");if(!e||e.length<10){console.log("⚠️ Invalid or missing auth token, skipping backend session loading"),this.useBackend=!1,this.loadSessionsFromStorage();return}console.log("\uD83D\uDD04 Loading sessions from backend...");let{sessionApi:s}=await Promise.resolve().then(t.bind(t,5731)),o=await s.getAllSessions(!0);if(console.log("✅ Backend response received:",o),o&&o.sessions){this.sessions.clear(),o.sessions.forEach(e=>{let s={id:e.session_uuid,name:e.name,config:JSON.parse(e.config_snapshot||"{}"),createdAt:new Date(e.created_at).getTime(),lastModified:new Date(e.last_modified).getTime(),isActive:e.is_active,runtime:e.runtime||0,targetPriceRows:e.target_price_rows?JSON.parse(e.target_price_rows):[],orderHistory:e.order_history?JSON.parse(e.order_history):[],currentMarketPrice:e.current_market_price||1e5,crypto1Balance:e.crypto1_balance||1e4,crypto2Balance:e.crypto2_balance||1e4,stablecoinBalance:e.stablecoin_balance||1e4,alarmSettings:e.alarm_settings?JSON.parse(e.alarm_settings):void 0};this.sessions.set(e.session_uuid,s)});let e=o.sessions.find(e=>e.is_active);if(e){this.currentSessionId=e.session_uuid;let s=this.getWindowSpecificKey(a);localStorage.setItem(s,e.session_uuid),sessionStorage.setItem(a,e.session_uuid),console.log("✅ Restored active session from backend: ".concat(e.session_uuid))}this.saveSessionsToStorage(),console.log("\uD83D\uDCC2 Loaded ".concat(this.sessions.size," sessions from backend"))}}catch(s){let e=s instanceof Error?s.message:String(s);e.includes("Authentication")||e.includes("401")||e.includes("422")?(console.log("\uD83D\uDD10 Authentication issue detected, disabling backend mode"),this.useBackend=!1):e.includes("Cannot connect to server")?console.log("\uD83C\uDF10 Backend server not available, using local storage only"):console.warn("⚠️ Backend session loading failed, falling back to local storage:",e),this.loadSessionsFromStorage()}}saveSessionsToStorage(){try{let e=Object.fromEntries(this.sessions);localStorage.setItem(n,JSON.stringify(e));let s=this.getWindowSpecificKey(a);this.currentSessionId&&localStorage.setItem(s,this.currentSessionId),window.dispatchEvent(new StorageEvent("storage",{key:n,newValue:JSON.stringify(e),storageArea:localStorage}))}catch(e){console.error("Failed to save sessions to storage:",e)}}async createNewSessionWithAutoName(e,s,t){let o=s||this.generateSessionName(e);return this.createNewSession(o,e,t)}async createNewSession(e,s,t){let n=t||{crypto1:10,crypto2:1e5,stablecoin:0};if(this.useBackend)try{let t={name:e,config:s,targetPriceRows:[],currentMarketPrice:1e5,crypto1Balance:n.crypto1,crypto2Balance:n.crypto2,stablecoinBalance:n.stablecoin},o=await i.sessionApi.createSession(t),a={id:o.session.session_uuid,name:o.session.name,config:s,createdAt:new Date(o.session.created_at).getTime(),lastModified:new Date(o.session.last_modified).getTime(),isActive:o.session.is_active,runtime:o.session.runtime||0,targetPriceRows:[],orderHistory:[],currentMarketPrice:1e5,crypto1Balance:n.crypto1,crypto2Balance:n.crypto2,stablecoinBalance:n.stablecoin};return this.sessions.set(o.session.session_uuid,a),console.log("✅ Session created on backend:",o.session.session_uuid),o.session.session_uuid}catch(e){console.error("❌ Failed to create session on backend, falling back to localStorage:",e),this.useBackend=!1}let a=(0,o.A)(),r=Date.now(),c={id:a,name:e,config:s,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:n.crypto1,crypto2Balance:n.crypto2,stablecoinBalance:n.stablecoin,createdAt:r,lastModified:r,isActive:!1,runtime:0};return this.sessions.set(a,c),this.saveSessionsToStorage(),a}async saveSession(e,s,o,i,n,a,r,c){let l=arguments.length>8&&void 0!==arguments[8]&&arguments[8],d=arguments.length>9?arguments[9]:void 0;try{let g;let u=this.sessions.get(e);if(!u)return console.error("Session not found:",e),!1;let h={lastSaved:Date.now(),windowId:this.windowId,isActive:l,sessionId:e};if(void 0!==d)g=d,console.log("\uD83D\uDCCA Using override runtime: ".concat(g,"ms for session ").concat(e));else{g=u.runtime||0;let s=this.sessionStartTimes.get(e);s&&l?(g=(u.runtime||0)+(Date.now()-s),this.sessionStartTimes.set(e,Date.now())):!l&&s?(g=(u.runtime||0)+(Date.now()-s),this.sessionStartTimes.delete(e)):l&&!s&&this.sessionStartTimes.set(e,Date.now())}let S={...u,config:s,targetPriceRows:[...o],orderHistory:[...i],currentMarketPrice:n,crypto1Balance:a,crypto2Balance:r,stablecoinBalance:c,isActive:l,lastModified:Date.now(),runtime:g};if(localStorage.setItem("pluto_session_persistence_".concat(e),JSON.stringify(h)),console.log("\uD83D\uDCBE Saving session with complete data:",{sessionId:e,targetPriceRows:o.length,orderHistory:i.length,balances:{crypto1Balance:a,crypto2Balance:r,stablecoinBalance:c},isActive:l,runtime:g}),this.sessions.set(e,S),l?(this.addActiveSession(e),console.log("✅ Session marked as active and tracked: ".concat(e))):(this.removeActiveSession(e),console.log("⏹️ Session marked as inactive and removed from tracking: ".concat(e))),this.useBackend){let i=localStorage.getItem("plutoAuthToken");if(i&&i.length>10)try{let{sessionApi:i}=await Promise.resolve().then(t.bind(t,5731)),d={name:S.name,config:s,targetPriceRows:o,currentMarketPrice:n,crypto1Balance:a,crypto2Balance:r,stablecoinBalance:c,isActive:l,additionalRuntime:g};await i.updateSession(e,d),console.log("✅ Session saved to backend:",e)}catch(o){let t=o instanceof Error?o.message:String(o);if(console.warn("❌ Backend session save failed:",t),t.includes("Session not found")){console.log("\uD83D\uDD04 Session not found in backend, creating new session...");try{let t=await this.createNewSession(S.name,s,{crypto1:a,crypto2:r,stablecoin:c});return this.sessions.set(t,{...S,id:t}),this.sessions.delete(e),this.setCurrentSession(t),console.log("✅ Created new session to replace missing one:",t),!0}catch(e){console.error("❌ Failed to create replacement session:",e)}}(t.includes("401")||t.includes("422")||t.includes("Authentication")||t.includes("required"))&&(console.log("\uD83D\uDD10 Disabling backend mode due to authentication issue"),this.useBackend=!1)}else console.log("⚠️ Invalid or missing auth token, skipping backend session save"),this.useBackend=!1}return this.saveSessionsToStorage(),!0}catch(e){return console.error("Failed to save session:",e),!1}}loadSession(e){return this.sessions.get(e)||null}async deleteSession(e){if(this.useBackend){if(localStorage.getItem("plutoAuthToken"))try{await i.sessionApi.deleteSession(e),console.log("✅ Session deleted from backend:",e)}catch(s){let e=s instanceof Error?s.message:String(s);e.includes("401")||e.includes("422")||e.includes("Authentication")?console.log("\uD83D\uDD10 Authentication issue during session deletion, proceeding with local deletion"):console.error("❌ Failed to delete session from backend:",s)}else console.log("⚠️ No auth token, skipping backend session deletion")}let s=this.sessions.delete(e);if(s){if(this.currentSessionId===e){this.currentSessionId=null;let e=this.getWindowSpecificKey(a);localStorage.removeItem(e)}this.saveSessionsToStorage()}return s}getAllSessions(){return Array.from(this.sessions.values()).map(e=>({id:e.id,name:e.name,pair:"".concat(e.config.crypto1,"/").concat(e.config.crypto2),createdAt:e.createdAt,lastModified:e.lastModified,isActive:e.isActive,runtime:this.getCurrentRuntime(e.id),totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,s)=>e+(s.realizedProfitLossCrypto2||0),0)}))}getCurrentActiveSessions(){let e=new Set(Array.from(this.activeSessionsAcrossWindows.values()).map(e=>e.sessionId).filter(e=>e&&"undefined"!==e));this.currentSessionId&&this.sessions.has(this.currentSessionId)&&e.add(this.currentSessionId),console.log("\uD83D\uDD0D getCurrentActiveSessions debug:",{activeSessionsAcrossWindows:Array.from(this.activeSessionsAcrossWindows.entries()),activeSessionIds:Array.from(e),allSessions:Array.from(this.sessions.keys()),currentSessionId:this.currentSessionId,windowId:this.windowId});let s=Array.from(this.sessions.values()).filter(s=>{let t=s.id&&e.has(s.id),o=s.isActive,i=s.id===this.currentSessionId;return console.log("\uD83D\uDD0D Session ".concat(s.id," (").concat(s.name,"):"),{isInActiveTracking:t,isMarkedActive:o,isCurrentSession:i,shouldInclude:t||o||i}),t||o||i}).map(e=>({id:e.id,name:e.name,pair:"".concat(e.config.crypto1,"/").concat(e.config.crypto2),createdAt:e.createdAt,lastModified:e.lastModified,isActive:!0,runtime:this.getCurrentRuntime(e.id),totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,s)=>e+(s.realizedProfitLossCrypto2||0),0)})).filter(e=>e.id);return console.log("\uD83D\uDD0D Returning active sessions:",s.length,s.map(e=>({id:e.id,name:e.name}))),s}setCurrentSession(e){if(this.sessions.has(e)){this.currentSessionId=e;let s=this.getWindowSpecificKey(a);localStorage.setItem(s,e),sessionStorage.setItem(a,e);let t=this.sessions.get(e);t&&(t.isActive=!0,t.lastModified=Date.now(),this.sessions.set(e,t),this.saveSessionsToStorage(),this.addActiveSession(e),console.log("✅ Session ".concat(e," marked as active for window ").concat(this.windowId)),window.dispatchEvent(new StorageEvent("storage",{key:"pluto_current_session",newValue:e,storageArea:localStorage})))}}getCurrentSessionId(){return this.currentSessionId}clearCurrentSession(){if(this.currentSessionId){if(this.removeActiveSession(this.currentSessionId),Array.from(this.activeSessionsAcrossWindows.values()).some(e=>e.sessionId===this.currentSessionId))console.log("\uD83D\uDD04 Session ".concat(this.currentSessionId," still active in other windows"));else{let e=this.sessions.get(this.currentSessionId);e&&e.isActive&&(e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage(),console.log("⏹️ Session ".concat(this.currentSessionId," marked as inactive (no other windows using it)")))}}this.currentSessionId=null;{let e=this.getWindowSpecificKey(a);localStorage.removeItem(e),sessionStorage.removeItem(a)}console.log("\uD83D\uDDD1️ Cleared current session for window ".concat(this.windowId))}startSessionRuntime(e){this.sessionStartTimes.set(e,Date.now())}stopSessionRuntime(e){let s=this.sessionStartTimes.get(e);if(s){let t=this.sessions.get(e);if(t){let o=Date.now()-s;t.runtime=(t.runtime||0)+o,t.lastModified=Date.now(),this.sessions.set(e,t),this.saveSessionsToStorage()}this.sessionStartTimes.delete(e)}}deactivateSession(e){let s=this.sessions.get(e);s&&s.isActive&&(s.isActive=!1,s.lastModified=Date.now(),this.sessions.set(e,s),this.saveSessionsToStorage(),console.log("⏹️ Session ".concat(e," deactivated")))}getCurrentRuntime(e){let s=this.sessions.get(e);if(!s)return 0;let t=this.sessionStartTimes.get(e);return t?(s.runtime||0)+(Date.now()-t):s.runtime||0}async refreshBackendConnection(){console.log("\uD83D\uDD04 Refreshing backend connection..."),await this.initializeBackendConnection()}disableBackendMode(){console.log("\uD83D\uDD10 Disabling backend mode due to authentication issues"),this.useBackend=!1,this.isInitializing=!1}handleLogout(){console.log("\uD83D\uDC4B User logged out, switching to localStorage mode"),this.useBackend=!1,this.sessions.clear(),this.currentSessionId=null,this.loadSessionsFromStorage()}exportSessionToJSON(e){let s=this.sessions.get(e);return s?JSON.stringify(s,null,2):null}importSessionFromJSON(e){try{let s=JSON.parse(e),t=(0,o.A)(),i={...s,id:t,isActive:!1,lastModified:Date.now()};return this.sessions.set(t,i),this.saveSessionsToStorage(),t}catch(e){return console.error("Failed to import session:",e),null}}renameSession(e,s){let t=this.sessions.get(e);return!!t&&(t.name=s,t.lastModified=Date.now(),this.sessions.set(e,t),this.saveSessionsToStorage(),!0)}async updateSessionAlarmSettings(e,s){let t=this.sessions.get(e);if(!t)return!1;if(t.alarmSettings=s,t.lastModified=Date.now(),this.sessions.set(e,t),this.useBackend)try{let o={name:t.name,config:t.config,targetPriceRows:t.targetPriceRows,currentMarketPrice:t.currentMarketPrice,crypto1Balance:t.crypto1Balance,crypto2Balance:t.crypto2Balance,stablecoinBalance:t.stablecoinBalance,isActive:t.isActive,alarm_settings:s};await i.sessionApi.updateSession(e,o),console.log("✅ Session alarm settings saved to backend:",e)}catch(e){console.error("❌ Failed to save session alarm settings to backend:",e)}return this.saveSessionsToStorage(),!0}getSessionHistory(e){let s=this.sessions.get(e);return s?[...s.orderHistory]:[]}exportSessionToCSV(e){let s=this.sessions.get(e);return s?["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...s.orderHistory.map(e=>{var t,o,i,n,a,r,c;return[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,(null===(t=e.amountCrypto1)||void 0===t?void 0:t.toFixed(s.config.numDigits))||"",(null===(o=e.avgPrice)||void 0===o?void 0:o.toFixed(s.config.numDigits))||"",(null===(i=e.valueCrypto2)||void 0===i?void 0:i.toFixed(s.config.numDigits))||"",(null===(n=e.price1)||void 0===n?void 0:n.toFixed(s.config.numDigits))||"",e.crypto1Symbol,(null===(a=e.price2)||void 0===a?void 0:a.toFixed(s.config.numDigits))||"",e.crypto2Symbol,(null===(r=e.realizedProfitLossCrypto1)||void 0===r?void 0:r.toFixed(s.config.numDigits))||"",(null===(c=e.realizedProfitLossCrypto2)||void 0===c?void 0:c.toFixed(s.config.numDigits))||""].join(",")})].join("\n"):null}clearAllSessions(){this.sessions.clear(),this.currentSessionId=null,localStorage.removeItem(n);let e=this.getWindowSpecificKey(a);localStorage.removeItem(e)}enableAutoSave(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,o=setInterval(()=>{let t=s();this.saveSession(e,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,t.isActive)},t);return()=>clearInterval(o)}constructor(){this.sessions=new Map,this.currentSessionId=null,this.useBackend=!0,this.isInitializing=!1,this.sessionStartTimes=new Map,this.activeSessionsAcrossWindows=new Map,this.windowId=l(),console.log("\uD83E\uDE9F SessionManager initialized for window: ".concat(this.windowId)),this.sessionStartTimes.clear(),this.useBackend=!0,this.loadSessionsFromStorage(),this.loadActiveSessionsFromStorage(),this.setupStorageListener(),this.handleAppRestart(),this.cleanupStalePersistenceInfo(),setTimeout(()=>{this.initializeBackendConnection()},1e3),console.log("\uD83E\uDE9F SessionManager initialized for window ".concat(this.windowId))}}}}]);