"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[737],{9737:(t,r,e)=>{let n;e.d(r,{A:()=>p});let o={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},u=new Uint8Array(16),d=[];for(let t=0;t<256;++t)d.push((t+256).toString(16).slice(1));let p=function(t,r,e){if(o.randomUUID&&!r&&!t)return o.randomUUID();let p=(t=t||{}).random||(t.rng||function(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(u)})();if(p[6]=15&p[6]|64,p[8]=63&p[8]|128,r){e=e||0;for(let t=0;t<16;++t)r[e+t]=p[t];return r}return function(t,r=0){return d[t[r+0]]+d[t[r+1]]+d[t[r+2]]+d[t[r+3]]+"-"+d[t[r+4]]+d[t[r+5]]+"-"+d[t[r+6]]+d[t[r+7]]+"-"+d[t[r+8]]+d[t[r+9]]+"-"+d[t[r+10]]+d[t[r+11]]+d[t[r+12]]+d[t[r+13]]+d[t[r+14]]+d[t[r+15]]}(p)}}}]);