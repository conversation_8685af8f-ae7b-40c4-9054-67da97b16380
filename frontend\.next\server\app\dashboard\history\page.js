(()=>{var e={};e.id=610,e.ids=[610],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7252:e=>{"use strict";e.exports=require("express")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19185:e=>{"use strict";e.exports=require("dgram")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37830:e=>{"use strict";e.exports=require("node:stream/web")},44708:e=>{"use strict";e.exports=require("node:https")},54379:e=>{"use strict";e.exports=require("node:path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57207:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68291:(e,t,r)=>{Promise.resolve().then(r.bind(r,72967))},72967:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>et});var n=r(60687),a=r(43210),i=r.n(a),s=r(22649),o=r(44493),d=r(29523),u=r(15079),l=r(96834),c=r(39907),h=r(78895),m=r(5551),p=r(29867),f=r(57207);let g=(0,r(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var x=r(15036);let y={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function b(e){return (t={})=>{let r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let w={date:b({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:b({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:b({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},v={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function j(e){return(t,r)=>{let n;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,a=r?.width?String(r.width):t;n=e.formattingValues[a]||e.formattingValues[t]}else{let t=e.defaultWidth,a=r?.width?String(r.width):e.defaultWidth;n=e.values[a]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}function k(e){return(t,r={})=>{let n;let a=r.width,i=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],s=t.match(i);if(!s)return null;let o=s[0],d=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(d)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(d,e=>e.test(o)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(d,e=>e.test(o));return n=e.valueCallback?e.valueCallback(u):u,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(o.length)}}}let P={code:"en-US",formatDistance:(e,t,r)=>{let n;let a=y[e];return(n="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",t.toString()),r?.addSuffix)?r.comparison&&r.comparison>0?"in "+n:n+" ago":n},formatLong:w,formatRelative:(e,t,r,n)=>v[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:j({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:j({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:j({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:j({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:j({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,r={})=>{let n=t.match(e.matchPattern);if(!n)return null;let a=n[0],i=t.match(e.parsePattern);if(!i)return null;let s=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:s=r.valueCallback?r.valueCallback(s):s,rest:t.slice(a.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:k({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:k({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:k({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:k({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:k({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},M={};function C(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}function S(e){let t=C(e);return t.setHours(0,0,0,0),t}function N(e){let t=C(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),+e-+r}function q(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}function T(e,t){let r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??M.weekStartsOn??M.locale?.options?.weekStartsOn??0,n=C(e),a=n.getDay();return n.setDate(n.getDate()-(7*(a<r)+a-r)),n.setHours(0,0,0,0),n}function D(e){return T(e,{weekStartsOn:1})}function W(e){let t=C(e),r=t.getFullYear(),n=q(e,0);n.setFullYear(r+1,0,4),n.setHours(0,0,0,0);let a=D(n),i=q(e,0);i.setFullYear(r,0,4),i.setHours(0,0,0,0);let s=D(i);return t.getTime()>=a.getTime()?r+1:t.getTime()>=s.getTime()?r:r-1}function E(e,t){let r=C(e),n=r.getFullYear(),a=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??M.firstWeekContainsDate??M.locale?.options?.firstWeekContainsDate??1,i=q(e,0);i.setFullYear(n+1,0,a),i.setHours(0,0,0,0);let s=T(i,t),o=q(e,0);o.setFullYear(n,0,a),o.setHours(0,0,0,0);let d=T(o,t);return r.getTime()>=s.getTime()?n+1:r.getTime()>=d.getTime()?n:n-1}function L(e,t){let r=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+r}let z={y(e,t){let r=e.getFullYear(),n=r>0?r:1-r;return L("yy"===t?n%100:n,t.length)},M(e,t){let r=e.getMonth();return"M"===t?String(r+1):L(r+1,2)},d:(e,t)=>L(e.getDate(),t.length),a(e,t){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>L(e.getHours()%12||12,t.length),H:(e,t)=>L(e.getHours(),t.length),m:(e,t)=>L(e.getMinutes(),t.length),s:(e,t)=>L(e.getSeconds(),t.length),S(e,t){let r=t.length;return L(Math.trunc(e.getMilliseconds()*Math.pow(10,r-3)),t.length)}},O={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},F={G:function(e,t,r){let n=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});default:return r.era(n,{width:"wide"})}},y:function(e,t,r){if("yo"===t){let t=e.getFullYear();return r.ordinalNumber(t>0?t:1-t,{unit:"year"})}return z.y(e,t)},Y:function(e,t,r,n){let a=E(e,n),i=a>0?a:1-a;return"YY"===t?L(i%100,2):"Yo"===t?r.ordinalNumber(i,{unit:"year"}):L(i,t.length)},R:function(e,t){return L(W(e),t.length)},u:function(e,t){return L(e.getFullYear(),t.length)},Q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return L(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return L(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,r){let n=e.getMonth();switch(t){case"M":case"MM":return z.M(e,t);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,r){let n=e.getMonth();switch(t){case"L":return String(n+1);case"LL":return L(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,r,n){let a=function(e,t){let r=C(e);return Math.round((+T(r,t)-+function(e,t){let r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??M.firstWeekContainsDate??M.locale?.options?.firstWeekContainsDate??1,n=E(e,t),a=q(e,0);return a.setFullYear(n,0,r),a.setHours(0,0,0,0),T(a,t)}(r,t))/6048e5)+1}(e,n);return"wo"===t?r.ordinalNumber(a,{unit:"week"}):L(a,t.length)},I:function(e,t,r){let n=function(e){let t=C(e);return Math.round((+D(t)-+function(e){let t=W(e),r=q(e,0);return r.setFullYear(t,0,4),r.setHours(0,0,0,0),D(r)}(t))/6048e5)+1}(e);return"Io"===t?r.ordinalNumber(n,{unit:"week"}):L(n,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):z.d(e,t)},D:function(e,t,r){let n=function(e){let t=C(e);return function(e,t){let r=S(e),n=S(t);return Math.round((+r-N(r)-(+n-N(n)))/864e5)}(t,function(e){let t=C(e),r=q(e,0);return r.setFullYear(t.getFullYear(),0,1),r.setHours(0,0,0,0),r}(t))+1}(e);return"Do"===t?r.ordinalNumber(n,{unit:"dayOfYear"}):L(n,t.length)},E:function(e,t,r){let n=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,r,n){let a=e.getDay(),i=(a-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return L(i,2);case"eo":return r.ordinalNumber(i,{unit:"day"});case"eee":return r.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(a,{width:"short",context:"formatting"});default:return r.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,r,n){let a=e.getDay(),i=(a-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return L(i,t.length);case"co":return r.ordinalNumber(i,{unit:"day"});case"ccc":return r.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(a,{width:"narrow",context:"standalone"});case"cccccc":return r.day(a,{width:"short",context:"standalone"});default:return r.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,r){let n=e.getDay(),a=0===n?7:n;switch(t){case"i":return String(a);case"ii":return L(a,t.length);case"io":return r.ordinalNumber(a,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,r){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},b:function(e,t,r){let n;let a=e.getHours();switch(n=12===a?O.noon:0===a?O.midnight:a/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},B:function(e,t,r){let n;let a=e.getHours();switch(n=a>=17?O.evening:a>=12?O.afternoon:a>=4?O.morning:O.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return z.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):z.H(e,t)},K:function(e,t,r){let n=e.getHours()%12;return"Ko"===t?r.ordinalNumber(n,{unit:"hour"}):L(n,t.length)},k:function(e,t,r){let n=e.getHours();return(0===n&&(n=24),"ko"===t)?r.ordinalNumber(n,{unit:"hour"}):L(n,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):z.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):z.s(e,t)},S:function(e,t){return z.S(e,t)},X:function(e,t,r){let n=e.getTimezoneOffset();if(0===n)return"Z";switch(t){case"X":return Y(n);case"XXXX":case"XX":return A(n);default:return A(n,":")}},x:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"x":return Y(n);case"xxxx":case"xx":return A(n);default:return A(n,":")}},O:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+H(n,":");default:return"GMT"+A(n,":")}},z:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+H(n,":");default:return"GMT"+A(n,":")}},t:function(e,t,r){return L(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,r){return L(e.getTime(),t.length)}};function H(e,t=""){let r=e>0?"-":"+",n=Math.abs(e),a=Math.trunc(n/60),i=n%60;return 0===i?r+String(a):r+String(a)+t+L(i,2)}function Y(e,t){return e%60==0?(e>0?"-":"+")+L(Math.abs(e)/60,2):A(e,t)}function A(e,t=""){let r=Math.abs(e);return(e>0?"-":"+")+L(Math.trunc(r/60),2)+t+L(r%60,2)}let _=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},$=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},B={p:$,P:(e,t)=>{let r;let n=e.match(/(P+)(p+)?/)||[],a=n[1],i=n[2];if(!i)return _(e,t);switch(a){case"P":r=t.dateTime({width:"short"});break;case"PP":r=t.dateTime({width:"medium"});break;case"PPP":r=t.dateTime({width:"long"});break;default:r=t.dateTime({width:"full"})}return r.replace("{{date}}",_(a,t)).replace("{{time}}",$(i,t))}},G=/^D+$/,Q=/^Y+$/,X=["D","DD","YY","YYYY"],R=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,V=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,I=/^'([^]*?)'?$/,U=/''/g,J=/[a-zA-Z]/;function Z(e,t,r){let n=r?.locale??M.locale??P,a=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??M.firstWeekContainsDate??M.locale?.options?.firstWeekContainsDate??1,i=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??M.weekStartsOn??M.locale?.options?.weekStartsOn??0,s=C(e);if(!((s instanceof Date||"object"==typeof s&&"[object Date]"===Object.prototype.toString.call(s)||"number"==typeof s)&&!isNaN(Number(C(s)))))throw RangeError("Invalid time value");let o=t.match(V).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,B[t])(e,n.formatLong):e}).join("").match(R).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(I);return t?t[1].replace(U,"'"):e}(e)};if(F[t])return{isToken:!0,value:e};if(t.match(J))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});n.localize.preprocessor&&(o=n.localize.preprocessor(s,o));let d={firstWeekContainsDate:a,weekStartsOn:i,locale:n};return o.map(a=>{if(!a.isToken)return a.value;let i=a.value;return(!r?.useAdditionalWeekYearTokens&&Q.test(i)||!r?.useAdditionalDayOfYearTokens&&G.test(i))&&!function(e,t,r){let n=function(e,t,r){let n="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${n} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,r);if(console.warn(n),X.includes(e))throw RangeError(n)}(i,t,String(e)),(0,F[i[0]])(s,i,n.localize,d)}).join("")}function K(){let{dispatch:e,orderHistory:t,config:r}=(0,h.U)(),{toast:s}=(0,p.dj)(),[x,y]=(0,a.useState)([]),[b,w]=(0,a.useState)("current"),[v,j]=(0,a.useState)([]),k=m.SessionManager.getInstance(),P="current"===b?{name:"Current Session",pair:`${r.crypto1}/${r.crypto2}`,totalTrades:t.length,totalProfitLoss:t.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),lastModified:Date.now(),isActive:!0}:x.find(e=>e.id===b);return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{className:"text-xl font-bold text-primary",children:"Session History"}),(0,n.jsx)(o.BT,{children:"View trading history for current and past sessions."})]}),(0,n.jsxs)(o.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,n.jsxs)(u.l6,{value:b,onValueChange:w,children:[(0,n.jsx)(u.bq,{className:"w-full sm:w-[300px]",children:(0,n.jsx)(u.yv,{placeholder:"Select a session"})}),(0,n.jsxs)(u.gC,{children:[(0,n.jsx)(u.eb,{value:"current",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(l.E,{variant:"default",className:"text-xs",children:"Current"}),(0,n.jsxs)("span",{children:["Current Session (",r.crypto1&&r.crypto2?`${r.crypto1}/${r.crypto2}`:"Crypto 1/Crypto 2 = 0",")"]})]})}),x.length>0&&(0,n.jsxs)(i().Fragment,{children:[(0,n.jsx)(c.w,{className:"my-1"}),x.map(e=>(0,n.jsx)(u.eb,{value:e.id,children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(l.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,n.jsx)("span",{children:e.name})]})},e.id))]},"sessions-list")]})]})]}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{"current"===b?(e({type:"CLEAR_ORDER_HISTORY"}),s({title:"History Cleared",description:"Current session trade history has been cleared."})):s({title:"Cannot Clear",description:"Cannot clear history for past sessions. Use current session to clear history.",variant:"destructive"})},className:"btn-outline-neo",disabled:"current"!==b,children:[(0,n.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Clear History"]}),(0,n.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{let e,t;if(0===v.length){s({title:"No Data to Export",description:"There is no trade history to export for the selected session.",variant:"destructive"});return}if("current"===b)e=["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...v.map(e=>[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,e.amountCrypto1?.toFixed(r.numDigits)||"",e.avgPrice?.toFixed(r.numDigits)||"",e.valueCrypto2?.toFixed(r.numDigits)||"",e.price1?.toFixed(r.numDigits)||"",e.crypto1Symbol,e.price2?.toFixed(r.numDigits)||"",e.crypto2Symbol,e.realizedProfitLossCrypto1?.toFixed(r.numDigits)||"",e.realizedProfitLossCrypto2?.toFixed(r.numDigits)||""].join(","))].join("\n"),t=`current_session_history_${new Date().toISOString().split("T")[0]}.csv`;else{e=k.exportSessionToCSV(b)||"";let r=k.loadSession(b);t=`${r?.name||"session"}_${new Date().toISOString().split("T")[0]}.csv`}if(!e){s({title:"Export Failed",description:"Failed to generate CSV content.",variant:"destructive"});return}let n=new Blob([e],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),i=URL.createObjectURL(n);a.setAttribute("href",i),a.setAttribute("download",t),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a),s({title:"Export Complete",description:"Trade history has been exported to CSV file."})},className:"btn-outline-neo",children:[(0,n.jsx)(g,{className:"mr-2 h-4 w-4"}),"Export"]})]})]}),P&&(0,n.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Session:"}),(0,n.jsx)("div",{className:"font-medium",children:P.name})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Pair:"}),(0,n.jsx)("div",{className:"font-medium",children:P.pair})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Total Trades:"}),(0,n.jsx)("div",{className:"font-medium",children:P.totalTrades})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"Total P/L:"}),(0,n.jsx)("div",{className:`font-medium ${P.totalProfitLoss>=0?"text-green-600":"text-red-600"}`,children:P.totalProfitLoss.toFixed(4)})]})]}),"current"!==b&&(0,n.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Last modified: ",Z(new Date(P.lastModified),"MMM dd, yyyy HH:mm")]})]})]})]}),(0,n.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsxs)(o.ZB,{className:"text-lg font-bold text-primary",children:["Trade History - ",P?.name||"Unknown Session"]}),(0,n.jsx)(o.BT,{children:0===v.length?"No trades recorded for this session yet.":`Showing ${v.length} trades for the selected session.`})]}),(0,n.jsx)(o.Wu,{children:(0,n.jsx)(ee,{history:v,config:r})})]})]})}function ee({history:e,config:t}){let r=e=>e?.toFixed(t.numDigits)??"-",a=[{key:"date",label:"Date"},{key:"hour",label:"Hour"},{key:"pair",label:"Couple"},{key:"crypto",label:`Crypto (${t.crypto1})`},{key:"orderType",label:"Order Type"},{key:"amount",label:"Amount"},{key:"avgPrice",label:"Avg Price"},{key:"value",label:`Value (${t.crypto2})`},{key:"price1",label:"Price 1"},{key:"crypto1Symbol",label:`Crypto (${t.crypto1})`},{key:"price2",label:"Price 2"},{key:"crypto2Symbol",label:`Crypto (${t.crypto2})`}];return 0===e.length?(0,n.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,n.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,n.jsx)("p",{children:"No trading history for this session yet."})]}):(0,n.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"min-w-full",children:[(0,n.jsx)("thead",{children:(0,n.jsx)("tr",{className:"bg-card hover:bg-card border-b",children:a.map(e=>(0,n.jsx)("th",{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left",children:e.label},e.key))})}),(0,n.jsx)("tbody",{children:e.map(e=>(0,n.jsxs)("tr",{className:"hover:bg-card/80 border-b",children:[(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:Z(new Date(e.timestamp),"yyyy-MM-dd")}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:Z(new Date(e.timestamp),"HH:mm:ss")}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:e.pair}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,n.jsx)("td",{className:`px-3 py-2 text-xs font-semibold ${"BUY"===e.orderType?"text-green-400":"text-destructive"}`,children:e.orderType}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.amountCrypto1)}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.avgPrice)}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.valueCrypto2)}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.price1)}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.price2)??"-"}),(0,n.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto2Symbol}),(0,n.jsx)("td",{className:`px-3 py-2 text-xs ${e.realizedProfitLossCrypto1&&e.realizedProfitLossCrypto1>0?"text-green-400":e.realizedProfitLossCrypto1&&e.realizedProfitLossCrypto1<0?"text-destructive":""}`,children:r(e.realizedProfitLossCrypto1)}),(0,n.jsx)("td",{className:`px-3 py-2 text-xs ${e.realizedProfitLossCrypto2&&e.realizedProfitLossCrypto2>0?"text-green-400":e.realizedProfitLossCrypto2&&e.realizedProfitLossCrypto2<0?"text-destructive":""}`,children:r(e.realizedProfitLossCrypto2)})]},e.id))})]})})})}function et(){return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)(s.A,{}),(0,n.jsx)(K,{})]})}},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74896:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>h,tree:()=>u});var n=r(65239),a=r(48088),i=r(88170),s=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let u={children:["",{children:["dashboard",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90540)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/history/page",pathname:"/dashboard/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},74998:e=>{"use strict";e.exports=require("perf_hooks")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},90540:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},92267:(e,t,r)=>{Promise.resolve().then(r.bind(r,90540))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[585,301,941,973,991],()=>r(74896));module.exports=n})();