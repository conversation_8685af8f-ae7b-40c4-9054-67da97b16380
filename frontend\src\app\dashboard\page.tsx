"use client";
import React, { useState, useEffect } from 'react';
import OrdersTable from '@/components/dashboard/OrdersTable';
import DashboardTabs from '@/components/dashboard/DashboardTabs';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTradingContext } from '@/contexts/TradingContext';
import MarketPriceDisplay from '@/components/dashboard/MarketPriceDisplay';
import { SessionManager } from '@/lib/session-manager';

import { useToast } from '@/hooks/use-toast';

export default function DashboardOrdersPage() {
  const { config, saveCurrentSession, targetPriceRows, orderHistory } = useTradingContext();
  const { toast } = useToast();
  const [currentSessionName, setCurrentSessionName] = useState<string>('');
  const sessionManager = SessionManager.getInstance();

  useEffect(() => {
    const updateSessionName = () => {
      const currentSessionId = sessionManager.getCurrentSessionId();
      if (currentSessionId) {
        const session = sessionManager.loadSession(currentSessionId);
        if (session) {
          setCurrentSessionName(session.name);
          return;
        }
      }

      // No session or session not found, generate default name
      if (config.crypto1 && config.crypto2) {
        const defaultName = `${config.crypto1}/${config.crypto2} ${config.tradingMode || 'SimpleSpot'}`;
        setCurrentSessionName(defaultName);
      } else {
        setCurrentSessionName('Crypto 1/Crypto 2 = 0');
      }
    };

    updateSessionName();
  }, [config.crypto1, config.crypto2, config.tradingMode, sessionManager, targetPriceRows.length, orderHistory.length]);

  // Show placeholder text when cryptos are not selected
  const displayTitle = currentSessionName || ((config.crypto1 && config.crypto2)
    ? `${config.crypto1}/${config.crypto2} ${config.tradingMode || 'SimpleSpot'}`
    : "Crypto 1/Crypto 2 = 0");



  return (
    <div className="space-y-6">
      <DashboardTabs />
      <Card className="border-2 border-border">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-primary">Active Orders ({displayTitle})</CardTitle>
          <CardDescription>Current state of your target price levels. Prices update in real-time.</CardDescription>
        </CardHeader>
        <CardContent>
          <MarketPriceDisplay />
          <OrdersTable />
        </CardContent>
      </Card>
    </div>
  );
}
