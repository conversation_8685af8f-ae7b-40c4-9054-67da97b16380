"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Save,
  FolderOpen,
  Trash2,
  Download,
  Upload,
  Edit2,
  Clock,
  TrendingUp,
  Activity,
  FileText,
  Volume2,
  RefreshCw
} from 'lucide-react';
import { SessionManager as SessionManagerService } from '@/lib/session-manager';
import { useTradingContext } from '@/contexts/TradingContext';
import { useToast } from '@/hooks/use-toast';
import { SessionAlarmConfigModal } from '@/components/modals/SessionAlarmConfigModal';
import type { AppSettings } from '@/lib/types';
import type { SessionMetadata } from '@/lib/types';
import { format } from 'date-fns';

export function SessionManager() {
  const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = useTradingContext();
  const { toast } = useToast();
  const [sessions, setSessions] = useState<SessionMetadata[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [currentRuntime, setCurrentRuntime] = useState<number>(0);
  const [alarmModalOpen, setAlarmModalOpen] = useState(false);
  const [selectedSessionForAlarm, setSelectedSessionForAlarm] = useState<{ id: string; name: string; alarmSettings?: AppSettings } | null>(null);
  const sessionManager = SessionManagerService.getInstance();

  useEffect(() => {
    loadSessions();
    const sessionId = sessionManager.getCurrentSessionId();
    setCurrentSessionId(sessionId);

    console.log('🔄 Admin Panel - Initial load:', {
      sessionId,
      totalSessions: sessionManager.getAllSessions().length,
      activeSessions: sessionManager.getCurrentActiveSessions().length
    });

    // Initialize current runtime
    if (sessionId) {
      const runtime = sessionManager.getCurrentRuntime(sessionId);
      setCurrentRuntime(runtime);
    }

    // Listen for storage changes to sync sessions across windows
    const handleStorageChange = (event: StorageEvent) => {
      console.log('🔄 Admin Panel - Storage change detected:', {
        key: event.key,
        hasNewValue: !!event.newValue
      });

      if (event.key === 'trading_sessions' && event.newValue) {
        loadSessions();
        console.log('🔄 Sessions synced from another window');
      }

      // Listen for active session changes
      if (event.key?.includes('active_sessions')) {
        loadSessions();
        console.log('🔄 Active sessions updated from another window');
      }

      // Listen for current session changes (when bot starts/stops)
      if (event.key?.includes('current_session')) {
        const newSessionId = sessionManager.getCurrentSessionId();
        setCurrentSessionId(newSessionId);
        loadSessions();
        console.log('🔄 Current session changed:', newSessionId);
      }

      // Listen for any session-related storage changes
      if (event.key?.includes('pluto_')) {
        loadSessions();
        console.log('🔄 Pluto storage updated, refreshing sessions');
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Update runtime display and refresh sessions for active sessions
  useEffect(() => {
    const interval = setInterval(() => {
      // Update current session runtime
      if (currentSessionId) {
        const runtime = sessionManager.getCurrentRuntime(currentSessionId);
        setCurrentRuntime(runtime);
      }

      // Refresh sessions to update active status and runtime displays
      loadSessions();
    }, 2000); // Update every 2 seconds for more responsive admin panel

    return () => clearInterval(interval);
  }, [currentSessionId, sessionManager]);

  const loadSessions = () => {
    const allSessions = sessionManager.getAllSessions();
    console.log('🔄 Admin Panel - Loading sessions:', {
      totalSessions: allSessions.length,
      currentSessionId: sessionManager.getCurrentSessionId(),
      sessions: allSessions.map(s => ({
        id: s.id,
        name: s.name,
        isActive: s.isActive
      }))
    });
    setSessions(allSessions.sort((a, b) => b.lastModified - a.lastModified));
  };



  const handleSaveCurrentSession = async () => {
    if (!currentSessionId) {
      toast({
        title: "Error",
        description: "No active session to save",
        variant: "destructive"
      });
      return;
    }

    try {
      // Check if there's already a saved version of this session
      const currentSession = sessionManager.loadSession(currentSessionId);
      if (!currentSession) {
        toast({
          title: "Error",
          description: "Current session not found",
          variant: "destructive"
        });
        return;
      }

      // Look for existing saved session with same base name (only manually saved ones)
      const allSessions = sessionManager.getAllSessions();
      const baseName = currentSession.name.replace(/ \((Saved|AutoSaved).*\)$/, ''); // Remove existing timestamp
      const existingSavedSession = allSessions.find(s =>
        s.id !== currentSessionId &&
        s.name.startsWith(baseName) &&
        s.name.includes('(Saved') && // Only look for manually saved sessions
        !s.isActive // Only look in inactive sessions
      );

      let targetSessionId: string;
      let savedName: string;

      if (existingSavedSession) {
        // Update existing saved session - update the timestamp to show latest save
        targetSessionId = existingSavedSession.id;
        const timestamp = new Date().toLocaleString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
        savedName = `${baseName} (Saved ${timestamp})`;
        console.log(`📝 Updating existing saved session: ${savedName}`);
      } else {
        // Create new saved session with timestamp
        const timestamp = new Date().toLocaleString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
        savedName = `${baseName} (Saved ${timestamp})`;
        targetSessionId = await sessionManager.createNewSession(savedName, config);
        console.log(`💾 Creating new saved session: ${savedName}`);
      }

      // Get current runtime from the active session
      const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);

      // Update the session name if it's an existing session
      if (existingSavedSession) {
        sessionManager.renameSession(targetSessionId, savedName);
      }

      // Save/update the session with current data and runtime
      const success = await sessionManager.saveSession(
        targetSessionId,
        config,
        targetPriceRows,
        orderHistory,
        currentMarketPrice,
        crypto1Balance,
        crypto2Balance,
        stablecoinBalance,
        false, // Saved session is not active
        currentRuntime // Pass the current runtime to the saved session
      );

      // DO NOT deactivate the current session - keep it running!
      // The current session should remain active for continued trading

      if (success) {
        loadSessions();
        toast({
          title: "Session Saved",
          description: existingSavedSession
            ? `Save checkpoint updated (Runtime: ${formatRuntime(currentRuntime)})`
            : `Session saved as checkpoint (Runtime: ${formatRuntime(currentRuntime)})`,
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to save session",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error saving session:', error);
      toast({
        title: "Error",
        description: "Failed to save session",
        variant: "destructive"
      });
    }
  };

  const handleLoadSession = (sessionId: string) => {
    const session = sessionManager.loadSession(sessionId);
    if (!session) {
      toast({
        title: "Error",
        description: "Failed to load session",
        variant: "destructive"
      });
      return;
    }

    console.log('🔄 Loading session with full data:', {
      sessionId,
      name: session.name,
      config: session.config,
      targetPriceRows: session.targetPriceRows.length,
      orderHistory: session.orderHistory.length,
      balances: {
        crypto1: session.crypto1Balance,
        crypto2: session.crypto2Balance,
        stablecoin: session.stablecoinBalance
      },
      isActive: session.isActive,
      runtime: session.runtime
    });

    // Load session data into context
    dispatch({ type: 'SET_CONFIG', payload: session.config });
    dispatch({ type: 'SET_TARGET_PRICE_ROWS', payload: session.targetPriceRows });
    dispatch({ type: 'CLEAR_ORDER_HISTORY' });
    session.orderHistory.forEach(entry => {
      dispatch({ type: 'ADD_ORDER_HISTORY_ENTRY', payload: entry });
    });
    dispatch({ type: 'SET_MARKET_PRICE', payload: session.currentMarketPrice });
    dispatch({ type: 'SET_BALANCES', payload: {
      crypto1: session.crypto1Balance,
      crypto2: session.crypto2Balance,
      stablecoin: session.stablecoinBalance || 0
    }});

    // Restore bot status if session was active and has target prices
    const shouldBotBeRunning = session.isActive && session.targetPriceRows && session.targetPriceRows.length > 0;
    if (shouldBotBeRunning) {
      dispatch({ type: 'SYSTEM_START_BOT_INITIATE' });
      dispatch({ type: 'SYSTEM_COMPLETE_WARMUP' });
      console.log('✅ Bot status restored to Running for loaded session');
    } else {
      dispatch({ type: 'SYSTEM_STOP_BOT' });
      console.log('✅ Bot status set to Stopped for loaded session');
    }

    sessionManager.setCurrentSession(sessionId);
    setCurrentSessionId(sessionId);
    loadSessions();

    toast({
      title: "Session Loaded",
      description: `Session "${session.name}" has been loaded`,
    });

    // Navigate to dashboard to see the loaded session
    setTimeout(() => {
      window.location.href = '/dashboard';
    }, 1000);
  };

  const handleDeleteSession = async (sessionId: string) => {
    const success = await sessionManager.deleteSession(sessionId);
    if (success) {
      if (currentSessionId === sessionId) {
        setCurrentSessionId(null);
      }
      loadSessions();
      toast({
        title: "Session Deleted",
        description: "Session has been deleted successfully",
      });
    }
  };

  const handleRenameSession = (sessionId: string) => {
    if (!editingName.trim()) return;

    const success = sessionManager.renameSession(sessionId, editingName.trim());
    if (success) {
      setEditingSessionId(null);
      setEditingName('');
      loadSessions();
      toast({
        title: "Session Renamed",
        description: "Session has been renamed successfully",
      });
    }
  };

  const handleOpenAlarmConfig = (sessionId: string) => {
    const session = sessionManager.loadSession(sessionId);
    if (session) {
      setSelectedSessionForAlarm({
        id: sessionId,
        name: session.name,
        alarmSettings: session.alarmSettings
      });
      setAlarmModalOpen(true);
    }
  };

  const handleSaveAlarmSettings = async (sessionId: string, alarmSettings: AppSettings) => {
    const success = await sessionManager.updateSessionAlarmSettings(sessionId, alarmSettings);
    if (success) {
      loadSessions();
      toast({
        title: "Alarm Settings Saved",
        description: "Session alarm settings have been updated",
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to save alarm settings",
        variant: "destructive"
      });
    }
  };

  const handleExportSession = (sessionId: string) => {
    const csvContent = sessionManager.exportSessionToCSV(sessionId);
    if (!csvContent) {
      toast({
        title: "Error",
        description: "Failed to export session",
        variant: "destructive"
      });
      return;
    }

    const session = sessionManager.loadSession(sessionId);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${session?.name || 'session'}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Export Complete",
      description: "Session data has been exported to CSV",
    });
  };

  const formatRuntime = (runtime?: number) => {
    if (!runtime || runtime < 0) return '0s';

    const totalSeconds = Math.floor(runtime / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getCurrentSession = () => {
    return sessions.find(s => s.id === currentSessionId);
  };

  const getActiveSessions = () => {
    // Get active sessions from multiple sources to ensure we don't miss any
    const currentSessionId = sessionManager.getCurrentSessionId();

    // Method 1: Get from session manager's active tracking
    const trackedActiveSessions = sessionManager.getCurrentActiveSessions();

    // Method 2: Get from loaded sessions that are marked as active
    const markedActiveSessions = sessions.filter(s => s.isActive);

    // Method 3: Include current session if it exists and is running
    const currentSessionActive = currentSessionId && botSystemStatus === 'Running';
    let currentSessionData = null;
    if (currentSessionActive) {
      currentSessionData = sessions.find(s => s.id === currentSessionId);
      if (!currentSessionData) {
        // Current session not in loaded sessions, try to get it directly
        const currentSession = sessionManager.loadSession(currentSessionId);
        if (currentSession) {
          currentSessionData = {
            id: currentSession.id,
            name: currentSession.name,
            pair: `${currentSession.config.crypto1}/${currentSession.config.crypto2}`,
            createdAt: currentSession.createdAt,
            lastModified: currentSession.lastModified,
            isActive: true,
            runtime: sessionManager.getCurrentRuntime(currentSession.id),
            totalTrades: currentSession.orderHistory.length,
            totalProfitLoss: currentSession.orderHistory
              .filter(trade => trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined)
              .reduce((sum, trade) => sum + (trade.realizedProfitLossCrypto2 || 0), 0)
          };
        }
      }
    }

    // Combine all sources and deduplicate
    const allActiveSessions = new Map();

    // Add tracked active sessions
    trackedActiveSessions.forEach(session => {
      allActiveSessions.set(session.id, session);
    });

    // Add marked active sessions
    markedActiveSessions.forEach(session => {
      allActiveSessions.set(session.id, session);
    });

    // Add current session if running
    if (currentSessionData) {
      allActiveSessions.set(currentSessionData.id, currentSessionData);
    }

    const result = Array.from(allActiveSessions.values());

    console.log('🔍 Admin Panel - Active Sessions (Enhanced):', {
      currentSessionId,
      botSystemStatus,
      trackedCount: trackedActiveSessions.length,
      markedCount: markedActiveSessions.length,
      currentSessionActive,
      finalCount: result.length,
      sessions: result.map(s => ({
        id: s.id,
        name: s.name,
        isActive: s.isActive,
        runtime: s.runtime
      }))
    });

    return result;
  };

  const getInactiveSessions = () => {
    return sessions.filter(s => !s.isActive);
  };

  return (
    <div className="space-y-6">
      {/* Current Sessions */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Current Sessions
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                console.log('🔄 Manual refresh triggered');
                loadSessions();
              }}
              title="Refresh Sessions"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {getActiveSessions().length > 0 ? (
            <div className="space-y-4">
              {/* Table Header */}
              <div className="grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground">
                <div>Session Name</div>
                <div>Active Status</div>
                <div>Runtime</div>
                <div>Alarm</div>
                <div>Actions</div>
              </div>

              {/* Active Sessions Rows */}
              <div className="space-y-2">
                {getActiveSessions().map((session) => (
                  <div key={session.id} className="grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0">
                    <div className="flex items-center gap-2">
                      {editingSessionId === session.id ? (
                        <div className="flex gap-2 flex-1">
                          <Input
                            value={editingName}
                            onChange={(e) => setEditingName(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleRenameSession(session.id)}
                            className="text-sm"
                          />
                          <Button size="sm" onClick={() => handleRenameSession(session.id)}>
                            <Save className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <>
                          <span className="font-medium">{session.name}</span>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              setEditingSessionId(session.id);
                              setEditingName(session.name);
                            }}
                          >
                            <Edit2 className="h-3 w-3" />
                          </Button>
                        </>
                      )}
                    </div>

                    <div>
                      <Badge variant="default">
                        Active
                      </Badge>
                    </div>

                    <div className="text-sm">
                      {session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)}
                    </div>

                    <div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleOpenAlarmConfig(session.id)}
                        title="Configure Alarms"
                        className="btn-outline-neo"
                      >
                        <Volume2 className="h-3 w-3" />
                      </Button>
                    </div>

                    <div>
                      {session.id === currentSessionId ? (
                        <Button onClick={handleSaveCurrentSession} size="sm" className="btn-neo">
                          <Save className="mr-2 h-3 w-3" />
                          Save
                        </Button>
                      ) : (
                        <div className="flex gap-1">
                          <Button size="sm" variant="outline" onClick={() => handleLoadSession(session.id)} title="Load Session">
                            <FolderOpen className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleExportSession(session.id)} title="Export Session">
                            <Download className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No active session</p>
              <p className="text-xs">Start trading to create a session automatically</p>
            </div>
          )}
        </CardContent>
      </Card>



      {/* Past Sessions */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Past Sessions ({getInactiveSessions().length})
          </CardTitle>
          <CardDescription>
            Auto-saved: {getInactiveSessions().length} | Manual: 0
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            {getInactiveSessions().length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No saved sessions yet.</p>
                <p className="text-xs">Save your current session to get started.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Table Header */}
                <div className="grid grid-cols-5 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground">
                  <div>Session Name</div>
                  <div>Active Status</div>
                  <div>Total Runtime</div>
                  <div>Alarm</div>
                  <div>Actions</div>
                </div>

                {/* Past Sessions Rows */}
                <div className="space-y-2">
                  {getInactiveSessions().map((session) => (
                    <div key={session.id} className="grid grid-cols-5 gap-4 items-center py-2 border-b border-border/50 last:border-b-0">
                      <div className="flex items-center gap-2">
                        {editingSessionId === session.id ? (
                          <div className="flex gap-2 flex-1">
                            <Input
                              value={editingName}
                              onChange={(e) => setEditingName(e.target.value)}
                              onKeyPress={(e) => e.key === 'Enter' && handleRenameSession(session.id)}
                              className="text-sm"
                            />
                            <Button size="sm" onClick={() => handleRenameSession(session.id)}>
                              <Save className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <>
                            <span className="font-medium">{session.name}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setEditingSessionId(session.id);
                                setEditingName(session.name);
                              }}
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                      </div>

                      <div>
                        <Badge variant="secondary">
                          Inactive
                        </Badge>
                      </div>

                      <div className="text-sm">
                        {formatRuntime(sessionManager.getCurrentRuntime(session.id))}
                      </div>

                      <div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleOpenAlarmConfig(session.id)}
                          title="Configure Alarms"
                          className="btn-outline-neo"
                        >
                          <Volume2 className="h-3 w-3" />
                        </Button>
                      </div>

                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => handleLoadSession(session.id)} title="Load Session">
                          <FolderOpen className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleExportSession(session.id)} title="Export Session">
                          <Download className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDeleteSession(session.id)} title="Delete Session">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Session Alarm Configuration Modal */}
      {selectedSessionForAlarm && (
        <SessionAlarmConfigModal
          isOpen={alarmModalOpen}
          onClose={() => {
            setAlarmModalOpen(false);
            setSelectedSessionForAlarm(null);
          }}
          sessionId={selectedSessionForAlarm.id}
          sessionName={selectedSessionForAlarm.name}
          currentAlarmSettings={selectedSessionForAlarm.alarmSettings}
          onSave={handleSaveAlarmSettings}
        />
      )}
    </div>
  );
}
