(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,AuthProvider:()=>d});var s=r(5155),a=r(2115),o=r(5695),n=r(172),i=r(5731);let l=(0,a.createContext)(void 0),d=e=>{let{children:t}=e,[d,u]=(0,a.useState)(!1),[c,f]=(0,a.useState)(!0),m=(0,o.useRouter)(),g=(0,o.usePathname)();(0,a.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");"true"===e&&t&&u(!0),f(!1)},[]),(0,a.useEffect)(()=>{c||d||"/login"===g?!c&&d&&"/login"===g&&m.push("/dashboard"):m.push("/login")},[d,c,g,m]);let p=async(e,t)=>{f(!0);try{if(await i.ZQ.login(e,t)){u(!0);try{let{SessionManager:e}=await Promise.all([r.e(553),r.e(737)]).then(r.bind(r,4553));await e.getInstance().refreshBackendConnection()}catch(e){console.error("Failed to refresh session manager:",e)}return m.push("/dashboard"),!0}return u(!1),!1}catch(e){return console.error("Login failed:",e),u(!1),!1}finally{f(!1)}},h=async()=>{try{await i.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{try{let{SessionManager:e}=await Promise.all([r.e(553),r.e(737)]).then(r.bind(r,4553));e.getInstance().handleLogout()}catch(e){console.error("Failed to handle session manager logout:",e)}u(!1),m.push("/login")}};return!c||(null==g?void 0:g.startsWith("/_next/static/"))?d||"/login"===g||(null==g?void 0:g.startsWith("/_next/static/"))?(0,s.jsx)(l.Provider,{value:{isAuthenticated:d,login:p,logout:h,isLoading:c},children:t}):(0,s.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,s.jsx)(n.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,s.jsx)(n.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]})},u=()=>{let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},2558:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>v});var s=r(5155),a=r(7481),o=r(2115),n=r(6621),i=r(2085),l=r(5318),d=r(9434);let u=n.Kq,c=o.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.LM,{ref:t,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...a})});c.displayName=n.LM.displayName;let f=(0,i.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),m=o.forwardRef((e,t)=>{let{className:r,variant:a,...o}=e;return(0,s.jsx)(n.bL,{ref:t,className:(0,d.cn)(f({variant:a}),r),...o})});m.displayName=n.bL.displayName,o.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.rc,{ref:t,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...a})}).displayName=n.rc.displayName;let g=o.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.bm,{ref:t,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...a,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})});g.displayName=n.bm.displayName;let p=o.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.hE,{ref:t,className:(0,d.cn)("text-sm font-semibold",r),...a})});p.displayName=n.hE.displayName;let h=o.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,d.cn)("text-sm opacity-90",r),...a})});function v(){let{toasts:e}=(0,a.dj)();return(0,s.jsxs)(u,{children:[e.map(function(e){let{id:t,title:r,description:a,action:o,...n}=e;return(0,s.jsxs)(m,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[r&&(0,s.jsx)(p,{children:r}),a&&(0,s.jsx)(h,{children:a})]}),o,(0,s.jsx)(g,{})]},t)}),(0,s.jsx)(c,{})]})}h.displayName=n.VY.displayName},6870:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,1383,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,2558)),Promise.resolve().then(r.bind(r,8913)),Promise.resolve().then(r.bind(r,283)),Promise.resolve().then(r.bind(r,7213))},8913:(e,t,r)=>{"use strict";r.d(t,{AIProvider:()=>l});var s=r(5155),a=r(2115),o=r(4477);let n=(0,o.createServerReference)("40e74f27756bb59ec5044c39bd76f8b541e1652b84",o.callServer,void 0,o.findSourceMapURL,"suggestTradingMode"),i=(0,a.createContext)(void 0),l=e=>{let{children:t}=e,[r,o]=(0,a.useState)(null),[l,d]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),f=async e=>{d(!0),c(null),o(null);try{let t=await n(e);o(t)}catch(e){c(e instanceof Error?e.message:"An unknown error occurred during AI suggestion."),console.error("Error fetching trading mode suggestion:",e)}finally{d(!1)}};return(0,s.jsx)(i.Provider,{value:{suggestion:r,isLoading:l,error:u,getTradingModeSuggestion:f},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[832,1,527,353,553,318,441,684,358],()=>t(6870)),_N_E=e.O()}]);