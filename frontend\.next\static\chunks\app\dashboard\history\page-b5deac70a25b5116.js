(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[610],{205:(e,t,r)=>{Promise.resolve().then(r.bind(r,8620))},285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(5155),a=r(2115),i=r(9708),o=r(2085),n=r(9434);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:o,asChild:d=!1,...c}=e,m=d?i.DX:"button";return(0,s.jsx)(m,{className:(0,n.cn)(l({variant:a,size:o,className:r})),ref:t,...c})});d.displayName="Button"},2346:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var s=r(5155),a=r(2115),i=r(7489),o=r(9434);let n=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:n=!0,...l}=e;return(0,s.jsx)(i.b,{ref:t,decorative:n,orientation:a,className:(0,o.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...l})});n.displayName=i.b.displayName},3033:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(5155);r(2115);var a=r(7313),i=r(5695),o=r(1462),n=r(3638);let l=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,s.jsx)(o.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,s.jsx)(n.A,{})}];function d(){let e=(0,i.useRouter)(),t=(0,i.usePathname)(),r="orders";return"/dashboard/history"===t&&(r="history"),(0,s.jsx)(a.tU,{value:r,onValueChange:t=>{let r=l.find(e=>e.value===t);r&&e.push(r.href)},className:"w-full mb-6",children:(0,s.jsx)(a.j7,{className:"grid w-full grid-cols-2 bg-card border-2 border-border",children:l.map(e=>(0,s.jsx)(a.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var s=r(5155);r(2115);var a=r(2085),i=r(9434);let o=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)(o({variant:r}),t),...a})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>n});var s=r(5155),a=r(2115),i=r(9434);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",r),...a})});o.displayName="Card";let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-4 md:p-6",r),...a})});n.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-4 md:p-6 pt-0",r),...a})});c.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-4 md:p-6 pt-0",r),...a})}).displayName="CardFooter"},7313:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>n});var s=r(5155),a=r(2115),i=r(64),o=r(9434);let n=i.bL,l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.B8,{ref:t,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...a})});l.displayName=i.B8.displayName;let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.l9,{ref:t,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...a})});d.displayName=i.l9.displayName;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.UC,{ref:t,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...a})});c.displayName=i.UC.displayName},8620:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(5155),a=r(2115),i=r(3033),o=r(6695),n=r(285),l=r(9409),d=r(6126),c=r(2346),m=r(7213),u=r(4553),p=r(7481),x=r(7223),f=r(679),y=r(7648),h=r(8184);function v(){let{dispatch:e,orderHistory:t,config:r}=(0,m.U)(),{toast:i}=(0,p.dj)(),[y,v]=(0,a.useState)([]),[g,j]=(0,a.useState)("current"),[N,w]=(0,a.useState)([]),C=u.SessionManager.getInstance();(0,a.useEffect)(()=>{P()},[]),(0,a.useEffect)(()=>{"current"===g?w(t):w(C.getSessionHistory(g))},[g,t]);let P=()=>{let e=C.getAllSessions(),t=C.getCurrentSessionId();v(e.filter(e=>e.id!==t).sort((e,t)=>t.lastModified-e.lastModified))},S="current"===g?{name:"Current Session",pair:"".concat(r.crypto1,"/").concat(r.crypto2),totalTrades:t.length,totalProfitLoss:t.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),lastModified:Date.now(),isActive:!0}:y.find(e=>e.id===g);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{className:"text-xl font-bold text-primary",children:"Session History"}),(0,s.jsx)(o.BT,{children:"View trading history for current and past sessions."})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,s.jsxs)(l.l6,{value:g,onValueChange:j,children:[(0,s.jsx)(l.bq,{className:"w-full sm:w-[300px]",children:(0,s.jsx)(l.yv,{placeholder:"Select a session"})}),(0,s.jsxs)(l.gC,{children:[(0,s.jsx)(l.eb,{value:"current",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.E,{variant:"default",className:"text-xs",children:"Current"}),(0,s.jsxs)("span",{children:["Current Session (",r.crypto1&&r.crypto2?"".concat(r.crypto1,"/").concat(r.crypto2):"Crypto 1/Crypto 2 = 0",")"]})]})}),y.length>0&&(0,s.jsxs)(a.Fragment,{children:[(0,s.jsx)(c.w,{className:"my-1"}),y.map(e=>(0,s.jsx)(l.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,s.jsx)("span",{children:e.name})]})},e.id))]},"sessions-list")]})]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{"current"===g?(e({type:"CLEAR_ORDER_HISTORY"}),i({title:"History Cleared",description:"Current session trade history has been cleared."})):i({title:"Cannot Clear",description:"Cannot clear history for past sessions. Use current session to clear history.",variant:"destructive"})},className:"btn-outline-neo",disabled:"current"!==g,children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Clear History"]}),(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{let e,t;if(0===N.length){i({title:"No Data to Export",description:"There is no trade history to export for the selected session.",variant:"destructive"});return}if("current"===g)e=["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...N.map(e=>{var t,s,a,i,o,n,l;return[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,(null===(t=e.amountCrypto1)||void 0===t?void 0:t.toFixed(r.numDigits))||"",(null===(s=e.avgPrice)||void 0===s?void 0:s.toFixed(r.numDigits))||"",(null===(a=e.valueCrypto2)||void 0===a?void 0:a.toFixed(r.numDigits))||"",(null===(i=e.price1)||void 0===i?void 0:i.toFixed(r.numDigits))||"",e.crypto1Symbol,(null===(o=e.price2)||void 0===o?void 0:o.toFixed(r.numDigits))||"",e.crypto2Symbol,(null===(n=e.realizedProfitLossCrypto1)||void 0===n?void 0:n.toFixed(r.numDigits))||"",(null===(l=e.realizedProfitLossCrypto2)||void 0===l?void 0:l.toFixed(r.numDigits))||""].join(",")})].join("\n"),t="current_session_history_".concat(new Date().toISOString().split("T")[0],".csv");else{e=C.exportSessionToCSV(g)||"";let r=C.loadSession(g);t="".concat((null==r?void 0:r.name)||"session","_").concat(new Date().toISOString().split("T")[0],".csv")}if(!e){i({title:"Export Failed",description:"Failed to generate CSV content.",variant:"destructive"});return}let s=new Blob([e],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),o=URL.createObjectURL(s);a.setAttribute("href",o),a.setAttribute("download",t),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a),i({title:"Export Complete",description:"Trade history has been exported to CSV file."})},className:"btn-outline-neo",children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Export"]})]})]}),S&&(0,s.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Session:"}),(0,s.jsx)("div",{className:"font-medium",children:S.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Pair:"}),(0,s.jsx)("div",{className:"font-medium",children:S.pair})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total Trades:"}),(0,s.jsx)("div",{className:"font-medium",children:S.totalTrades})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total P/L:"}),(0,s.jsx)("div",{className:"font-medium ".concat(S.totalProfitLoss>=0?"text-green-600":"text-red-600"),children:S.totalProfitLoss.toFixed(4)})]})]}),"current"!==g&&(0,s.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Last modified: ",(0,h.GP)(new Date(S.lastModified),"MMM dd, yyyy HH:mm")]})]})]})]}),(0,s.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"text-lg font-bold text-primary",children:["Trade History - ",(null==S?void 0:S.name)||"Unknown Session"]}),(0,s.jsx)(o.BT,{children:0===N.length?"No trades recorded for this session yet.":"Showing ".concat(N.length," trades for the selected session.")})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)(b,{history:N,config:r})})]})]})}function b(e){let{history:t,config:r}=e,a=e=>{var t;return null!==(t=null==e?void 0:e.toFixed(r.numDigits))&&void 0!==t?t:"-"},i=[{key:"date",label:"Date"},{key:"hour",label:"Hour"},{key:"pair",label:"Couple"},{key:"crypto",label:"Crypto (".concat(r.crypto1,")")},{key:"orderType",label:"Order Type"},{key:"amount",label:"Amount"},{key:"avgPrice",label:"Avg Price"},{key:"value",label:"Value (".concat(r.crypto2,")")},{key:"price1",label:"Price 1"},{key:"crypto1Symbol",label:"Crypto (".concat(r.crypto1,")")},{key:"price2",label:"Price 2"},{key:"crypto2Symbol",label:"Crypto (".concat(r.crypto2,")")}];return 0===t.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(y.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"No trading history for this session yet."})]}):(0,s.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsx)("tr",{className:"bg-card hover:bg-card border-b",children:i.map(e=>(0,s.jsx)("th",{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left",children:e.label},e.key))})}),(0,s.jsx)("tbody",{children:t.map(e=>{var t;return(0,s.jsxs)("tr",{className:"hover:bg-card/80 border-b",children:[(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,h.GP)(new Date(e.timestamp),"yyyy-MM-dd")}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,h.GP)(new Date(e.timestamp),"HH:mm:ss")}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.pair}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs font-semibold ".concat("BUY"===e.orderType?"text-green-400":"text-destructive"),children:e.orderType}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:a(e.amountCrypto1)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:a(e.avgPrice)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:a(e.valueCrypto2)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:a(e.price1)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:null!==(t=a(e.price2))&&void 0!==t?t:"-"}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto2Symbol}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs ".concat(e.realizedProfitLossCrypto1&&e.realizedProfitLossCrypto1>0?"text-green-400":e.realizedProfitLossCrypto1&&e.realizedProfitLossCrypto1<0?"text-destructive":""),children:a(e.realizedProfitLossCrypto1)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs ".concat(e.realizedProfitLossCrypto2&&e.realizedProfitLossCrypto2>0?"text-green-400":e.realizedProfitLossCrypto2&&e.realizedProfitLossCrypto2<0?"text-destructive":""),children:a(e.realizedProfitLossCrypto2)})]},e.id)})})]})})})}function g(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)(v,{})]})}},9409:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>y,gC:()=>f,l6:()=>c,yv:()=>m});var s=r(5155),a=r(2115),i=r(663),o=r(9556),n=r(7381),l=r(518),d=r(9434);let c=i.bL;i.YJ;let m=i.WT,u=a.forwardRef((e,t)=>{let{className:r,children:a,...n}=e;return(0,s.jsxs)(i.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...n,children:[a,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=i.l9.displayName;let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})});p.displayName=i.PP.displayName;let x=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})});x.displayName=i.wn.displayName;let f=a.forwardRef((e,t)=>{let{className:r,children:a,position:o="popper",...n}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:o,...n,children:[(0,s.jsx)(p,{}),(0,s.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(x,{})]})})});f.displayName=i.UC.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=i.JU.displayName;let y=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,s.jsxs)(i.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:a})]})});y.displayName=i.q7.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=i.wv.displayName}},e=>{var t=t=>e(e.s=t);e.O(0,[1,64,527,26,645,553,318,441,684,358],()=>t(205)),_N_E=e.O()}]);