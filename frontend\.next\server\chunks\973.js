exports.id=973,exports.ids=[973],exports.modules={4780:(e,t,o)=>{"use strict";o.d(t,{cn:()=>n});var r=o(49384),s=o(82348);function n(...e){return(0,s.QP)((0,r.$)(e))}},5551:(e,t,o)=>{"use strict";o.d(t,{SessionManager:()=>a});var r=o(74112),s=o(62185);let n="pluto_current_session",i=()=>"server";class a{constructor(){this.sessions=new Map,this.currentSessionId=null,this.useBackend=!0,this.isInitializing=!1,this.sessionStartTimes=new Map,this.activeSessionsAcrossWindows=new Map,this.windowId=i(),console.log(`🪟 SessionManager initialized for window: ${this.windowId}`),this.sessionStartTimes.clear(),this.useBackend=!0,this.loadSessionsFromStorage(),this.loadActiveSessionsFromStorage(),this.setupStorageListener(),this.handleAppRestart(),this.cleanupStalePersistenceInfo(),setTimeout(()=>{this.initializeBackendConnection()},1e3),console.log(`🪟 SessionManager initialized for window ${this.windowId}`)}static getInstance(){return a.instance||(a.instance=new a),a.instance}generateSessionName(e){let t=e.crypto1||"Crypto1",o=e.crypto2||"Crypto2",r=e.tradingMode||"SimpleSpot",s=`${t}/${o} ${r}`,n=Array.from(this.sessions.values()).filter(e=>e.name.startsWith(s));if(0===n.length)return s;let i=0;return n.forEach(e=>{let t=e.name.match(RegExp(`^${s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")} Session (\\d+)$`));if(t){let e=parseInt(t[1],10);e>i&&(i=e)}else e.name===s&&(i=Math.max(i,1))}),`${s} Session ${i+1}`}async initializeBackendConnection(){if(this.isInitializing){console.log("⚠️ Backend initialization already in progress, skipping");return}this.isInitializing=!0;try{if(!localStorage.getItem("plutoAuthToken")){console.log("⚠️ No auth token found, using localStorage mode until login"),this.useBackend=!1,this.loadSessionsFromStorage();return}if((await fetch("http://localhost:5000/health/",{method:"GET",headers:{"Content-Type":"application/json"}})).ok)console.log("✅ Backend connection established, testing auth and loading sessions"),this.useBackend=!0,await this.loadSessionsFromBackend();else throw Error("Backend health check failed")}catch(e){console.log("⚠️ Backend not available, using localStorage mode:",e),this.useBackend=!1,this.loadSessionsFromStorage()}finally{this.isInitializing=!1}}async checkBackendConnection(){try{let e=localStorage.getItem("plutoAuthToken");if(!e)return!1;return(await fetch("http://localhost:5000/health/",{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}})).ok}catch(e){return console.log("⚠️ Backend connection check failed:",e),!1}}getWindowSpecificKey(e){return`${e}_${this.windowId}`}loadActiveSessionsFromStorage(){try{return}catch(e){console.error("Failed to load active sessions from storage:",e)}}saveActiveSessionsToStorage(){try{return}catch(e){console.error("Failed to save active sessions to storage:",e)}}addActiveSession(e){if(!e||"undefined"===e||"null"===e){console.warn("⚠️ Attempted to add invalid session ID to active sessions:",e);return}let t=`${this.windowId}_${e}`;this.activeSessionsAcrossWindows.set(t,{sessionId:e,windowId:this.windowId,timestamp:Date.now()}),this.saveActiveSessionsToStorage()}removeActiveSession(e){let t=`${this.windowId}_${e}`;this.activeSessionsAcrossWindows.delete(t),this.saveActiveSessionsToStorage()}setupStorageListener(){}handleAppRestart(){try{console.log("\uD83D\uDD04 Checking for app restart and cleaning up running sessions...");let e=localStorage.getItem("pluto_last_app_close"),t=Date.now();if(!e||t-parseInt(e)>3e5){console.log("\uD83D\uDD04 App restart detected - cleaning up running sessions");let e=Array.from(this.sessions.values()).filter(e=>e.isActive);if(e.length>0){console.log(`🛑 Found ${e.length} running sessions to clean up:`,e.map(e=>({id:e.id,name:e.name}))),e.forEach(e=>{try{let t=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),o=`${e.name} (AutoSaved ${t})`,r=this.createNewSession(o,e.config);this.saveSession(r,e.config,e.targetPriceRows,e.orderHistory,e.currentMarketPrice,e.crypto1Balance,e.crypto2Balance,e.stablecoinBalance,!1),e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(e.id,e),console.log(`✅ Auto-saved running session "${e.name}" to past sessions as "${o}"`)}catch(t){console.error(`❌ Failed to auto-save running session "${e.name}":`,t),e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(e.id,e)}}),this.currentSessionId=null;let t=this.getWindowSpecificKey(n);localStorage.removeItem(t),sessionStorage.removeItem(n),this.saveSessionsToStorage(),console.log("✅ App restart cleanup completed - all running sessions moved to past sessions")}else console.log("✅ No running sessions found - no cleanup needed")}else console.log("✅ Normal app continuation - no cleanup needed");localStorage.setItem("pluto_app_start",t.toString())}catch(e){console.error("❌ Error during app restart cleanup:",e)}}cleanupStalePersistenceInfo(){try{console.log("\uD83E\uDDF9 Cleaning up stale persistence info...");let e=Date.now(),t=[];for(let o=0;o<localStorage.length;o++){let r=localStorage.key(o);if(r&&r.startsWith("pluto_session_persistence_"))try{let o=JSON.parse(localStorage.getItem(r)||"{}");o.lastSaved&&e-o.lastSaved>864e5&&t.push(r)}catch(e){t.push(r)}}t.forEach(e=>{localStorage.removeItem(e),console.log(`🗑️ Removed stale persistence info: ${e}`)}),t.length>0?console.log(`✅ Cleaned up ${t.length} stale persistence entries`):console.log("✅ No stale persistence info found")}catch(e){console.error("❌ Error cleaning up persistence info:",e)}}loadSessionsFromStorage(){try{return}catch(e){console.error("Failed to load sessions from storage:",e)}}async loadSessionsFromBackend(){try{console.log("⚠️ Invalid or missing auth token, skipping backend session loading"),this.useBackend=!1,this.loadSessionsFromStorage();return}catch(t){let e=t instanceof Error?t.message:String(t);e.includes("Authentication")||e.includes("401")||e.includes("422")?(console.log("\uD83D\uDD10 Authentication issue detected, disabling backend mode"),this.useBackend=!1):e.includes("Cannot connect to server")?console.log("\uD83C\uDF10 Backend server not available, using local storage only"):console.warn("⚠️ Backend session loading failed, falling back to local storage:",e),this.loadSessionsFromStorage()}}saveSessionsToStorage(){try{return}catch(e){console.error("Failed to save sessions to storage:",e)}}async createNewSessionWithAutoName(e,t,o){let r=t||this.generateSessionName(e);return this.createNewSession(r,e,o)}async createNewSession(e,t,o){let n=o||{crypto1:10,crypto2:1e5,stablecoin:0};if(this.useBackend)try{let o={name:e,config:t,targetPriceRows:[],currentMarketPrice:1e5,crypto1Balance:n.crypto1,crypto2Balance:n.crypto2,stablecoinBalance:n.stablecoin},r=await s.sessionApi.createSession(o),i={id:r.session.session_uuid,name:r.session.name,config:t,createdAt:new Date(r.session.created_at).getTime(),lastModified:new Date(r.session.last_modified).getTime(),isActive:r.session.is_active,runtime:r.session.runtime||0,targetPriceRows:[],orderHistory:[],currentMarketPrice:1e5,crypto1Balance:n.crypto1,crypto2Balance:n.crypto2,stablecoinBalance:n.stablecoin};return this.sessions.set(r.session.session_uuid,i),console.log("✅ Session created on backend:",r.session.session_uuid),r.session.session_uuid}catch(e){console.error("❌ Failed to create session on backend, falling back to localStorage:",e),this.useBackend=!1}let i=(0,r.A)(),a=Date.now(),c={id:i,name:e,config:t,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:n.crypto1,crypto2Balance:n.crypto2,stablecoinBalance:n.stablecoin,createdAt:a,lastModified:a,isActive:!1,runtime:0};return this.sessions.set(i,c),this.saveSessionsToStorage(),i}async saveSession(e,t,o,r,s,n,i,a,c=!1,l){try{let d;let u=this.sessions.get(e);if(!u)return console.error("Session not found:",e),!1;let p={lastSaved:Date.now(),windowId:this.windowId,isActive:c,sessionId:e};if(void 0!==l)d=l,console.log(`📊 Using override runtime: ${d}ms for session ${e}`);else{d=u.runtime||0;let t=this.sessionStartTimes.get(e);t&&c?(d=(u.runtime||0)+(Date.now()-t),this.sessionStartTimes.set(e,Date.now())):!c&&t?(d=(u.runtime||0)+(Date.now()-t),this.sessionStartTimes.delete(e)):c&&!t&&this.sessionStartTimes.set(e,Date.now())}let g={...u,config:t,targetPriceRows:[...o],orderHistory:[...r],currentMarketPrice:s,crypto1Balance:n,crypto2Balance:i,stablecoinBalance:a,isActive:c,lastModified:Date.now(),runtime:d};return localStorage.setItem(`pluto_session_persistence_${e}`,JSON.stringify(p)),console.log(`💾 Saving session with complete data:`,{sessionId:e,targetPriceRows:o.length,orderHistory:r.length,balances:{crypto1Balance:n,crypto2Balance:i,stablecoinBalance:a},isActive:c,runtime:d}),this.sessions.set(e,g),c?(this.addActiveSession(e),console.log(`✅ Session marked as active and tracked: ${e}`)):(this.removeActiveSession(e),console.log(`⏹️ Session marked as inactive and removed from tracking: ${e}`)),this.useBackend,this.saveSessionsToStorage(),!0}catch(e){return console.error("Failed to save session:",e),!1}}loadSession(e){return this.sessions.get(e)||null}async deleteSession(e){this.useBackend;let t=this.sessions.delete(e);if(t){if(this.currentSessionId===e){this.currentSessionId=null;let e=this.getWindowSpecificKey(n);localStorage.removeItem(e)}this.saveSessionsToStorage()}return t}getAllSessions(){return Array.from(this.sessions.values()).map(e=>({id:e.id,name:e.name,pair:`${e.config.crypto1}/${e.config.crypto2}`,createdAt:e.createdAt,lastModified:e.lastModified,isActive:e.isActive,runtime:this.getCurrentRuntime(e.id),totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0)}))}getCurrentActiveSessions(){let e=new Set(Array.from(this.activeSessionsAcrossWindows.values()).map(e=>e.sessionId).filter(e=>e&&"undefined"!==e));this.currentSessionId&&this.sessions.has(this.currentSessionId)&&e.add(this.currentSessionId),console.log("\uD83D\uDD0D getCurrentActiveSessions debug:",{activeSessionsAcrossWindows:Array.from(this.activeSessionsAcrossWindows.entries()),activeSessionIds:Array.from(e),allSessions:Array.from(this.sessions.keys()),currentSessionId:this.currentSessionId,windowId:this.windowId});let t=Array.from(this.sessions.values()).filter(t=>{let o=t.id&&e.has(t.id),r=t.isActive,s=t.id===this.currentSessionId;return console.log(`🔍 Session ${t.id} (${t.name}):`,{isInActiveTracking:o,isMarkedActive:r,isCurrentSession:s,shouldInclude:o||r||s}),o||r||s}).map(e=>({id:e.id,name:e.name,pair:`${e.config.crypto1}/${e.config.crypto2}`,createdAt:e.createdAt,lastModified:e.lastModified,isActive:!0,runtime:this.getCurrentRuntime(e.id),totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0)})).filter(e=>e.id);return console.log("\uD83D\uDD0D Returning active sessions:",t.length,t.map(e=>({id:e.id,name:e.name}))),t}setCurrentSession(e){if(this.sessions.has(e)){this.currentSessionId=e;let t=this.getWindowSpecificKey(n);localStorage.setItem(t,e),sessionStorage.setItem(n,e);let o=this.sessions.get(e);o&&(o.isActive=!0,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage(),this.addActiveSession(e),console.log(`✅ Session ${e} marked as active for window ${this.windowId}`),window.dispatchEvent(new StorageEvent("storage",{key:"pluto_current_session",newValue:e,storageArea:localStorage})))}}getCurrentSessionId(){return this.currentSessionId}clearCurrentSession(){if(this.currentSessionId){if(this.removeActiveSession(this.currentSessionId),Array.from(this.activeSessionsAcrossWindows.values()).some(e=>e.sessionId===this.currentSessionId))console.log(`🔄 Session ${this.currentSessionId} still active in other windows`);else{let e=this.sessions.get(this.currentSessionId);e&&e.isActive&&(e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage(),console.log(`⏹️ Session ${this.currentSessionId} marked as inactive (no other windows using it)`))}}this.currentSessionId=null,console.log(`🗑️ Cleared current session for window ${this.windowId}`)}startSessionRuntime(e){this.sessionStartTimes.set(e,Date.now())}stopSessionRuntime(e){let t=this.sessionStartTimes.get(e);if(t){let o=this.sessions.get(e);if(o){let r=Date.now()-t;o.runtime=(o.runtime||0)+r,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage()}this.sessionStartTimes.delete(e)}}deactivateSession(e){let t=this.sessions.get(e);t&&t.isActive&&(t.isActive=!1,t.lastModified=Date.now(),this.sessions.set(e,t),this.saveSessionsToStorage(),console.log(`⏹️ Session ${e} deactivated`))}getCurrentRuntime(e){let t=this.sessions.get(e);if(!t)return 0;let o=this.sessionStartTimes.get(e);return o?(t.runtime||0)+(Date.now()-o):t.runtime||0}async refreshBackendConnection(){console.log("\uD83D\uDD04 Refreshing backend connection..."),await this.initializeBackendConnection()}disableBackendMode(){console.log("\uD83D\uDD10 Disabling backend mode due to authentication issues"),this.useBackend=!1,this.isInitializing=!1}handleLogout(){console.log("\uD83D\uDC4B User logged out, switching to localStorage mode"),this.useBackend=!1,this.sessions.clear(),this.currentSessionId=null,this.loadSessionsFromStorage()}exportSessionToJSON(e){let t=this.sessions.get(e);return t?JSON.stringify(t,null,2):null}importSessionFromJSON(e){try{let t=JSON.parse(e),o=(0,r.A)(),s={...t,id:o,isActive:!1,lastModified:Date.now()};return this.sessions.set(o,s),this.saveSessionsToStorage(),o}catch(e){return console.error("Failed to import session:",e),null}}renameSession(e,t){let o=this.sessions.get(e);return!!o&&(o.name=t,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage(),!0)}async updateSessionAlarmSettings(e,t){let o=this.sessions.get(e);if(!o)return!1;if(o.alarmSettings=t,o.lastModified=Date.now(),this.sessions.set(e,o),this.useBackend)try{let r={name:o.name,config:o.config,targetPriceRows:o.targetPriceRows,currentMarketPrice:o.currentMarketPrice,crypto1Balance:o.crypto1Balance,crypto2Balance:o.crypto2Balance,stablecoinBalance:o.stablecoinBalance,isActive:o.isActive,alarm_settings:t};await s.sessionApi.updateSession(e,r),console.log("✅ Session alarm settings saved to backend:",e)}catch(e){console.error("❌ Failed to save session alarm settings to backend:",e)}return this.saveSessionsToStorage(),!0}getSessionHistory(e){let t=this.sessions.get(e);return t?[...t.orderHistory]:[]}exportSessionToCSV(e){let t=this.sessions.get(e);return t?["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...t.orderHistory.map(e=>[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,e.amountCrypto1?.toFixed(t.config.numDigits)||"",e.avgPrice?.toFixed(t.config.numDigits)||"",e.valueCrypto2?.toFixed(t.config.numDigits)||"",e.price1?.toFixed(t.config.numDigits)||"",e.crypto1Symbol,e.price2?.toFixed(t.config.numDigits)||"",e.crypto2Symbol,e.realizedProfitLossCrypto1?.toFixed(t.config.numDigits)||"",e.realizedProfitLossCrypto2?.toFixed(t.config.numDigits)||""].join(","))].join("\n"):null}clearAllSessions(){this.sessions.clear(),this.currentSessionId=null,localStorage.removeItem("pluto_trading_sessions");let e=this.getWindowSpecificKey(n);localStorage.removeItem(e)}enableAutoSave(e,t,o=3e4){let r=setInterval(()=>{let o=t();this.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,o.isActive)},o);return()=>clearInterval(r)}}},10074:(e,t,o)=>{Promise.resolve().then(o.bind(o,14947)),Promise.resolve().then(o.bind(o,18449)),Promise.resolve().then(o.bind(o,63213)),Promise.resolve().then(o.bind(o,78895))},14947:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>m});var r=o(60687),s=o(29867),n=o(43210),i=o(47313),a=o(24224),c=o(78726),l=o(4780);let d=i.Kq,u=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.LM,{ref:o,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));u.displayName=i.LM.displayName;let p=(0,a.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),g=n.forwardRef(({className:e,variant:t,...o},s)=>(0,r.jsx)(i.bL,{ref:s,className:(0,l.cn)(p({variant:t}),e),...o}));g.displayName=i.bL.displayName,n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.rc,{ref:o,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=i.rc.displayName;let y=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.bm,{ref:o,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})}));y.displayName=i.bm.displayName;let h=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.hE,{ref:o,className:(0,l.cn)("text-sm font-semibold",e),...t}));h.displayName=i.hE.displayName;let S=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.VY,{ref:o,className:(0,l.cn)("text-sm opacity-90",e),...t}));function m(){let{toasts:e}=(0,s.dj)();return(0,r.jsxs)(d,{children:[e.map(function({id:e,title:t,description:o,action:s,...n}){return(0,r.jsxs)(g,{...n,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&(0,r.jsx)(h,{children:t}),o&&(0,r.jsx)(S,{children:o})]}),s,(0,r.jsx)(y,{})]},e)}),(0,r.jsx)(u,{})]})}S.displayName=i.VY.displayName},16949:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,16444,23)),Promise.resolve().then(o.t.bind(o,16042,23)),Promise.resolve().then(o.t.bind(o,88170,23)),Promise.resolve().then(o.t.bind(o,49477,23)),Promise.resolve().then(o.t.bind(o,29345,23)),Promise.resolve().then(o.t.bind(o,12089,23)),Promise.resolve().then(o.t.bind(o,46577,23)),Promise.resolve().then(o.t.bind(o,31307,23))},18449:(e,t,o)=>{"use strict";o.d(t,{AIProvider:()=>c});var r=o(60687),s=o(43210),n=o(6475);let i=(0,n.createServerReference)("40e74f27756bb59ec5044c39bd76f8b541e1652b84",n.callServer,void 0,n.findSourceMapURL,"suggestTradingMode"),a=(0,s.createContext)(void 0),c=({children:e})=>{let[t,o]=(0,s.useState)(null),[n,c]=(0,s.useState)(!1),[l,d]=(0,s.useState)(null),u=async e=>{c(!0),d(null),o(null);try{let t=await i(e);o(t)}catch(e){d(e instanceof Error?e.message:"An unknown error occurred during AI suggestion."),console.error("Error fetching trading mode suggestion:",e)}finally{c(!1)}};return(0,r.jsx)(a.Provider,{value:{suggestion:t,isLoading:n,error:l,getTradingModeSuggestion:u},children:e})}},26443:(e,t,o)=>{"use strict";o.d(t,{AIProvider:()=>s});var r=o(12907);let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call AIProvider() from the server but AIProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx","AIProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAIContext() from the server but useAIContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx","useAIContext")},29131:(e,t,o)=>{"use strict";o.d(t,{AuthProvider:()=>s});var r=o(12907);let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},29867:(e,t,o)=>{"use strict";o.d(t,{dj:()=>p});var r=o(43210);let s=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:o}=t;return o?i(o):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===o||void 0===o?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function d(e){l=a(l,e),c.forEach(e=>{e(l)})}function u({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),o=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||o()}}}),{id:t,dismiss:o,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=r.useState(l);return r.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},47002:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=47002,e.exports=t},47506:(e,t,o)=>{"use strict";o.d(t,{TradingProvider:()=>s});var r=o(12907);let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call TradingProvider() from the server but TradingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx","TradingProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useTradingContext() from the server but useTradingContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx","useTradingContext")},55280:(e,t,o)=>{"use strict";o.d(t,{Oh:()=>r,Ql:()=>i,hg:()=>s,vA:()=>n});let r={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/order-executed.mp3",soundError:"/sounds/error.mp3",clearOrderHistoryOnStart:!1},s=["BTC","ETH","ADA","SOL","DOGE","LINK","MATIC","DOT","AVAX","XRP","LTC","BCH","BNB","SHIB"],n={BTC:["USDT","USDC","FDUSD","EUR"],ETH:["USDT","USDC","FDUSD","BTC","EUR"],ADA:["USDT","USDC","BTC","ETH"],SOL:["USDT","USDC","BTC","ETH"]},i=["USDT","USDC","FDUSD","DAI"]},58805:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,86346,23)),Promise.resolve().then(o.t.bind(o,27924,23)),Promise.resolve().then(o.t.bind(o,35656,23)),Promise.resolve().then(o.t.bind(o,40099,23)),Promise.resolve().then(o.t.bind(o,38243,23)),Promise.resolve().then(o.t.bind(o,28827,23)),Promise.resolve().then(o.t.bind(o,62763,23)),Promise.resolve().then(o.t.bind(o,97173,23))},61135:()=>{},62185:(e,t,o)=>{"use strict";o.d(t,{ZQ:()=>n,oc:()=>i,sessionApi:()=>a});let r="http://localhost:5000";async function s(e,t={}){let o=`${r}${e}`,n=localStorage.getItem("plutoAuthToken"),i=o.includes("/sessions")||o.includes("/trading")||o.includes("/admin");if(i&&!n)throw console.warn(`🔐 Blocked API call to ${o} - no authentication token available`),Error("Authentication required - no token available");console.log("\uD83D\uDD10 Auth token check:",{hasToken:!!n,tokenLength:n?n.length:0,tokenPreview:n?`${n.substring(0,20)}...`:"No token",isAuthenticatedEndpoint:i});let a={"Content-Type":"application/json",...n?{Authorization:`Bearer ${n}`}:{},...t.headers};try{let e;let r=new AbortController,s=setTimeout(()=>r.abort(),1e4),n=await fetch(o,{...t,headers:a,signal:r.signal}).finally(()=>clearTimeout(s));if(401===n.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),Error("Authentication expired. Please login again.");let i="",c=n.headers.get("content-type");if(c&&c.includes("application/json")){i=await n.text();try{e=JSON.parse(i)}catch{e={message:i}}}else{i=await n.text();try{e=JSON.parse(i)}catch(t){e={message:i}}}if(!n.ok){let o={status:n.status,statusText:n.statusText,url:n.url,method:t.method||"GET",responseData:e,responseText:i.substring(0,500)};throw 401!==n.status&&422!==n.status&&e&&(e.error||e.message||Object.keys(e).length>0)?console.error("\uD83D\uDEA8 API Error Details:",o):console.warn(`⚠️ API ${n.status} Error:`,n.url),Error(e.error||e.message||`API error: ${n.status}`)}return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",e),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw console.error("Request timeout:",e),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",e),e}}console.log("API Base URL:",r);let n={login:async(e,t)=>{try{let o=await c(async()=>await s("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(o&&o.access_token)return localStorage.setItem("plutoAuthToken",o.access_token),localStorage.setItem("plutoAuth","true"),o.user&&localStorage.setItem("plutoUser",JSON.stringify(o.user)),!0;return!1}catch(e){return console.error("Login API error:",e),!1}},register:async(e,t,o)=>c(async()=>await s("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:o})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},i={getConfig:async e=>s(e?`/trading/config/${e}`:"/trading/config"),saveConfig:async e=>s("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>s(`/trading/config/${e}`,{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>s(`/trading/bot/start/${e}`,{method:"POST"}),stopBot:async e=>s(`/trading/bot/stop/${e}`,{method:"POST"}),getBotStatus:async e=>s(`/trading/bot/status/${e}`),getTradeHistory:async e=>{let t=e?`?configId=${e}`:"";return s(`/trading/history${t}`)},getBalances:async()=>s("/trading/balances"),getMarketPrice:async e=>s(`/trading/market-data/${e}`),getTradingPairs:async(e="binance")=>s(`/trading/exchange/trading-pairs?exchange=${e}`),getCryptocurrencies:async(e="binance")=>s(`/trading/exchange/cryptocurrencies?exchange=${e}`)},a={getAllSessions:async(e=!0)=>s(`/sessions/?include_inactive=${e}`),createSession:async e=>s("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>s(`/sessions/${e}`),updateSession:async(e,t)=>s(`/sessions/${e}`,{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>s(`/sessions/${e}`,{method:"DELETE"}),activateSession:async e=>s(`/sessions/${e}/activate`,{method:"POST"}),getSessionHistory:async e=>s(`/sessions/${e}/history`),getActiveSession:async()=>s("/sessions/active")},c=async(e,t=3)=>{let o=0,r=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&o<t){let e=500*Math.pow(2,o);return console.log(`Retrying after ${e}ms (attempt ${o+1}/${t})...`),o++,await new Promise(t=>setTimeout(t,e)),r()}throw e}};return r()}},62486:(e,t,o)=>{"use strict";o.r(t),o.d(t,{"40e74f27756bb59ec5044c39bd76f8b541e1652b84":()=>d});var r=o(91199);o(42087);var s=o(37612),n=o(56758);let i=(0,s.genkit)({plugins:[(0,n.YF)()],model:"googleai/gemini-2.0-flash"});var a=o(33331);let c=s.z.object({riskTolerance:s.z.string().describe("The user risk tolerance, can be low, medium, or high."),preferredCryptocurrencies:s.z.string().describe("The user preferred cryptocurrencies, comma separated."),investmentGoals:s.z.string().describe("The user investment goals, such as long term investment or short term profit.")}),l=s.z.object({suggestedMode:s.z.enum(["Simple Spot","Stablecoin Swap"]).describe("The suggested trading mode."),reason:s.z.string().describe("The reason for the suggestion.")});async function d(e){return p(e)}let u=i.definePrompt({name:"tradingModeSuggestionPrompt",input:{schema:c},output:{schema:l},prompt:`You are an expert in trading mode selection. You will suggest the most suitable trading mode (Simple Spot or Stablecoin Swap) based on the user's risk tolerance, preferred cryptocurrencies, and investment goals.

Risk Tolerance: {{{riskTolerance}}}
Preferred Cryptocurrencies: {{{preferredCryptocurrencies}}}
Investment Goals: {{{investmentGoals}}}

Consider the following:

*   Simple Spot Mode is suitable for users who are comfortable with higher risk and are looking for short term profits.
*   Stablecoin Swap Mode is suitable for users who are risk averse and are looking for long term investment.

Based on the information above, suggest a trading mode and explain your reasoning.`}),p=i.defineFlow({name:"suggestTradingModeFlow",inputSchema:c,outputSchema:l},async e=>{let{output:t}=await u(e);return t});(0,a.D)([d]),(0,r.A)(d,"40e74f27756bb59ec5044c39bd76f8b541e1652b84",null)},63213:(e,t,o)=>{"use strict";o.d(t,{A:()=>d,AuthProvider:()=>l});var r=o(60687),s=o(43210),n=o(16189),i=o(11516),a=o(62185);let c=(0,s.createContext)(void 0),l=({children:e})=>{let[t,l]=(0,s.useState)(!1),[d,u]=(0,s.useState)(!0),p=(0,n.useRouter)(),g=(0,n.usePathname)();(0,s.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");"true"===e&&t&&l(!0),u(!1)},[]),(0,s.useEffect)(()=>{d||t||"/login"===g?!d&&t&&"/login"===g&&p.push("/dashboard"):p.push("/login")},[t,d,g,p]);let y=async(e,t)=>{u(!0);try{if(await a.ZQ.login(e,t)){l(!0);try{let{SessionManager:e}=await Promise.resolve().then(o.bind(o,5551));await e.getInstance().refreshBackendConnection()}catch(e){console.error("Failed to refresh session manager:",e)}return p.push("/dashboard"),!0}return l(!1),!1}catch(e){return console.error("Login failed:",e),l(!1),!1}finally{u(!1)}},h=async()=>{try{await a.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{try{let{SessionManager:e}=await Promise.resolve().then(o.bind(o,5551));e.getInstance().handleLogout()}catch(e){console.error("Failed to handle session manager logout:",e)}l(!1),p.push("/login")}};return d&&!g?.startsWith("/_next/static/")?(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]}):t||"/login"===g||g?.startsWith("/_next/static/")?(0,r.jsx)(c.Provider,{value:{isAuthenticated:t,login:y,logout:h,isLoading:d},children:e}):(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]})},d=()=>{let e=(0,s.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},78895:(e,t,o)=>{"use strict";o.d(t,{TradingProvider:()=>k,U:()=>B});var r=o(60687),s=o(43210),n=o(55280),i=o(74112),a=o(29867),c=o(62185),l=o(5551);class d{constructor(){this.isOnline=navigator.onLine,this.listeners=new Set,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=5e3,this.hasInitialized=!1,this.setupEventListeners(),this.startPeriodicCheck(),setTimeout(()=>{this.hasInitialized=!0},1e3)}static getInstance(){return d.instance||(d.instance=new d),d.instance}setupEventListeners(){window.addEventListener("online",this.handleOnline.bind(this)),window.addEventListener("offline",this.handleOffline.bind(this)),document.addEventListener("visibilitychange",()=>{document.hidden||this.checkConnection()})}handleOnline(){console.log("\uD83C\uDF10 Network: Back online"),this.isOnline=!0,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.notifyListeners(!0,!this.hasInitialized)}handleOffline(){console.log("\uD83C\uDF10 Network: Gone offline"),this.isOnline=!1,this.notifyListeners(!1,!this.hasInitialized)}async checkConnection(){let e=navigator.onLine;return e!==this.isOnline&&(this.isOnline=e,this.notifyListeners(e,!this.hasInitialized),e&&(this.lastOnlineTime=Date.now(),this.reconnectAttempts=0)),e}startPeriodicCheck(){let e=setInterval(()=>{this.checkConnection()},6e4);this.periodicInterval=e}cleanup(){this.periodicInterval&&clearInterval(this.periodicInterval),this.listeners.clear()}notifyListeners(e,t=!1){this.listeners.forEach(o=>{try{o(e,t)}catch(e){console.error("Error in network status listener:",e)}})}addListener(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}getStatus(){return{isOnline:this.isOnline,lastOnlineTime:this.lastOnlineTime,reconnectAttempts:this.reconnectAttempts}}async forceCheck(){return await this.checkConnection()}async attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return console.log("\uD83C\uDF10 Network: Max reconnect attempts reached"),!1;this.reconnectAttempts++;let e=Math.min(this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1),3e4);console.log(`🌐 Network: Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${e}ms`),await new Promise(t=>setTimeout(t,e));let t=await this.checkConnection();return!t&&this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>this.attemptReconnect(),1e3),t}}class u{constructor(){this.saveInterval=null,this.saveFunction=null,this.intervalMs=3e4,this.isEnabled=!0,this.lastSaveTime=0,this.networkMonitor=d.getInstance(),this.setupNetworkListener(),this.setupBeforeUnloadListener()}static getInstance(){return u.instance||(u.instance=new u),u.instance}setupNetworkListener(){this.networkMonitor.addListener(e=>{e&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on network reconnection"),this.saveFunction(),this.lastSaveTime=Date.now())})}setupBeforeUnloadListener(){window.addEventListener("beforeunload",()=>{this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving before page unload"),this.saveFunction())}),document.addEventListener("visibilitychange",()=>{document.hidden&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on tab switch"),this.saveFunction(),this.lastSaveTime=Date.now())})}enable(e,t=3e4){this.saveFunction=e,this.intervalMs=t,this.isEnabled=!0,this.stop(),this.saveInterval=setInterval(()=>{this.isEnabled&&this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Periodic save"),this.saveFunction(),this.lastSaveTime=Date.now())},this.intervalMs),console.log(`💾 Auto-save: Enabled with ${t}ms interval`)}disable(){this.isEnabled=!1,this.stop(),console.log("\uD83D\uDCBE Auto-save: Disabled")}stop(){this.saveInterval&&(clearInterval(this.saveInterval),this.saveInterval=null)}saveNow(){this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Manual save triggered"),this.saveFunction(),this.lastSaveTime=Date.now())}getStatus(){return{isEnabled:this.isEnabled,lastSaveTime:this.lastSaveTime,intervalMs:this.intervalMs,isOnline:this.networkMonitor.getStatus().isOnline}}}class p{constructor(){this.checkInterval=null,this.warningThreshold=262144e3,this.criticalThreshold=0x19000000,this.listeners=new Set,this.startMonitoring()}static getInstance(){return p.instance||(p.instance=new p),p.instance}startMonitoring(){console.log("\uD83D\uDCCA Memory monitoring disabled to prevent frequent notifications")}checkMemoryUsage(){if("memory"in performance){let e=performance.memory,t=e.usedJSHeapSize;this.notifyListeners(e),t>this.criticalThreshold?(console.warn("\uD83E\uDDE0 Memory: Critical memory usage detected:",{used:`${(t/1024/1024).toFixed(2)}MB`,total:`${(e.totalJSHeapSize/1024/1024).toFixed(2)}MB`,limit:`${(e.jsHeapSizeLimit/1024/1024).toFixed(2)}MB`}),"gc"in window&&window.gc()):t>this.warningThreshold&&console.log("\uD83E\uDDE0 Memory: High memory usage:",{used:`${(t/1024/1024).toFixed(2)}MB`,total:`${(e.totalJSHeapSize/1024/1024).toFixed(2)}MB`})}}notifyListeners(e){this.listeners.forEach(t=>{try{t(e)}catch(e){console.error("Error in memory monitor listener:",e)}})}addListener(e){return this.listeners.add(e),()=>this.listeners.delete(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}stop(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}}let g=e=>e.crypto1&&e.crypto2?w(e):0,y=async e=>{try{if(!e.crypto1||!e.crypto2)return 0;let t=`${e.crypto1}${e.crypto2}`.toUpperCase();try{let o=await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${t}`,{method:"GET",headers:{Accept:"application/json"}});if(o.ok){let t=await o.json(),r=parseFloat(t.price);if(r>0)return console.log(`✅ Real-time price from Binance: ${e.crypto1}/${e.crypto2} = ${r.toLocaleString()}`),r}else console.warn(`Binance API response not OK: ${o.status} ${o.statusText}`)}catch(e){console.warn("Binance API failed, trying alternative...",e)}try{let o=await fetch(`https://api.binance.com/api/v3/ticker/24hr?symbol=${t}`);if(o.ok){let t=await o.json(),r=parseFloat(t.lastPrice);if(r>0)return console.log(`✅ Real-time price from Binance (24hr): ${e.crypto1}/${e.crypto2} = ${r.toLocaleString()}`),r}}catch(e){console.warn("Binance 24hr API also failed...",e)}try{let t=h(e.crypto1),o=h(e.crypto2);if(t&&o){let r=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${t}&vs_currencies=${o}`);if(r.ok){let s=await r.json(),n=s[t]?.[o];if(n>0)return console.log(`✅ Price fetched from CoinGecko: ${e.crypto1}/${e.crypto2} = ${n}`),n}}}catch(e){console.warn("CoinGecko API failed, using mock price...",e)}try{let t=await f(e.crypto1),o=await f(e.crypto2),r=t/o;return console.log(`⚠️ Using calculated price from real-time USD values: ${e.crypto1}/${e.crypto2} = ${r.toLocaleString()}`),r}catch(e){console.warn("Real-time USD price calculation failed, using static fallback...",e)}let o=w(e);return console.log(`⚠️ Using static mock price: ${e.crypto1}/${e.crypto2} = ${o.toLocaleString()}`),o}catch(t){return console.error("Error fetching market price:",t),w(e)}},h=e=>({BTC:"bitcoin",ETH:"ethereum",SOL:"solana",ADA:"cardano",DOT:"polkadot",MATIC:"matic-network",AVAX:"avalanche-2",LINK:"chainlink",UNI:"uniswap",USDT:"tether",USDC:"usd-coin",BUSD:"binance-usd",DAI:"dai"})[e.toUpperCase()]||null,S={},m=0,f=async e=>{let t=Date.now(),o=e.toUpperCase();if(S[o]&&t-m<6e4)return S[o];try{let r=h(e);if(r){let s=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${r}&vs_currencies=usd`);if(s.ok){let n=await s.json(),i=n[r]?.usd;if(i&&i>0)return S[o]=i,m=t,console.log(`📊 Real-time price fetched for ${e}: $${i}`),i}}}catch(t){console.warn(`⚠️ Failed to fetch real-time price for ${e}, using fallback:`,t)}return v(e)},v=e=>{let t=e.toUpperCase();return S[t]?S[t]:b(e)},b=e=>({BTC:106e3,ETH:2500,SOL:180,ADA:.85,DOGE:.32,LINK:22,MATIC:.42,DOT:6.5,AVAX:38,SHIB:22e-6,XRP:2.1,LTC:95,BCH:420,UNI:15,AAVE:180,MKR:1800,SNX:3.5,COMP:85,YFI:8500,SUSHI:2.1,"1INCH":.65,CRV:.85,UMA:3.2,ATOM:12,NEAR:6.5,ALGO:.35,ICP:14,HBAR:.28,APT:12.5,TON:5.8,FTM:.95,ONE:.025,FIL:8.5,TRX:.25,ETC:35,VET:.055,QNT:125,LDO:2.8,CRO:.18,LUNC:15e-5,MANA:.85,SAND:.75,AXS:8.5,ENJ:.45,CHZ:.12,THETA:2.1,FLOW:1.2,XTZ:1.8,EOS:1.1,GRT:.28,BAT:.35,ZEC:45,DASH:35,LRC:.45,ZRX:.65,KNC:.85,REN:.15,BAND:2.5,STORJ:.85,NMR:25,ANT:8.5,BNT:.95,MLN:35,REP:15,IOTX:.065,ZIL:.045,ICX:.35,QTUM:4.5,ONT:.45,WAVES:3.2,LSK:1.8,NANO:1.5,SC:.008,DGB:.025,RVN:.035,BTT:15e-7,WIN:15e-5,HOT:.0035,DENT:.0018,NPXS:85e-5,FUN:.0085,CELR:.025,USDT:1,USDC:1,FDUSD:1,BUSD:1,DAI:1})[e.toUpperCase()]||100,w=e=>{let t=v(e.crypto1),o=v(e.crypto2),r=t/o*(1+(Math.random()-.5)*.02);return console.log(`📊 Fallback price calculation: ${e.crypto1} ($${t}) / ${e.crypto2} ($${o}) = ${r.toFixed(6)}`),r},A={tradingMode:"SimpleSpot",crypto1:"",crypto2:"",baseBid:100,multiplier:1.005,numDigits:4,slippagePercent:.2,incomeSplitCrypto1Percent:50,incomeSplitCrypto2Percent:50,preferredStablecoin:n.Ql[0]},P={config:A,targetPriceRows:[],orderHistory:[],appSettings:n.Oh,currentMarketPrice:g(A),botSystemStatus:"Stopped",crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,backendStatus:"unknown"},T=new Map,C=e=>{},$=()=>null,E=(e,t)=>{switch(t.type){case"SET_CONFIG":let o={...e.config,...t.payload};if(t.payload.crypto1||t.payload.crypto2)return{...e,config:o,currentMarketPrice:g(o)};return{...e,config:o};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload.sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}))};case"ADD_TARGET_PRICE_ROW":{let o=[...e.targetPriceRows,t.payload].sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"UPDATE_TARGET_PRICE_ROW":{let o=e.targetPriceRows.map(e=>e.id===t.payload.id?t.payload:e).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"REMOVE_TARGET_PRICE_ROW":{let o=e.targetPriceRows.filter(e=>e.id!==t.payload).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"ADD_ORDER_HISTORY_ENTRY":return setTimeout(()=>{let o=l.SessionManager.getInstance(),r=o.getCurrentSessionId();if(r&&o.loadSession(r)){let s=[t.payload,...e.orderHistory];o.saveSession(r,e.config,e.targetPriceRows,s,e.currentMarketPrice,e.crypto1Balance,e.crypto2Balance,e.stablecoinBalance,"Running"===e.botSystemStatus).then(()=>{console.log("\uD83D\uDCBE Immediate auto-save triggered by trade")}).catch(e=>{console.error("❌ Failed immediate auto-save after trade:",e)})}},100),{...e,orderHistory:[t.payload,...e.orderHistory]};case"CLEAR_ORDER_HISTORY":return{...e,orderHistory:[]};case"SET_APP_SETTINGS":return{...e,appSettings:{...e.appSettings,...t.payload}};case"SET_MARKET_PRICE":return{...e,currentMarketPrice:t.payload};case"UPDATE_BALANCES":return{...e,crypto1Balance:void 0!==t.payload.crypto1?t.payload.crypto1:e.crypto1Balance,crypto2Balance:void 0!==t.payload.crypto2?t.payload.crypto2:e.crypto2Balance,stablecoinBalance:void 0!==t.payload.stablecoin?t.payload.stablecoin:e.stablecoinBalance};case"SET_BALANCES":return{...e,crypto1Balance:t.payload.crypto1,crypto2Balance:t.payload.crypto2,stablecoinBalance:void 0!==t.payload.stablecoin?t.payload.stablecoin:e.stablecoinBalance};case"UPDATE_STABLECOIN_BALANCE":return{...e,stablecoinBalance:t.payload};case"RESET_SESSION":let r={...e.config};return{...P,config:r,appSettings:{...e.appSettings},currentMarketPrice:g(r),crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance};case"SET_BACKEND_STATUS":return{...e,backendStatus:t.payload};case"SYSTEM_START_BOT_INITIATE":if(!e.config.crypto1||!e.config.crypto2)return console.warn("⚠️ Cannot start bot: Both crypto1 and crypto2 must be selected"),e;if(!e.targetPriceRows||0===e.targetPriceRows.length)return console.warn("⚠️ Cannot start bot: Target prices must be set"),e;return{...e,botSystemStatus:"WarmingUp"};case"SYSTEM_COMPLETE_WARMUP":return{...e,botSystemStatus:"Running"};case"SYSTEM_STOP_BOT":return{...e,botSystemStatus:"Stopped"};case"SYSTEM_RESET_BOT":return T.clear(),l.SessionManager.getInstance().clearCurrentSession(),{...e,botSystemStatus:"Stopped",targetPriceRows:[],orderHistory:[]};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload};case"RESET_FOR_NEW_CRYPTO":return{...P,config:e.config,backendStatus:e.backendStatus,botSystemStatus:"Stopped",currentMarketPrice:0,crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance};default:return e}},I=(0,s.createContext)(void 0),k=({children:e})=>{let[t,o]=(0,s.useReducer)(E,(()=>{let e=l.SessionManager.getInstance(),t=e.getCurrentSessionId();if(console.log("\uD83D\uDD0D Session restoration check:",{currentSessionId:t,hasSessionId:!!t,allSessions:e.getAllSessions().length}),!t){let t=localStorage.getItem("pluto_last_app_close"),o=Date.now();if(!t||o-parseInt(t)>3e5)return console.log("\uD83C\uDD95 App restart detected - starting with fresh state (running sessions already cleaned up)"),P;{let t=e.getAllSessions().filter(e=>e.isActive).sort((e,t)=>t.lastModified-e.lastModified)[0];if(!t)return console.log("\uD83C\uDD95 No active sessions found - starting with fresh state"),P;console.log("\uD83D\uDD04 Page refresh detected - restoring recent active session:",t.id),e.setCurrentSession(t.id)}}let o=e.getCurrentSessionId();if(!o)return console.log("\uD83C\uDD95 No session ID available after restoration attempt - starting fresh"),P;let r=e.loadSession(o);if(r){console.log("\uD83D\uDD04 Loading session data:",{sessionId:o,isActive:r.isActive,targetPriceRows:r.targetPriceRows.length,orderHistory:r.orderHistory.length,config:r.config,balances:{crypto1:r.crypto1Balance,crypto2:r.crypto2Balance,stablecoin:r.stablecoinBalance}});let e={...A,...r.config,baseBid:r.config.baseBid||A.baseBid,multiplier:r.config.multiplier||A.multiplier,numDigits:r.config.numDigits||A.numDigits,slippagePercent:r.config.slippagePercent||A.slippagePercent,incomeSplitCrypto1Percent:r.config.incomeSplitCrypto1Percent||A.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:r.config.incomeSplitCrypto2Percent||A.incomeSplitCrypto2Percent},t=localStorage.getItem("pluto_last_app_close"),s=Date.now(),n=!t||s-parseInt(t)>3e5,i=r.isActive&&r.targetPriceRows&&r.targetPriceRows.length>0&&!n;return console.log("\uD83D\uDD04 Session restoration details:",{sessionId:o,isActive:r.isActive,hasTargetPrices:r.targetPriceRows.length>0,isAppRestart:n,shouldBotBeRunning:i,targetPriceCount:r.targetPriceRows.length,orderHistoryCount:r.orderHistory.length,restorationReason:n?"App restart - bot will be stopped":"Page refresh - bot state preserved",targetPriceRows:r.targetPriceRows.map(e=>({id:e.id,counter:e.counter,targetPrice:e.targetPrice,status:e.status}))}),{...P,config:e,targetPriceRows:r.targetPriceRows,orderHistory:r.orderHistory,currentMarketPrice:r.currentMarketPrice,crypto1Balance:r.crypto1Balance,crypto2Balance:r.crypto2Balance,stablecoinBalance:r.stablecoinBalance,botSystemStatus:i?"Running":"Stopped"}}console.warn("⚠️ Session ID exists but session data not found, clearing invalid session ID:",o),e.clearCurrentSession();let s=$();return s?{...P,...s}:P})()),{toast:n}=(0,a.dj)();(0,s.useEffect)(()=>{},[]);let g=(0,s.useRef)(null),h=(0,s.useCallback)(async()=>{try{let e;if(!t.config.crypto1||!t.config.crypto2){o({type:"SET_MARKET_PRICE",payload:0});return}if("StablecoinSwap"===t.config.tradingMode)try{let o=await f(t.config.crypto1),r=await f(t.config.crypto2);e=o/r,console.log(`📊 StablecoinSwap Market Price Calculation (Real-time):
            - ${t.config.crypto1} USD Price: $${o.toLocaleString()}
            - ${t.config.crypto2} USD Price: $${r.toLocaleString()}
            - Market Price (${t.config.crypto1}/${t.config.crypto2}): ${e.toFixed(6)}
            - Calculation: ${o} \xf7 ${r} = ${e.toFixed(6)}`)}catch(s){let o=v(t.config.crypto1),r=v(t.config.crypto2);e=o/r,console.log(`📊 StablecoinSwap Market Price Calculation (Fallback):
            - ${t.config.crypto1} USD Price: $${o.toLocaleString()}
            - ${t.config.crypto2} USD Price: $${r.toLocaleString()}
            - Market Price (${t.config.crypto1}/${t.config.crypto2}): ${e.toFixed(6)}`)}else e=await y(t.config);o({type:"SET_MARKET_PRICE",payload:e})}catch(e){console.error("Failed to fetch market price:",e),T("Price API",`Failed to fetch market price: ${e}`).catch(console.error)}},[t.config,o]);(0,s.useEffect)(()=>{h();let e=setInterval(async()=>{if(d.getInstance().getStatus().isOnline)try{await h()}catch(e){console.warn("Failed to update market price:",e)}},2e3),o=setInterval(async()=>{if(d.getInstance().getStatus().isOnline&&"StablecoinSwap"===t.config.tradingMode)try{await f(t.config.crypto1),await f(t.config.crypto2),console.log("\uD83D\uDCCA Real-time USD price cache updated")}catch(e){console.warn("⚠️ Failed to update USD price cache:",e)}},12e4);return()=>{clearInterval(e),clearInterval(o)}},[h,o,t.config.crypto1,t.config.crypto2,t.config.tradingMode]),(0,s.useEffect)(()=>{},[]);let S=(0,s.useCallback)(e=>{let o=l.SessionManager.getInstance(),r=o.getCurrentSessionId(),s=t.appSettings;if(r){let e=o.loadSession(r);e&&e.alarmSettings&&(s=e.alarmSettings)}if(s.soundAlertsEnabled&&g.current){let t;if("soundOrderExecution"===e&&s.alertOnOrderExecution?t=s.soundOrderExecution:"soundError"===e&&s.alertOnError&&(t=s.soundError),t)try{g.current.pause(),g.current.currentTime=0,g.current.src=t,g.current.load(),g.current.onerror=e=>{console.warn(`⚠️ Audio loading failed for ${t}:`,e),"/sounds/chime2.wav"!==t&&g.current&&(g.current.src="/sounds/chime2.wav",g.current.load())};let e=g.current.play();void 0!==e&&e.then(()=>{setTimeout(()=>{g.current&&!g.current.paused&&(g.current.pause(),g.current.currentTime=0)},2e3)}).catch(e=>{let t=e.message||String(e);t.includes("user didn't interact")||t.includes("autoplay")?console.log("\uD83D\uDD07 Audio autoplay blocked by browser - user interaction required"):t.includes("interrupted")||t.includes("supported source")||console.warn("⚠️ Audio playback failed:",t)})}catch(e){console.warn(`⚠️ Audio setup failed for ${t}:`,e)}}},[t.appSettings]),m=(0,s.useCallback)(async(e,t=!1)=>{try{let o=localStorage.getItem("telegram_bot_token"),r=localStorage.getItem("telegram_chat_id");if(!o||!r){console.log("Telegram not configured - skipping notification");return}let s=await fetch(`https://api.telegram.org/bot${o}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:r,text:e,parse_mode:"HTML"})});s.ok?console.log("✅ Telegram notification sent successfully"):(console.error("Failed to send Telegram notification:",s.statusText),t||await b("Telegram API Error",`Failed to send notification: ${s.statusText}`))}catch(e){console.error("Error sending Telegram notification:",e),t||await b("Network Disconnection",`Failed to connect to Telegram API: ${e}`)}},[]),b=(0,s.useCallback)(async(e,o)=>{let r=new Date().toLocaleString(),s=`⚠️ <b>ERROR ALERT</b>

🔴 <b>Type:</b> ${e}
📝 <b>Message:</b> ${o}
⏰ <b>Time:</b> ${r}
🤖 <b>Bot:</b> ${t.config.crypto1}/${t.config.crypto2} ${t.config.tradingMode}`;await m(s,!0)},[t.config,m]),w=(0,s.useCallback)(async(e,o,r)=>{let s=new Date().toLocaleString(),n=`💰 <b>LOW BALANCE ALERT</b>

🪙 <b>Currency:</b> ${e}
📊 <b>Current Balance:</b> ${o.toFixed(6)} ${e}
⚡ <b>Required Amount:</b> ${r.toFixed(6)} ${e}
📉 <b>Shortage:</b> ${(r-o).toFixed(6)} ${e}
⏰ <b>Time:</b> ${s}
🤖 <b>Bot:</b> ${t.config.crypto1}/${t.config.crypto2} ${t.config.tradingMode}`;await m(n)},[t.config,m]),T=(0,s.useCallback)(async(e,o)=>{let r=new Date().toLocaleString(),s=`🔌 <b>API ERROR ALERT</b>

🌐 <b>API:</b> ${e}
❌ <b>Error:</b> ${o}
⏰ <b>Time:</b> ${r}
🤖 <b>Bot:</b> ${t.config.crypto1}/${t.config.crypto2} ${t.config.tradingMode}`;await m(s)},[t.config,m]);(0,s.useEffect)(()=>{},[t.config.crypto1,t.config.crypto2]);let k=(0,s.useCallback)(async()=>{try{let e=l.SessionManager.getInstance(),o=e.getCurrentSessionId();if(!o){if(t.config.crypto1&&t.config.crypto2&&("Running"===t.botSystemStatus||t.targetPriceRows.length>0||t.orderHistory.length>0)){let o={crypto1:t.crypto1Balance,crypto2:t.crypto2Balance,stablecoin:t.stablecoinBalance},r=await e.createNewSessionWithAutoName(t.config,void 0,o);return e.setCurrentSession(r),await e.saveSession(r,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus),!0}return console.log("⚠️ No session to save - bot not running and no meaningful data"),!1}return await e.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus)}catch(e){return console.error("Failed to save current session:",e),!1}},[t]),B=(0,s.useCallback)(e=>{if(!e||!Array.isArray(e))return;let r=[...e].filter(e=>!isNaN(e)&&e>0).sort((e,t)=>e-t).map((e,o)=>{let r=t.targetPriceRows.find(t=>t.targetPrice===e);return r?{...r,counter:o+1}:{id:(0,i.A)(),counter:o+1,status:"Free",orderLevel:0,valueLevel:t.config.baseBid,targetPrice:e}});o({type:"SET_TARGET_PRICE_ROWS",payload:r}),setTimeout(async()=>{let e=l.SessionManager.getInstance(),o=e.getCurrentSessionId();o&&(await e.saveSession(o,t.config,r,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus),console.log("✅ Session saved immediately after setting target prices"))},100),setTimeout(async()=>{try{await k(),console.log("✅ Session auto-saved after setting target prices")}catch(e){console.error("❌ Failed to auto-save session after setting target prices:",e)}},100)},[t.targetPriceRows,t.config.baseBid,o,k]);(0,s.useEffect)(()=>{let e=d.getInstance().getStatus().isOnline;if("Running"!==t.botSystemStatus||0===t.targetPriceRows.length||t.currentMarketPrice<=0||!e){e||"Running"!==t.botSystemStatus||console.log("\uD83D\uDD34 Trading paused - network offline");return}let{config:r,currentMarketPrice:s,targetPriceRows:a,crypto1Balance:c,crypto2Balance:l}=t,u=[...a].sort((e,t)=>e.targetPrice-t.targetPrice),p=c,g=l,y=0;console.log(`🚀 CONTINUOUS TRADING: Price $${s.toFixed(2)} | Targets: ${u.length} | Balance: $${g} ${r.crypto2}`);let h=u.filter(e=>Math.abs(s-e.targetPrice)/s*100<=r.slippagePercent);h.length>0&&console.log(`🎯 TARGETS IN RANGE (\xb1${r.slippagePercent}%):`,h.map(e=>`Counter ${e.counter} (${e.status})`));for(let e=0;e<u.length;e++){let a=u[e];if(Math.abs(s-a.targetPrice)/s*100<=r.slippagePercent){if("SimpleSpot"===r.tradingMode){if("Free"===a.status){let e=a.valueLevel;if(g>=e){let t=e/s;o({type:"UPDATE_TARGET_PRICE_ROW",payload:{...a,status:"Full",orderLevel:a.orderLevel+1,valueLevel:r.baseBid*Math.pow(r.multiplier,a.orderLevel+1),crypto1AmountHeld:t,originalCostCrypto2:e,crypto1Var:t,crypto2Var:-e}}),o({type:"UPDATE_BALANCES",payload:{crypto1:p+t,crypto2:g-e}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto1}/${r.crypto2}`,crypto1:r.crypto1,orderType:"BUY",amountCrypto1:t,avgPrice:s,valueCrypto2:e,price1:s,crypto1Symbol:r.crypto1||"",crypto2Symbol:r.crypto2||""}}),console.log(`✅ BUY: Counter ${a.counter} bought ${t.toFixed(6)} ${r.crypto1} at $${s.toFixed(2)}`),n({title:"BUY Executed",description:`Counter ${a.counter}: ${t.toFixed(6)} ${r.crypto1}`,duration:2e3}),S("soundOrderExecution"),m(`🟢 <b>BUY EXECUTED</b>
📊 Counter: ${a.counter}
💰 Amount: ${t.toFixed(6)} ${r.crypto1}
💵 Price: $${s.toFixed(2)}
💸 Cost: $${e.toFixed(2)} ${r.crypto2}
📈 Mode: Simple Spot`),y++,g-=e,p+=t}else console.log(`❌ Insufficient ${r.crypto2} balance: ${g.toFixed(6)} < ${e.toFixed(6)}`),w(r.crypto2,g,e).catch(console.error)}let e=a.counter,t=u.find(t=>t.counter===e-1);if(t&&"Full"===t.status&&t.crypto1AmountHeld&&t.originalCostCrypto2){let a=t.crypto1AmountHeld,c=a*s,l=c-t.originalCostCrypto2,d=s>0?l*r.incomeSplitCrypto1Percent/100/s:0;o({type:"UPDATE_TARGET_PRICE_ROW",payload:{...t,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:r.baseBid*Math.pow(r.multiplier,t.orderLevel),crypto1Var:-a,crypto2Var:c}}),o({type:"UPDATE_BALANCES",payload:{crypto1:p-a,crypto2:g+c}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto1}/${r.crypto2}`,crypto1:r.crypto1,orderType:"SELL",amountCrypto1:a,avgPrice:s,valueCrypto2:c,price1:s,crypto1Symbol:r.crypto1||"",crypto2Symbol:r.crypto2||"",realizedProfitLossCrypto2:l,realizedProfitLossCrypto1:d}}),console.log(`✅ SELL: Counter ${e-1} sold ${a.toFixed(6)} ${r.crypto1}. Profit: $${l.toFixed(2)}`),n({title:"SELL Executed",description:`Counter ${e-1}: Profit $${l.toFixed(2)}`,duration:2e3}),S("soundOrderExecution");let u=l>0?"\uD83D\uDCC8":l<0?"\uD83D\uDCC9":"➖";m(`🔴 <b>SELL EXECUTED</b>
📊 Counter: ${e-1}
💰 Amount: ${a.toFixed(6)} ${r.crypto1}
💵 Price: $${s.toFixed(2)}
💸 Received: $${c.toFixed(2)} ${r.crypto2}
${u} Profit: $${l.toFixed(2)} ${r.crypto2}
📈 Mode: Simple Spot`),y++,p-=a,g+=c}}else if("StablecoinSwap"===r.tradingMode){if("Free"===a.status){let e=a.valueLevel;if(g>=e){let s=v(r.crypto2||"USDT"),c=r.preferredStablecoin||"USDT",l=v(c),d=s/l,u=e*d,h=v(r.crypto1||"BTC"),f=h*(t.currentMarketPrice/(h/s)),b=f/l,w=u/b,A=a.orderLevel+1,P=r.baseBid*Math.pow(r.multiplier,A),T={...a,status:"Full",orderLevel:A,valueLevel:P,crypto1AmountHeld:w,originalCostCrypto2:e,crypto1Var:w,crypto2Var:-e};console.log(`💰 StablecoinSwap BUY - Setting originalCostCrypto2:`,{counter:a.counter,amountCrypto2ToUse:e,crypto1Bought:w,originalCostCrypto2:e,newLevel:A,crypto1StablecoinPrice:b,fluctuatedCrypto1Price:f,currentMarketPrice:t.currentMarketPrice}),o({type:"UPDATE_TARGET_PRICE_ROW",payload:T}),o({type:"UPDATE_BALANCES",payload:{crypto1:p+w,crypto2:g-e}});let C=v(r.crypto2)/v(c),$=e*(d-C),E=$/b;console.log(`📊 StablecoinSwap Step A SELL P/L:`,{crypto2:r.crypto2,amountSold:e,currentPrice:d,basePrice:C,estimatedProfitCrypto2:$,estimatedProfitCrypto1:E}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto2}/${c}`,crypto1:r.crypto2,orderType:"SELL",amountCrypto1:e,avgPrice:d,valueCrypto2:u,price1:d,crypto1Symbol:r.crypto2||"",crypto2Symbol:c,realizedProfitLossCrypto2:$,realizedProfitLossCrypto1:E}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto1}/${c}`,crypto1:r.crypto1,orderType:"BUY",amountCrypto1:w,avgPrice:b,valueCrypto2:u,price1:b,crypto1Symbol:r.crypto1||"",crypto2Symbol:c}}),console.log(`✅ STABLECOIN BUY: Counter ${a.counter} | Step 1: Sold ${e} ${r.crypto2} → ${u.toFixed(2)} ${c} | Step 2: Bought ${w.toFixed(6)} ${r.crypto1} | Level: ${a.orderLevel} → ${A}`),n({title:"BUY Executed (Stablecoin)",description:`Counter ${a.counter}: ${w.toFixed(6)} ${r.crypto1} via ${c}`,duration:2e3}),S("soundOrderExecution"),m(`🟢 <b>BUY EXECUTED (Stablecoin Swap)</b>
📊 Counter: ${a.counter}
🔄 Step 1: Sold ${e.toFixed(2)} ${r.crypto2} → ${u.toFixed(2)} ${c}
🔄 Step 2: Bought ${w.toFixed(6)} ${r.crypto1}
📊 Level: ${a.orderLevel} → ${A}
📈 Mode: Stablecoin Swap`),y++,g-=e,p+=w}else console.log(`❌ Insufficient ${r.crypto2} balance for stablecoin swap: ${g.toFixed(6)} < ${e.toFixed(6)}`),w(r.crypto2,g,e).catch(console.error)}let e=a.counter,s=u.find(t=>t.counter===e-1);if(console.log(`🔍 StablecoinSwap SELL Check:`,{currentCounter:e,inferiorRowFound:!!s,inferiorRowStatus:s?.status,inferiorRowCrypto1Held:s?.crypto1AmountHeld,inferiorRowOriginalCost:s?.originalCostCrypto2,canExecuteSell:!!(s&&"Full"===s.status&&s.crypto1AmountHeld&&s.originalCostCrypto2)}),s&&"Full"===s.status&&s.crypto1AmountHeld&&s.originalCostCrypto2){let a=s.crypto1AmountHeld,c=v(r.crypto1||"BTC"),l=v(r.crypto2||"USDT"),d=r.preferredStablecoin||"USDT",u=v(d),h=c*(t.currentMarketPrice/(c/l))/u,f=a*h,b=l/u,w=f/b,A=s.originalCostCrypto2||0,P=w-A,T=h>0?P/h:0;0===P&&console.warn(`⚠️ P/L is exactly 0 - this might indicate an issue:
                - Are prices changing between BUY and SELL?
                - Is the market price fluctuation working?
                - Current market price: ${t.currentMarketPrice}`),console.log(`💰 StablecoinSwap P/L Calculation DETAILED:
              - Inferior Row Counter: ${s.counter}
              - Original Cost (Crypto2): ${A}
              - Crypto2 Reacquired: ${w}
              - Realized P/L (Crypto2): ${P}
              - Crypto1 Stablecoin Price: ${h}
              - Realized P/L (Crypto1): ${T}
              - Is Profitable: ${P>0?"YES":"NO"}
              - Calculation: ${w} - ${A} = ${P}`),o({type:"UPDATE_TARGET_PRICE_ROW",payload:{...s,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:r.baseBid*Math.pow(r.multiplier,s.orderLevel),crypto1Var:0,crypto2Var:0}}),o({type:"UPDATE_BALANCES",payload:{crypto1:p-a,crypto2:g+w}}),console.log(`📊 Adding StablecoinSwap SELL order to history with P/L:`,{realizedProfitLossCrypto2:P,realizedProfitLossCrypto1:T,pair:`${r.crypto1}/${d}`,orderType:"SELL",amountCrypto1:a,avgPrice:h,valueCrypto2:f,price1:h,crypto1Symbol:r.crypto1,crypto2Symbol:d}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto1}/${d}`,crypto1:r.crypto1,orderType:"SELL",amountCrypto1:a,avgPrice:h,valueCrypto2:f,price1:h,crypto1Symbol:r.crypto1||"",crypto2Symbol:d,realizedProfitLossCrypto2:P,realizedProfitLossCrypto1:T}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto2}/${d}`,crypto1:r.crypto2,orderType:"BUY",amountCrypto1:w,avgPrice:b,valueCrypto2:f,price1:b,crypto1Symbol:r.crypto2||"",crypto2Symbol:d}}),console.log(`✅ STABLECOIN SELL: Counter ${e-1} | Step A: Sold ${a.toFixed(6)} ${r.crypto1} → ${f.toFixed(2)} ${d} | Step B: Bought ${w.toFixed(2)} ${r.crypto2} | Profit: ${P.toFixed(2)} ${r.crypto2} | Level: ${s.orderLevel} (unchanged)`),n({title:"SELL Executed (Stablecoin)",description:`Counter ${e-1}: Profit ${P.toFixed(2)} ${r.crypto2} via ${d}`,duration:2e3}),S("soundOrderExecution");let C=P>0?"\uD83D\uDCC8":P<0?"\uD83D\uDCC9":"➖";m(`🔴 <b>SELL EXECUTED (Stablecoin Swap)</b>
📊 Counter: ${e-1}
🔄 Step A: Sold ${a.toFixed(6)} ${r.crypto1} → ${f.toFixed(2)} ${d}
🔄 Step B: Bought ${w.toFixed(2)} ${r.crypto2}
${C} Profit: ${P.toFixed(2)} ${r.crypto2}
📊 Level: ${s.orderLevel} (unchanged)
📈 Mode: Stablecoin Swap`),y++,p-=a,g+=w}}}}y>0&&console.log(`🎯 CYCLE COMPLETE: ${y} actions taken at price $${s.toFixed(2)}`)},[t.botSystemStatus,t.currentMarketPrice,t.targetPriceRows,t.config,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,o,n,S,m]);let R=(0,s.useCallback)(()=>t.targetPriceRows&&Array.isArray(t.targetPriceRows)?t.targetPriceRows.map(e=>{let o,r;let s=t.currentMarketPrice||0,n=e.targetPrice||0;if("Full"===e.status&&e.crypto1AmountHeld&&e.originalCostCrypto2){let n=s*e.crypto1AmountHeld-e.originalCostCrypto2;r=n*t.config.incomeSplitCrypto2Percent/100,s>0&&(o=n*t.config.incomeSplitCrypto1Percent/100/s)}return{...e,currentPrice:s,priceDifference:n-s,priceDifferencePercent:s>0?(n-s)/s*100:0,potentialProfitCrypto1:t.config.incomeSplitCrypto1Percent/100*e.valueLevel/(n||1),potentialProfitCrypto2:t.config.incomeSplitCrypto2Percent/100*e.valueLevel,percentFromActualPrice:s&&n?(s/n-1)*100:0,incomeCrypto1:o,incomeCrypto2:r}}).sort((e,t)=>t.targetPrice-e.targetPrice):[],[t.targetPriceRows,t.currentMarketPrice,t.config.incomeSplitCrypto1Percent,t.config.incomeSplitCrypto2Percent,t.config.baseBid,t.config.multiplier]),x=(0,s.useCallback)(async e=>{try{let o={name:`${e.crypto1}/${e.crypto2} ${e.tradingMode}`,tradingMode:e.tradingMode,crypto1:e.crypto1,crypto2:e.crypto2,baseBid:e.baseBid,multiplier:e.multiplier,numDigits:e.numDigits,slippagePercent:e.slippagePercent,incomeSplitCrypto1Percent:e.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:e.incomeSplitCrypto2Percent,preferredStablecoin:e.preferredStablecoin,targetPrices:t.targetPriceRows.map(e=>e.targetPrice)},r=await c.oc.saveConfig(o);return console.log("✅ Config saved to backend:",r),r.config?.id||null}catch(e){return console.error("❌ Failed to save config to backend:",e),n({title:"Backend Error",description:"Failed to save configuration to backend",variant:"destructive",duration:3e3}),null}},[t.targetPriceRows,n]),D=(0,s.useCallback)(async e=>{try{let t=await c.oc.startBot(e);return console.log("✅ Bot started on backend:",t),n({title:"Bot Started",description:"Trading bot started successfully on backend",duration:3e3}),!0}catch(e){return console.error("❌ Failed to start bot on backend:",e),n({title:"Backend Error",description:"Failed to start bot on backend",variant:"destructive",duration:3e3}),!1}},[n]),L=(0,s.useCallback)(async e=>{try{let t=await c.oc.stopBot(e);return console.log("✅ Bot stopped on backend:",t),n({title:"Bot Stopped",description:"Trading bot stopped successfully on backend",duration:3e3}),!0}catch(e){return console.error("❌ Failed to stop bot on backend:",e),n({title:"Backend Error",description:"Failed to stop bot on backend",variant:"destructive",duration:3e3}),!1}},[n]),M=(0,s.useCallback)(async()=>{let e="http://localhost:5000";if(!e){console.error("Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed."),o({type:"SET_BACKEND_STATUS",payload:"offline"});return}try{let t=await fetch(`${e}/health/`);if(!t.ok){console.error(`Backend health check failed with status: ${t.status} ${t.statusText}`);let e=await t.text().catch(()=>"Could not read response text.");console.error("Backend health check response body:",e)}o({type:"SET_BACKEND_STATUS",payload:t.ok?"online":"offline"})}catch(t){o({type:"SET_BACKEND_STATUS",payload:"offline"}),console.error("Backend connectivity check failed. Error details:",t),t.cause&&console.error("Fetch error cause:",t.cause),console.error("Attempted to fetch API URL:",`${e}/health/`)}},[o]);(0,s.useEffect)(()=>{M()},[M]),(0,s.useEffect)(()=>{C(t)},[t]),(0,s.useEffect)(()=>{"Running"===t.botSystemStatus&&0===t.targetPriceRows.length&&(console.log("⚠️ Bot is running but no target prices set - stopping bot"),o({type:"SYSTEM_STOP_BOT"}))},[t.botSystemStatus,t.targetPriceRows.length,o]),(0,s.useEffect)(()=>{"WarmingUp"===t.botSystemStatus&&(console.log("Bot is Warming Up... Immediate execution enabled."),o({type:"SYSTEM_COMPLETE_WARMUP"}),console.log("Bot is now Running immediately."))},[t.botSystemStatus,o]),(0,s.useEffect)(()=>{if("Running"===t.botSystemStatus){let e=l.SessionManager.getInstance(),o=e.getCurrentSessionId();!o&&t.config.crypto1&&t.config.crypto2?(console.log("\uD83D\uDD04 Creating session for running bot..."),e.createNewSessionWithAutoName(t.config).then(async o=>{e.setCurrentSession(o),await e.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,!0),console.log("✅ Session created and saved for running bot:",o),window.dispatchEvent(new StorageEvent("storage",{key:"pluto_active_sessions",newValue:Date.now().toString(),storageArea:localStorage}))}).catch(e=>{console.error("❌ Failed to create session for running bot:",e)})):o&&(console.log("\uD83D\uDCBE Marking session as active and saving state for running bot..."),e.setCurrentSession(o),e.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,!0).then(()=>{console.log("✅ Session saved and marked as active for running bot:",o),window.dispatchEvent(new StorageEvent("storage",{key:"pluto_active_sessions",newValue:Date.now().toString(),storageArea:localStorage}))}).catch(e=>{console.error("❌ Failed to save session for running bot:",e)}))}},[t.botSystemStatus,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance]),(0,s.useEffect)(()=>{let e=l.SessionManager.getInstance(),o=e.getCurrentSessionId();o&&("Running"===t.botSystemStatus?(e.startSessionRuntime(o),console.log("✅ Started runtime tracking for session:",o)):"Stopped"===t.botSystemStatus&&(e.stopSessionRuntime(o),console.log("⏹️ Stopped runtime tracking for session:",o)))},[t.botSystemStatus]),(0,s.useEffect)(()=>{let e=l.SessionManager.getInstance();"Running"===t.botSystemStatus&&!e.getCurrentSessionId()&&t.config.crypto1&&t.config.crypto2&&t.targetPriceRows.length>0&&e.createNewSessionWithAutoName(t.config).then(t=>{e.setCurrentSession(t),console.log("✅ Auto-created session when bot started:",t)}).catch(e=>{console.error("❌ Failed to auto-create session:",e)});let o=e.getCurrentSessionId();o&&("Running"===t.botSystemStatus?(e.startSessionRuntime(o),console.log("⏱️ Started runtime tracking for session:",o)):"Stopped"===t.botSystemStatus&&(e.stopSessionRuntime(o),console.log("⏹️ Stopped runtime tracking for session:",o)))},[t.botSystemStatus,t.config.crypto1,t.config.crypto2]),(0,s.useEffect)(()=>{let e=l.SessionManager.getInstance(),r=e.getCurrentSessionId();if(r){let s=e.loadSession(r);if(s&&(s.config.crypto1!==t.config.crypto1||s.config.crypto2!==t.config.crypto2)){if(console.log("\uD83D\uDD04 Crypto pair changed during session, auto-saving and resetting..."),"Running"===t.botSystemStatus||t.targetPriceRows.length>0||t.orderHistory.length>0){"Running"===t.botSystemStatus&&L(r);let o=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),n=`${s.name} (AutoSaved ${o})`;e.createNewSession(n,s.config).then(async o=>{await e.saveSession(o,s.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,!1),console.log("\uD83D\uDCBE AutoSaved session:",n)})}if(o({type:"RESET_FOR_NEW_CRYPTO"}),t.config.crypto1&&t.config.crypto2&&"Running"===t.botSystemStatus){let o={crypto1:t.crypto1Balance,crypto2:t.crypto2Balance,stablecoin:t.stablecoinBalance};e.createNewSessionWithAutoName(t.config,void 0,o).then(r=>{e.setCurrentSession(r),console.log("\uD83C\uDD95 Created new session for crypto pair:",t.config.crypto1,"/",t.config.crypto2,"with balances:",o),n({title:"Crypto Pair Changed",description:`Previous session AutoSaved. New session created for ${t.config.crypto1}/${t.config.crypto2}`,duration:5e3})})}else e.clearCurrentSession(),console.log("\uD83D\uDD04 Crypto pair changed but bot wasn't running - cleared current session")}}},[t.config.crypto1,t.config.crypto2]),(0,s.useEffect)(()=>{let e=d.getInstance(),r=u.getInstance(),s=p.getInstance(),i=l.SessionManager.getInstance(),a=e.addListener((e,r)=>{if(console.log(`🌐 Network status changed: ${e?"Online":"Offline"}`),e||r)e&&!r&&n({title:"Network Reconnected",description:"Connection restored. You can resume trading.",duration:3e3});else{if("Running"===t.botSystemStatus){console.log("\uD83D\uDD34 Internet lost - stopping bot and saving session"),o({type:"SYSTEM_STOP_BOT"}),b("Internet Connection Lost","Trading bot has been automatically stopped due to internet connection loss. Session has been saved and will resume when connection is restored.").catch(console.error);let e=l.SessionManager.getInstance(),r=e.getCurrentSessionId();if(r){let o=e.loadSession(r);if(o){let r=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),s=`${o.name} (Offline Backup ${r})`;e.createNewSession(s,o.config).then(async o=>{await e.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,!1),console.log("\uD83D\uDCBE Created offline backup session:",s)})}}}n({title:"Network Disconnected",description:"Bot stopped and session saved. Trading paused until connection restored.",variant:"destructive",duration:8e3})}}),c=s.addListener(e=>{let t=e.usedJSHeapSize/1024/1024;t>150&&(console.warn(`🧠 High memory usage: ${t.toFixed(2)}MB`),n({title:"High Memory Usage",description:`Memory usage is high (${t.toFixed(0)}MB). Consider refreshing the page.`,variant:"destructive",duration:5e3}))}),g=async()=>{try{let e=i.getCurrentSessionId();if(e){if(i.loadSession(e))try{await i.saveSession(e,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus)}catch(e){console.warn("⚠️ Session save failed, will retry on next auto-save:",e)}else console.warn("⚠️ Current session ID exists but session not found, clearing session ID"),i.clearCurrentSession()}C(t)}catch(e){console.warn("Auto-save failed, will retry on next cycle:",e)}},y="Running"===t.botSystemStatus?15e3:6e4;r.enable(g,y),console.log(`⏰ Auto-save enabled with ${y/1e3}s interval (bot status: ${t.botSystemStatus})`);let h=e=>{console.log("\uD83D\uDEAA Browser closing/refreshing - saving session state"),localStorage.setItem("pluto_last_app_close",Date.now().toString());let o=i.getCurrentSessionId(),r=o?i.loadSession(o):null;if(g(),"Running"===t.botSystemStatus&&r){console.log("\uD83D\uDED1 Bot is running - auto-saving to past sessions before close");try{let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),s=`${r.name} (AutoSaved ${e})`,n=i.createNewSession(s,r.config);i.saveSession(n,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,!1),i.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,!1),console.log("✅ Auto-saved running session to past sessions:",s)}catch(e){console.error("❌ Failed to auto-save running session:",e)}let s="Trading bot is currently running and will be stopped. Progress has been auto-saved.";return e.returnValue=s,s}};return window.addEventListener("beforeunload",h),()=>{a(),c(),r.disable(),window.removeEventListener("beforeunload",h)}},[t,n]),(0,s.useEffect)(()=>{u.getInstance().saveNow();let e=l.SessionManager.getInstance(),o=e.getCurrentSessionId();if(o){let r="Running"===t.botSystemStatus;console.log(`🔄 Bot status changed to ${t.botSystemStatus}, updating session active state:`,{sessionId:o,isActive:r}),e.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,r).then(()=>{window.dispatchEvent(new StorageEvent("storage",{key:"pluto_active_sessions",newValue:Date.now().toString(),storageArea:localStorage}))}).catch(e=>{console.error("❌ Failed to update session active state:",e)})}},[t.botSystemStatus,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance]);let _={...t,dispatch:o,setTargetPrices:B,getDisplayOrders:R,checkBackendStatus:M,fetchMarketPrice:h,startBackendBot:D,stopBackendBot:L,saveConfigToBackend:x,saveCurrentSession:k,backendStatus:t.backendStatus,botSystemStatus:t.botSystemStatus,isBotActive:"Running"===t.botSystemStatus};return(0,r.jsx)(I.Provider,{value:_,children:e})},B=()=>{let e=(0,s.useContext)(I);if(void 0===e)throw Error("useTradingContext must be used within a TradingProvider");return e}},79737:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>r});let r=(0,o(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\components\\ui\\toaster.tsx","Toaster")},86514:(e,t,o)=>{Promise.resolve().then(o.bind(o,79737)),Promise.resolve().then(o.bind(o,26443)),Promise.resolve().then(o.bind(o,29131)),Promise.resolve().then(o.bind(o,47506))},94431:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>u,metadata:()=>d});var r=o(37413),s=o(7653),n=o.n(s);o(61135);var i=o(79737),a=o(29131),c=o(47506),l=o(26443);let d={title:"Pluto Trading Bot",description:"Simulated cryptocurrency trading bot with Neo Brutalist UI."};function u({children:e}){return(0,r.jsx)("html",{lang:"en",className:n().variable,suppressHydrationWarning:!0,children:(0,r.jsxs)("body",{className:"font-sans antialiased",suppressHydrationWarning:!0,children:[" ",(0,r.jsx)(a.AuthProvider,{children:(0,r.jsx)(c.TradingProvider,{children:(0,r.jsxs)(l.AIProvider,{children:[e,(0,r.jsx)(i.Toaster,{})]})})})]})})}}};