"use strict";(()=>{var e={};e.id=492,e.ids=[492],e.modules={643:e=>{e.exports=require("node:perf_hooks")},1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{e.exports=require("node:buffer")},7252:e=>{e.exports=require("express")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},14985:e=>{e.exports=require("dns")},16141:e=>{e.exports=require("node:zlib")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19185:e=>{e.exports=require("dgram")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37067:e=>{e.exports=require("node:http")},37830:e=>{e.exports=require("node:stream/web")},44708:e=>{e.exports=require("node:https")},48020:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>a,pages:()=>x,routeModule:()=>q,tree:()=>d});var o=t(65239),s=t(48088),i=t(88170),n=t.n(i),p=t(30893),u={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>p[e]);t.d(r,u);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,x=[],a={require:t,loadChunk:()=>Promise.resolve()},q=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54379:e=>{e.exports=require("node:path")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73136:e=>{e.exports=require("node:url")},73496:e=>{e.exports=require("http2")},73566:e=>{e.exports=require("worker_threads")},74075:e=>{e.exports=require("zlib")},74998:e=>{e.exports=require("perf_hooks")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},79748:e=>{e.exports=require("fs/promises")},81630:e=>{e.exports=require("http")},84297:e=>{e.exports=require("async_hooks")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[585,973],()=>t(48020));module.exports=o})();