"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[26],{518:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(157).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},663:(e,t,n)=>{n.d(t,{UC:()=>t8,YJ:()=>t4,In:()=>t3,q7:()=>nt,VF:()=>nr,p4:()=>nn,JU:()=>ne,ZL:()=>t7,bL:()=>t2,wn:()=>ni,PP:()=>no,wv:()=>nl,l9:()=>t5,WT:()=>t6,LM:()=>t9});var r=n(2115),o=n(7650),i=n(9367),l=n(5185),a=n(2284),u=n(6101),c=n(6081),s=n(4315),d=n(9178),f=n(2293),p=n(7900),h=n(1285);let v=["top","right","bottom","left"],m=Math.min,g=Math.max,y=Math.round,w=Math.floor,x=e=>({x:e,y:e}),b={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function E(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function A(e){return"x"===e?"y":"x"}function T(e){return"y"===e?"height":"width"}function k(e){return["top","bottom"].includes(C(e))?"y":"x"}function L(e){return e.replace(/start|end/g,e=>S[e])}function P(e){return e.replace(/left|right|bottom|top/g,e=>b[e])}function N(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function j(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function M(e,t,n){let r,{reference:o,floating:i}=e,l=k(t),a=A(k(t)),u=T(a),c=C(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(R(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let O=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=M(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=M(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function D(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=E(t,e),h=N(p),v=a[f?"floating"===d?"reference":"floating":d],m=j(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=j(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-x.top+h.top)/w.y,bottom:(x.bottom-m.bottom+h.bottom)/w.y,left:(m.left-x.left+h.left)/w.x,right:(x.right-m.right+h.right)/w.x}}function I(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function H(e){return v.some(t=>e[t]>=0)}async function F(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=C(n),a=R(n),u="y"===k(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,d=E(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function W(){return"undefined"!=typeof window}function B(e){return z(e)?(e.nodeName||"").toLowerCase():"#document"}function _(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function V(e){var t;return null==(t=(z(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function z(e){return!!W()&&(e instanceof Node||e instanceof _(e).Node)}function K(e){return!!W()&&(e instanceof Element||e instanceof _(e).Element)}function G(e){return!!W()&&(e instanceof HTMLElement||e instanceof _(e).HTMLElement)}function q(e){return!!W()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof _(e).ShadowRoot)}function X(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=J(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Y(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function U(e){let t=Z(),n=K(e)?J(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function $(e){return["html","body","#document"].includes(B(e))}function J(e){return _(e).getComputedStyle(e)}function Q(e){return K(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if("html"===B(e))return e;let t=e.assignedSlot||e.parentNode||q(e)&&e.host||V(e);return q(t)?t.host:t}function et(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ee(t);return $(n)?t.ownerDocument?t.ownerDocument.body:t.body:G(n)&&X(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=_(o);if(i){let e=en(l);return t.concat(l,l.visualViewport||[],X(o)?o:[],e&&n?et(e):[])}return t.concat(o,et(o,[],n))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function er(e){let t=J(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=G(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=y(n)!==i||y(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eo(e){return K(e)?e:e.contextElement}function ei(e){let t=eo(e);if(!G(t))return x(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=er(t),l=(i?y(n.width):n.width)/r,a=(i?y(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let el=x(0);function ea(e){let t=_(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function eu(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eo(e),a=x(1);t&&(r?K(r)&&(a=ei(r)):a=ei(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===_(l))&&o)?ea(l):x(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=_(l),t=r&&K(r)?_(r):r,n=e,o=en(n);for(;o&&r&&t!==n;){let e=ei(o),t=o.getBoundingClientRect(),r=J(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=en(n=_(o))}}return j({width:d,height:f,x:c,y:s})}function ec(e,t){let n=Q(e).scrollLeft;return t?t.left+n:eu(V(e)).left+n}function es(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ec(e,r)),y:r.top+t.scrollTop}}function ed(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=_(e),r=V(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=Z();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=V(e),n=Q(e),r=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=g(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ec(e),a=-n.scrollTop;return"rtl"===J(r).direction&&(l+=g(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(V(e));else if(K(t))r=function(e,t){let n=eu(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=G(e)?ei(e):x(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=ea(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return j(r)}function ef(e){return"static"===J(e).position}function ep(e,t){if(!G(e)||"fixed"===J(e).position)return null;if(t)return t(e);let n=e.offsetParent;return V(e)===n&&(n=n.ownerDocument.body),n}function eh(e,t){let n=_(e);if(Y(e))return n;if(!G(e)){let t=ee(e);for(;t&&!$(t);){if(K(t)&&!ef(t))return t;t=ee(t)}return n}let r=ep(e,t);for(;r&&["table","td","th"].includes(B(r))&&ef(r);)r=ep(r,t);return r&&$(r)&&ef(r)&&!U(r)?n:r||function(e){let t=ee(e);for(;G(t)&&!$(t);){if(U(t))return t;if(Y(t))break;t=ee(t)}return null}(e)||n}let ev=async function(e){let t=this.getOffsetParent||eh,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=G(t),o=V(t),i="fixed"===n,l=eu(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=x(0);if(r||!r&&!i){if(("body"!==B(t)||X(o))&&(a=Q(t)),r){let e=eu(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ec(o))}let c=!o||r||i?x(0):es(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},em={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=V(r),a=!!t&&Y(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=x(1),s=x(0),d=G(r);if((d||!d&&!i)&&(("body"!==B(r)||X(l))&&(u=Q(r)),G(r))){let e=eu(r);c=ei(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?x(0):es(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:V,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?Y(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=et(e,[],!1).filter(e=>K(e)&&"body"!==B(e)),o=null,i="fixed"===J(e).position,l=i?ee(e):e;for(;K(l)&&!$(l);){let t=J(l),n=U(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||X(l)&&!n&&function e(t,n){let r=ee(t);return!(r===n||!K(r)||$(r))&&("fixed"===J(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ee(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=ed(t,n,o);return e.top=g(r.top,e.top),e.right=m(r.right,e.right),e.bottom=m(r.bottom,e.bottom),e.left=g(r.left,e.left),e},ed(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eh,getElementRects:ev,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=er(e);return{width:t,height:n}},getScale:ei,isElement:K,isRTL:function(e){return"rtl"===J(e).direction}};function eg(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ey=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=E(e,t)||{};if(null==c)return{};let d=N(s),f={x:n,y:r},p=A(k(o)),h=T(p),v=await l.getDimensions(c),y="y"===p,w=y?"clientHeight":"clientWidth",x=i.reference[h]+i.reference[p]-f[p]-i.floating[h],b=f[p]-i.reference[p],S=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),C=S?S[w]:0;C&&await (null==l.isElement?void 0:l.isElement(S))||(C=a.floating[w]||i.floating[h]);let L=C/2-v[h]/2-1,P=m(d[y?"top":"left"],L),j=m(d[y?"bottom":"right"],L),M=C-v[h]-j,O=C/2-v[h]/2+(x/2-b/2),D=g(P,m(O,M)),I=!u.arrow&&null!=R(o)&&O!==D&&i.reference[h]/2-(O<P?P:j)-v[h]/2<0,H=I?O<P?O-P:O-M:0;return{[p]:f[p]+H,data:{[p]:D,centerOffset:O-D-H,...I&&{alignmentOffset:H}},reset:I}}}),ew=(e,t,n)=>{let r=new Map,o={platform:em,...n},i={...o.platform,_c:r};return O(e,t,{...o,platform:i})};var ex="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function eb(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eb(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eb(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eE(e,t){let n=eS(e);return Math.round(t*n)/n}function eC(e){let t=r.useRef(e);return ex(()=>{t.current=e}),t}let eR=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ey({element:n.current,padding:r}).fn(t):{}:n?ey({element:n,padding:r}).fn(t):{}}}),eA=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await F(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=E(e,t),c={x:n,y:r},s=await D(t,u),d=k(C(o)),f=A(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=g(n,m(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=g(n,m(h,r))}let v=a.fn({...t,[f]:p,[d]:h});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=E(e,t),s={x:n,y:r},d=k(o),f=A(d),p=s[f],h=s[d],v=E(a,t),m="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(C(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=E(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=C(a),b=k(s),S=C(s)===s,N=await (null==d.isRTL?void 0:d.isRTL(f.floating)),j=v||(S||!y?[P(s)]:function(e){let t=P(e);return[L(e),t,L(t)]}(s)),M="none"!==g;!v&&M&&j.push(...function(e,t,n,r){let o=R(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(C(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(L)))),i}(s,y,g,N));let O=[s,...j],I=await D(t,w),H=[],F=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&H.push(I[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=R(e),o=A(k(e)),i=T(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=P(l)),[l,P(l)]}(a,c,N);H.push(I[e[0]],I[e[1]])}if(F=[...F,{placement:a,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=O[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=F.filter(e=>{if(M){let t=k(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=E(e,t),f=await D(t,d),p=C(l),h=R(l),v="y"===k(l),{width:y,height:w}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let x=w-f.top-f.bottom,b=y-f.left-f.right,S=m(w-f[o],x),A=m(y-f[i],b),T=!t.middlewareData.shift,L=S,P=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(L=x),T&&!h){let e=g(f.left,0),t=g(f.right,0),n=g(f.top,0),r=g(f.bottom,0);v?P=y-2*(0!==e||0!==t?e+t:g(f.left,f.right)):L=w-2*(0!==n||0!==r?n+r:g(f.top,f.bottom))}await s({...t,availableWidth:P,availableHeight:L});let N=await u.getDimensions(c.floating);return y!==N.width||w!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=E(e,t);switch(r){case"referenceHidden":{let e=I(await D(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:H(e)}}}case"escaped":{let e=I(await D(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:H(e)}}}default:return{}}}}}(e),options:[e,t]}),ej=(e,t)=>({...eR(e),options:[e,t]});var eM=n(3655),eO=n(5155),eD=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eO.jsx)(eM.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eO.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eD.displayName="Arrow";var eI=n(9033),eH=n(2712),eF=n(1275),eW="Popper",[eB,e_]=(0,c.A)(eW),[eV,ez]=eB(eW),eK=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eO.jsx)(eV,{scope:t,anchor:o,onAnchorChange:i,children:n})};eK.displayName=eW;var eG="PopperAnchor",eq=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=ez(eG,n),a=r.useRef(null),c=(0,u.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eO.jsx)(eM.sG.div,{...i,ref:c})});eq.displayName=eG;var eX="PopperContent",[eY,eU]=eB(eX),eZ=r.forwardRef((e,t)=>{var n,i,l,a,c,s,d,f;let{__scopePopper:p,side:h="bottom",sideOffset:v=0,align:y="center",alignOffset:x=0,arrowPadding:b=0,avoidCollisions:S=!0,collisionBoundary:E=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:T="optimized",onPlaced:k,...L}=e,P=ez(eX,p),[N,j]=r.useState(null),M=(0,u.s)(t,e=>j(e)),[O,D]=r.useState(null),I=(0,eF.X)(O),H=null!==(d=null==I?void 0:I.width)&&void 0!==d?d:0,F=null!==(f=null==I?void 0:I.height)&&void 0!==f?f:0,W="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},B=Array.isArray(E)?E:[E],_=B.length>0,z={padding:W,boundary:B.filter(e0),altBoundary:_},{refs:K,floatingStyles:G,placement:q,isPositioned:X,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,v]=r.useState(i);eb(h,i)||v(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),S=a||m,E=u||y,C=r.useRef(null),R=r.useRef(null),A=r.useRef(f),T=null!=s,k=eC(s),L=eC(l),P=eC(d),N=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};L.current&&(e.platform=L.current),ew(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};j.current&&!eb(A.current,t)&&(A.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,L,P]);ex(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let j=r.useRef(!1);ex(()=>(j.current=!0,()=>{j.current=!1}),[]),ex(()=>{if(S&&(C.current=S),E&&(R.current=E),S&&E){if(k.current)return k.current(S,E,N);N()}},[S,E,N,k,T]);let M=r.useMemo(()=>({reference:C,floating:R,setReference:x,setFloating:b}),[x,b]),O=r.useMemo(()=>({reference:S,floating:E}),[S,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eE(O.floating,f.x),r=eE(O.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...eS(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,O.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:N,refs:M,elements:O,floatingStyles:D}),[f,N,M,O,D])}({strategy:"fixed",placement:h+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eo(e),d=i||l?[...s?et(s):[],...et(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=V(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=w(d),v=w(o.clientWidth-(s+f)),y={rootMargin:-h+"px "+-v+"px "+-w(o.clientHeight-(d+p))+"px "+-w(s)+"px",threshold:g(0,m(1,u))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==u){if(!x)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eg(c,e.getBoundingClientRect())||l(),x=!1}try{r=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let v=c?eu(e):null;return c&&function t(){let r=eu(e);v&&!eg(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===T})},elements:{reference:P.anchor},middleware:[eA({mainAxis:v+F,alignmentAxis:x}),S&&eT({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ek():void 0,...z}),S&&eL({...z}),eP({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),O&&ej({element:O,padding:b}),e1({arrowWidth:H,arrowHeight:F}),A&&eN({strategy:"referenceHidden",...z})]}),[U,Z]=e2(q),$=(0,eI.c)(k);(0,eH.N)(()=>{X&&(null==$||$())},[X,$]);let J=null===(n=Y.arrow)||void 0===n?void 0:n.x,Q=null===(i=Y.arrow)||void 0===i?void 0:i.y,ee=(null===(l=Y.arrow)||void 0===l?void 0:l.centerOffset)!==0,[en,er]=r.useState();return(0,eH.N)(()=>{N&&er(window.getComputedStyle(N).zIndex)},[N]),(0,eO.jsx)("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:X?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(a=Y.transformOrigin)||void 0===a?void 0:a.x,null===(c=Y.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(s=Y.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eO.jsx)(eY,{scope:p,placedSide:U,onArrowChange:D,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,eO.jsx)(eM.sG.div,{"data-side":U,"data-align":Z,...L,ref:M,style:{...L.style,animation:X?void 0:"none"}})})})});eZ.displayName=eX;var e$="PopperArrow",eJ={top:"bottom",right:"left",bottom:"top",left:"right"},eQ=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eU(e$,n),i=eJ[o.placedSide];return(0,eO.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eO.jsx)(eD,{...r,ref:t,style:{...r.style,display:"block"}})})});function e0(e){return null!==e}eQ.displayName=e$;var e1=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=e2(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function e2(e){let[t,n="center"]=e.split("-");return[t,n]}var e5=n(4378),e6=n(9708),e3=n(5845),e7=n(5503),e8=n(2564),e9=n(8168),e4=n(3795),te=[" ","Enter","ArrowUp","ArrowDown"],tt=[" ","Enter"],tn="Select",[tr,to,ti]=(0,a.N)(tn),[tl,ta]=(0,c.A)(tn,[ti,e_]),tu=e_(),[tc,ts]=tl(tn),[td,tf]=tl(tn),tp=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:v,required:m,form:g}=e,y=tu(t),[w,x]=r.useState(null),[b,S]=r.useState(null),[E,C]=r.useState(!1),R=(0,s.jH)(d),[A=!1,T]=(0,e3.i)({prop:o,defaultProp:i,onChange:l}),[k,L]=(0,e3.i)({prop:a,defaultProp:u,onChange:c}),P=r.useRef(null),N=!w||g||!!w.closest("form"),[j,M]=r.useState(new Set),O=Array.from(j).map(e=>e.props.value).join(";");return(0,eO.jsx)(eK,{...y,children:(0,eO.jsxs)(tc,{required:m,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:S,valueNodeHasChildren:E,onValueNodeHasChildrenChange:C,contentId:(0,h.B)(),value:k,onValueChange:L,open:A,onOpenChange:T,dir:R,triggerPointerDownPosRef:P,disabled:v,children:[(0,eO.jsx)(tr.Provider,{scope:t,children:(0,eO.jsx)(td,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{M(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,eO.jsxs)(tQ,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:k,onChange:e=>L(e.target.value),disabled:v,form:g,children:[void 0===k?(0,eO.jsx)("option",{value:""}):null,Array.from(j)]},O):null]})})};tp.displayName=tn;var th="SelectTrigger",tv=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=tu(n),c=ts(th,n),s=c.disabled||o,d=(0,u.s)(t,c.onTriggerChange),f=to(n),p=r.useRef("touch"),[h,v,m]=t0(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=t1(t,e,n);void 0!==r&&c.onValueChange(r.value)}),g=e=>{s||(c.onOpenChange(!0),m()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eO.jsx)(eq,{asChild:!0,...a,children:(0,eO.jsx)(eM.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":tJ(c.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&te.includes(e.key)&&(g(),e.preventDefault())})})})});tv.displayName=th;var tm="SelectValue",tg=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,c=ts(tm,n),{onValueNodeHasChildrenChange:s}=c,d=void 0!==i,f=(0,u.s)(t,c.onValueNodeChange);return(0,eH.N)(()=>{s(d)},[s,d]),(0,eO.jsx)(eM.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:tJ(c.value)?(0,eO.jsx)(eO.Fragment,{children:l}):i})});tg.displayName=tm;var ty=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,eO.jsx)(eM.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});ty.displayName="SelectIcon";var tw=e=>(0,eO.jsx)(e5.Z,{asChild:!0,...e});tw.displayName="SelectPortal";var tx="SelectContent",tb=r.forwardRef((e,t)=>{let n=ts(tx,e.__scopeSelect),[i,l]=r.useState();return((0,eH.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,eO.jsx)(tC,{...e,ref:t}):i?o.createPortal((0,eO.jsx)(tS,{scope:e.__scopeSelect,children:(0,eO.jsx)(tr.Slot,{scope:e.__scopeSelect,children:(0,eO.jsx)("div",{children:e.children})})}),i):null});tb.displayName=tx;var[tS,tE]=tl(tx),tC=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:s,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S,...E}=e,C=ts(tx,n),[R,A]=r.useState(null),[T,k]=r.useState(null),L=(0,u.s)(t,e=>A(e)),[P,N]=r.useState(null),[j,M]=r.useState(null),O=to(n),[D,I]=r.useState(!1),H=r.useRef(!1);r.useEffect(()=>{if(R)return(0,e9.Eq)(R)},[R]),(0,f.Oh)();let F=r.useCallback(e=>{let[t,...n]=O().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[O,T]),W=r.useCallback(()=>F([P,R]),[F,P,R]);r.useEffect(()=>{D&&W()},[D,W]);let{onOpenChange:B,triggerPointerDownPosRef:_}=C;r.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=_.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=_.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||B(!1),document.removeEventListener("pointermove",t),_.current=null};return null!==_.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[R,B,_]),r.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[V,z]=t0(e=>{let t=O().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=t1(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==C.value&&C.value===t||r)&&(N(e),r&&(H.current=!0))},[C.value]),G=r.useCallback(()=>null==R?void 0:R.focus(),[R]),q=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==C.value&&C.value===t||r)&&M(e)},[C.value]),X="popper"===o?tA:tR,Y=X===tA?{side:s,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,eO.jsx)(tS,{scope:n,content:R,viewport:T,onViewportChange:k,itemRefCallback:K,selectedItem:P,onItemLeave:G,itemTextRefCallback:q,focusSelectedItem:W,selectedItemText:j,position:o,isPositioned:D,searchRef:V,children:(0,eO.jsx)(e4.A,{as:e6.DX,allowPinchZoom:!0,children:(0,eO.jsx)(p.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{var t;null===(t=C.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eO.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,eO.jsx)(X,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...E,...Y,onPlaced:()=>I(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,l.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});tC.displayName="SelectContentImpl";var tR=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=ts(tx,n),c=tE(tx,n),[s,d]=r.useState(null),[f,p]=r.useState(null),h=(0,u.s)(t,e=>p(e)),v=to(n),m=r.useRef(!1),g=r.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:x,focusSelectedItem:b}=c,S=r.useCallback(()=>{if(a.trigger&&a.valueNode&&s&&f&&y&&w&&x){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,c=Math.max(u,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);s.style.minWidth=u+"px",s.style.left=f+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,c=Math.max(u,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);s.style.minWidth=u+"px",s.style.right=f+"px"}let l=v(),u=window.innerHeight-20,c=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),b=p+h+c+parseInt(d.paddingBottom,10)+g,S=Math.min(5*w.offsetHeight,b),E=window.getComputedStyle(y),C=parseInt(E.paddingTop,10),R=parseInt(E.paddingBottom,10),A=e.top+e.height/2-10,T=w.offsetHeight/2,k=p+h+(w.offsetTop+T);if(k<=A){let e=l.length>0&&w===l[l.length-1].ref.current;s.style.bottom="0px";let t=Math.max(u-A,T+(e?R:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+g);s.style.height=k+t+"px"}else{let e=l.length>0&&w===l[0].ref.current;s.style.top="0px";let t=Math.max(A,p+y.offsetTop+(e?C:0)+T);s.style.height=t+(b-k)+"px",y.scrollTop=k-A+y.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=S+"px",s.style.maxHeight=u+"px",null==o||o(),requestAnimationFrame(()=>m.current=!0)}},[v,a.trigger,a.valueNode,s,f,y,w,x,a.dir,o]);(0,eH.N)(()=>S(),[S]);let[E,C]=r.useState();(0,eH.N)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let R=r.useCallback(e=>{e&&!0===g.current&&(S(),null==b||b(),g.current=!1)},[S,b]);return(0,eO.jsx)(tT,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,eO.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,eO.jsx)(eM.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tR.displayName="SelectItemAlignedPosition";var tA=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=tu(n);return(0,eO.jsx)(eZ,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tA.displayName="SelectPopperPosition";var[tT,tk]=tl(tx,{}),tL="SelectViewport",tP=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=tE(tL,n),c=tk(tL,n),s=(0,u.s)(t,a.onViewportChange),d=r.useRef(0);return(0,eO.jsxs)(eO.Fragment,{children:[(0,eO.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eO.jsx)(tr.Slot,{scope:n,children:(0,eO.jsx)(eM.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tP.displayName=tL;var tN="SelectGroup",[tj,tM]=tl(tN),tO=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,eO.jsx)(tj,{scope:n,id:o,children:(0,eO.jsx)(eM.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tO.displayName=tN;var tD="SelectLabel",tI=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tM(tD,n);return(0,eO.jsx)(eM.sG.div,{id:o.id,...r,ref:t})});tI.displayName=tD;var tH="SelectItem",[tF,tW]=tl(tH),tB=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...c}=e,s=ts(tH,n),d=tE(tH,n),f=s.value===o,[p,v]=r.useState(null!=a?a:""),[m,g]=r.useState(!1),y=(0,u.s)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,o,i)}),w=(0,h.B)(),x=r.useRef("touch"),b=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eO.jsx)(tF,{scope:n,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:r.useCallback(e=>{v(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,eO.jsx)(tr.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,eO.jsx)(eM.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:y,onFocus:(0,l.m)(c.onFocus,()=>g(!0)),onBlur:(0,l.m)(c.onBlur,()=>g(!1)),onClick:(0,l.m)(c.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,l.m)(c.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,l.m)(c.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,l.m)(c.onPointerMove,e=>{if(x.current=e.pointerType,i){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,l.m)(c.onKeyDown,e=>{var t;((null===(t=d.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(tt.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});tB.displayName=tH;var t_="SelectItemText",tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,c=ts(t_,n),s=tE(t_,n),d=tW(t_,n),f=tf(t_,n),[p,h]=r.useState(null),v=(0,u.s)(t,e=>h(e),d.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,d.value,d.disabled)}),m=null==p?void 0:p.textContent,g=r.useMemo(()=>(0,eO.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=f;return(0,eH.N)(()=>(y(g),()=>w(g)),[y,w,g]),(0,eO.jsxs)(eO.Fragment,{children:[(0,eO.jsx)(eM.sG.span,{id:d.textId,...a,ref:v}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});tV.displayName=t_;var tz="SelectItemIndicator",tK=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return tW(tz,n).isSelected?(0,eO.jsx)(eM.sG.span,{"aria-hidden":!0,...r,ref:t}):null});tK.displayName=tz;var tG="SelectScrollUpButton",tq=r.forwardRef((e,t)=>{let n=tE(tG,e.__scopeSelect),o=tk(tG,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,eH.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eO.jsx)(tU,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tq.displayName=tG;var tX="SelectScrollDownButton",tY=r.forwardRef((e,t)=>{let n=tE(tX,e.__scopeSelect),o=tk(tX,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,eH.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eO.jsx)(tU,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tY.displayName=tX;var tU=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=tE("SelectScrollButton",n),u=r.useRef(null),c=to(n),s=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,eH.N)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[c]),(0,eO.jsx)(eM.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{s()})})}),tZ=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eO.jsx)(eM.sG.div,{"aria-hidden":!0,...r,ref:t})});tZ.displayName="SelectSeparator";var t$="SelectArrow";function tJ(e){return""===e||void 0===e}r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tu(n),i=ts(t$,n),l=tE(t$,n);return i.open&&"popper"===l.position?(0,eO.jsx)(eQ,{...o,...r,ref:t}):null}).displayName=t$;var tQ=r.forwardRef((e,t)=>{let{value:n,...o}=e,i=r.useRef(null),l=(0,u.s)(t,i),a=(0,e7.Z)(n);return r.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[a,n]),(0,eO.jsx)(e8.s,{asChild:!0,children:(0,eO.jsx)("select",{...o,ref:l,defaultValue:n})})});function t0(e){let t=(0,eI.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function t1(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}tQ.displayName="BubbleSelect";var t2=tp,t5=tv,t6=tg,t3=ty,t7=tw,t8=tb,t9=tP,t4=tO,ne=tI,nt=tB,nn=tV,nr=tK,no=tq,ni=tY,nl=tZ},1275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(2115),o=n(2712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:l()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},3795:(e,t,n)=>{n.d(t,{A:()=>G});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(2115)),a="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,l=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(o)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=o({async:!0,ssr:!1},e),l}(),h=function(){},v=l.forwardRef(function(e,t){var n,r,a,u,f=l.useRef(null),v=l.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=v[0],g=v[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,S=e.enabled,E=e.shards,C=e.sideCar,R=e.noIsolation,A=e.inert,T=e.allowPinchZoom,k=e.as,L=e.gapMode,P=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,u=a.facade,s(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(u,n)},[n]),u),j=o(o({},P),m);return l.createElement(l.Fragment,null,S&&l.createElement(C,{sideCar:p,removeScrollBar:b,shards:E,noIsolation:R,inert:A,setCallbacks:g,allowPinchZoom:!!T,lockRef:f,gapMode:L}),y?l.cloneElement(l.Children.only(w),o(o({},j),{ref:N})):l.createElement(void 0===k?"div":k,o({},j,{className:x,ref:N}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:u,zeroRight:a};var m=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,o({},n))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=w(),R="data-scroll-locked",A=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},k=function(){l.useEffect(function(){return document.body.setAttribute(R,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;k();var i=l.useMemo(function(){return E(o)},[o]);return l.createElement(C,{styles:A(i,!t,o,n?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){P=!1}var j=!!P&&{passive:!1},M=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},O=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),D(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},D=function(e,t){return"v"===e?M(t,"overflowY"):M(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},H=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{var h=I(e,u),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&D(e,u)&&(f+=m,p+=v),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},W=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},_=0,V=[];let z=(p.useMedium(function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(_++)[0],i=l.useState(w)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=F(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=O(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=O(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return H(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(V.length&&V[V.length-1]===i){var n="deltaY"in e?W(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,W(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return V.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){V=V.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var h=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(L,{gapMode:e.gapMode}):null)}),m);var K=l.forwardRef(function(e,t){return l.createElement(v,o({},e,{ref:t,sideCar:z}))});K.classNames=v.classNames;let G=K},5503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(2115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},7381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(157).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7648:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),i=n(3655),l=n(9033),a=n(5155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,l.c)(m),S=(0,l.c)(g),E=r.useRef(null),C=(0,o.s)(t,e=>x(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,R.paused]),r.useEffect(()=>{if(w){v.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,S),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,S),v.remove(R)},0)}}},[w,b,S,R]);let A=r.useCallback(e=>{if(!n&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:A})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null===(n=(e=m(e,t))[0])||void 0===n||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],d=[],f=new Set,p=new Set(c),h=function(e){!(!e||f.has(e))&&(f.add(e),h(e.parentNode))};c.forEach(h);var v=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),d.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,n,"aria-hidden")):function(){return null}}},9367:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},9556:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(157).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}}]);