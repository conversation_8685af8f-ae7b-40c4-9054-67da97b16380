"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[870],{955:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},1133:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3349:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},5318:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5452:(e,t,r)=>{r.d(t,{UC:()=>et,VY:()=>en,ZL:()=>$,bL:()=>Y,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>Q});var n=r(2115),o=r(5185),a=r(6101),l=r(6081),i=r(1285),s=r(5845),d=r(9178),c=r(7900),u=r(4378),p=r(8905),f=r(3655),h=r(2293),y=r(3795),v=r(8168),g=r(9708),m=r(5155),k="Dialog",[x,b]=(0,l.A)(k),[w,C]=x(k),D=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p=!1,f]=(0,s.i)({prop:o,defaultProp:a,onChange:l});return(0,m.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};D.displayName=k;var j="DialogTrigger",A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=C(j,r),i=(0,a.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});A.displayName=j;var R="DialogPortal",[E,I]=x(R,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=C(R,t);return(0,m.jsx)(E,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,m.jsx)(p.C,{present:r||l.open,children:(0,m.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};M.displayName=R;var N="DialogOverlay",O=n.forwardRef((e,t)=>{let r=I(N,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(N,e.__scopeDialog);return a.modal?(0,m.jsx)(p.C,{present:n||a.open,children:(0,m.jsx)(P,{...o,ref:t})}):null});O.displayName=N;var P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(N,r);return(0,m.jsx)(y.A,{as:g.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":W(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),_="DialogContent",F=n.forwardRef((e,t)=>{let r=I(_,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(_,e.__scopeDialog);return(0,m.jsx)(p.C,{present:n||a.open,children:a.modal?(0,m.jsx)(q,{...o,ref:t}):(0,m.jsx)(G,{...o,ref:t})})});F.displayName=_;var q=n.forwardRef((e,t)=>{let r=C(_,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,m.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=n.forwardRef((e,t)=>{let r=C(_,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,m.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=C(_,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,m.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":W(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(U,{titleId:u.titleId}),(0,m.jsx)(J,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(B,r);return(0,m.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});S.displayName=B;var T="DialogDescription",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(T,r);return(0,m.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});H.displayName=T;var z="DialogClose",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(z,r);return(0,m.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function W(e){return e?"open":"closed"}V.displayName=z;var X="DialogTitleWarning",[Z,K]=(0,l.q)(X,{contentName:_,titleName:B,docsSlug:"dialog"}),U=e=>{let{titleId:t}=e,r=K(X),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},Y=D,Q=A,$=M,ee=O,et=F,er=S,en=H,eo=V},6981:(e,t,r)=>{r.d(t,{C1:()=>j,bL:()=>D});var n=r(2115),o=r(6101),a=r(6081),l=r(5185),i=r(5845),s=r(5503),d=r(1275),c=r(8905),u=r(3655),p=r(5155),f="Checkbox",[h,y]=(0,a.A)(f),[v,g]=h(f),m=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:s,defaultChecked:d,required:c,disabled:f,value:h="on",onCheckedChange:y,form:g,...m}=e,[k,x]=n.useState(null),D=(0,o.s)(t,e=>x(e)),j=n.useRef(!1),A=!k||g||!!k.closest("form"),[R=!1,E]=(0,i.i)({prop:s,defaultProp:d,onChange:y}),I=n.useRef(R);return n.useEffect(()=>{let e=null==k?void 0:k.form;if(e){let t=()=>E(I.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[k,E]),(0,p.jsxs)(v,{scope:r,state:R,disabled:f,children:[(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":w(R)?"mixed":R,"aria-required":c,"data-state":C(R),"data-disabled":f?"":void 0,disabled:f,value:h,...m,ref:D,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(e.onClick,e=>{E(e=>!!w(e)||!e),A&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),A&&(0,p.jsx)(b,{control:k,bubbles:!j.current,name:a,value:h,checked:R,required:c,disabled:f,form:g,style:{transform:"translateX(-100%)"},defaultChecked:!w(d)&&d})]})});m.displayName=f;var k="CheckboxIndicator",x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=g(k,r);return(0,p.jsx)(c.C,{present:n||w(a.state)||!0===a.state,children:(0,p.jsx)(u.sG.span,{"data-state":C(a.state),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});x.displayName=k;var b=e=>{let{control:t,checked:r,bubbles:o=!0,defaultChecked:a,...l}=e,i=n.useRef(null),c=(0,s.Z)(r),u=(0,d.X)(t);n.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==r&&t){let n=new Event("click",{bubbles:o});e.indeterminate=w(r),t.call(e,!w(r)&&r),e.dispatchEvent(n)}},[c,r,o]);let f=n.useRef(!w(r)&&r);return(0,p.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=a?a:f.current,...l,tabIndex:-1,ref:i,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return"indeterminate"===e}function C(e){return w(e)?"indeterminate":e?"checked":"unchecked"}var D=m,j=x},7082:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},7489:(e,t,r)=>{r.d(t,{b:()=>d});var n=r(2115),o=r(3655),a=r(5155),l="horizontal",i=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=l,...d}=e,c=(r=s,i.includes(r))?s:l;return(0,a.jsx)(o.sG.div,{"data-orientation":c,...n?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:t})});s.displayName="Separator";var d=s},8186:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},8531:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},8803:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},9532:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(157).A)("SquarePlus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])}}]);